#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库配置模块

该模块提供数据库配置管理功能，支持在开发环境使用SQLite和生产环境使用PostgreSQL。
"""

import os
from dotenv import load_dotenv
from utils.logger import logger

# 加载环境变量
load_dotenv()

# 数据库类型常量
DB_TYPE_SQLITE = "sqlite"
DB_TYPE_POSTGRES = "postgres"

class DBConfig:
    """数据库配置类"""
    
    @staticmethod
    def get_db_type():
        """
        获取当前配置的数据库类型
        
        Returns:
            str: 数据库类型，"sqlite"或"postgres"
        """
        # 从环境变量获取数据库类型，默认为sqlite
        db_type = os.getenv("DB_TYPE", DB_TYPE_SQLITE).lower()
        
        # 验证数据库类型
        if db_type not in [DB_TYPE_SQLITE, DB_TYPE_POSTGRES]:
            logger.warning(f"未知的数据库类型: {db_type}，使用默认类型: {DB_TYPE_SQLITE}")
            db_type = DB_TYPE_SQLITE
            
        return db_type
    
    @staticmethod
    def is_production():
        """
        检查当前是否为生产环境
        
        Returns:
            bool: 如果是生产环境返回True，否则返回False
        """
        env = os.getenv("ENVIRONMENT", "development").lower()
        return env == "production"
    
    @staticmethod
    def get_sqlite_config():
        """
        获取SQLite数据库配置
        
        Returns:
            dict: SQLite配置字典
        """
        # 获取数据库文件路径，默认为项目根目录下的sqlite.db
        db_path = os.getenv("SQLITE_DB_PATH", "sqlite.db")
        
        # 确保路径是绝对路径
        if not os.path.isabs(db_path):
            # 获取项目根目录
            root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            db_path = os.path.join(root_dir, db_path)
            
        return {
            "db_path": db_path
        }
    
    @staticmethod
    def get_postgres_config():
        """
        获取PostgreSQL数据库配置
        
        Returns:
            dict: PostgreSQL配置字典
        """
        return {
            "host": os.getenv("POSTGRES_HOST", "localhost"),
            "port": int(os.getenv("POSTGRES_PORT", 5432)),
            "database": os.getenv("POSTGRES_DB", "postgres"),
            "user": os.getenv("POSTGRES_USERNAME", "postgres"),
            "password": os.getenv("POSTGRES_PASSWORD", "")
        }
    
    @staticmethod
    def get_db_config():
        """
        获取当前数据库配置
        
        Returns:
            tuple: (db_type, config_dict)，数据库类型和配置字典
        """
        db_type = DBConfig.get_db_type()
        
        # 如果是生产环境，强制使用PostgreSQL
        if DBConfig.is_production():
            db_type = DB_TYPE_POSTGRES
            logger.info("生产环境，强制使用PostgreSQL数据库")
        
        if db_type == DB_TYPE_SQLITE:
            config = DBConfig.get_sqlite_config()
            logger.info(f"使用SQLite数据库: {config['db_path']}")
        else:
            config = DBConfig.get_postgres_config()
            logger.info(f"使用PostgreSQL数据库: {config['host']}:{config['port']}/{config['database']}")
            
        return db_type, config
