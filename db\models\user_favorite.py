#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户自选股模型
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
from rest_api.models import Base

class UserFavorite(Base):
    """用户自选股模型"""
    __tablename__ = "user_favorites"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    symbol = Column(String(20), nullable=False)  # 股票/期货代码
    name = Column(String(50), nullable=False)    # 股票/期货名称
    type = Column(String(10), nullable=False)    # 类型：stock(股票) 或 future(期货)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联用户
    user = relationship("User", back_populates="favorites")

    # 添加唯一约束，确保用户不会重复添加同一个自选股
    __table_args__ = (
        UniqueConstraint('user_id', 'symbol', name='uix_user_symbol'),
    )

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "symbol": self.symbol,
            "name": self.name,
            "type": self.type,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
