"""
News sentiment analysis for the AI Hedge Fund module.

This file provides functions for analyzing news sentiment data.
"""

import os
import re
import json
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import io
import base64
import random

from ..data.cache import get_cache


class NewsArticle:
    """Class representing a news article."""
    
    def __init__(
        self,
        title: str,
        url: str,
        source: str,
        published_date: str,
        sentiment_score: float,
        relevance_score: float,
        summary: Optional[str] = None,
        categories: Optional[List[str]] = None,
        entities: Optional[List[Dict[str, Any]]] = None,
    ):
        """
        Initialize news article.
        
        Args:
            title: Article title
            url: Article URL
            source: News source
            published_date: Publication date (YYYY-MM-DD)
            sentiment_score: Sentiment score (-1.0 to 1.0)
            relevance_score: Relevance score (0.0 to 1.0)
            summary: Article summary
            categories: List of article categories
            entities: List of entities mentioned in the article
        """
        self.title = title
        self.url = url
        self.source = source
        self.published_date = published_date
        self.sentiment_score = sentiment_score
        self.relevance_score = relevance_score
        self.summary = summary
        self.categories = categories or []
        self.entities = entities or []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "title": self.title,
            "url": self.url,
            "source": self.source,
            "published_date": self.published_date,
            "sentiment_score": self.sentiment_score,
            "relevance_score": self.relevance_score,
            "summary": self.summary,
            "categories": self.categories,
            "entities": self.entities,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "NewsArticle":
        """Create from dictionary."""
        return cls(**data)


class NewsSentiment:
    """Class representing news sentiment data for a company."""
    
    def __init__(
        self,
        ticker: str,
        date: str,
        sentiment_score: float,
        sentiment_magnitude: float,
        article_count: int,
        positive_count: int,
        negative_count: int,
        neutral_count: int,
        top_articles: List[NewsArticle],
        trending_topics: List[str],
        source: str = "mock",
    ):
        """
        Initialize news sentiment data.
        
        Args:
            ticker: Stock ticker symbol
            date: Date of the sentiment data (YYYY-MM-DD)
            sentiment_score: Overall sentiment score (-1.0 to 1.0)
            sentiment_magnitude: Magnitude of sentiment (0.0 to +inf)
            article_count: Total number of articles
            positive_count: Number of positive articles
            negative_count: Number of negative articles
            neutral_count: Number of neutral articles
            top_articles: List of top articles
            trending_topics: List of trending topics
            source: Source of the data
        """
        self.ticker = ticker
        self.date = date
        self.sentiment_score = sentiment_score
        self.sentiment_magnitude = sentiment_magnitude
        self.article_count = article_count
        self.positive_count = positive_count
        self.negative_count = negative_count
        self.neutral_count = neutral_count
        self.top_articles = top_articles
        self.trending_topics = trending_topics
        self.source = source
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "ticker": self.ticker,
            "date": self.date,
            "sentiment_score": self.sentiment_score,
            "sentiment_magnitude": self.sentiment_magnitude,
            "article_count": self.article_count,
            "positive_count": self.positive_count,
            "negative_count": self.negative_count,
            "neutral_count": self.neutral_count,
            "top_articles": [article.to_dict() for article in self.top_articles],
            "trending_topics": self.trending_topics,
            "source": self.source,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "NewsSentiment":
        """Create from dictionary."""
        # Convert top_articles from dict to NewsArticle objects
        top_articles = [NewsArticle.from_dict(article) for article in data.get("top_articles", [])]
        
        # Create a new dict without top_articles
        new_data = {k: v for k, v in data.items() if k != "top_articles"}
        
        # Add top_articles as NewsArticle objects
        new_data["top_articles"] = top_articles
        
        return cls(**new_data)


def get_news_sentiment(ticker: str, date: str) -> Optional[NewsSentiment]:
    """
    Fetch news sentiment data for a company.
    
    Args:
        ticker: Stock ticker symbol
        date: Date (YYYY-MM-DD)
        
    Returns:
        NewsSentiment object or None if data not available
    """
    # Get cache instance
    cache = get_cache()
    
    # Check cache first
    if cached_data := cache.get_news_sentiment(ticker):
        # Find the closest date
        closest_date = None
        min_diff = float("inf")
        
        target_date = datetime.strptime(date, "%Y-%m-%d")
        
        for item in cached_data:
            item_date = datetime.strptime(item["date"], "%Y-%m-%d")
            diff = abs((target_date - item_date).days)
            
            if diff < min_diff:
                min_diff = diff
                closest_date = item
        
        # Use cached data if it's within 7 days
        if closest_date and min_diff <= 7:
            return NewsSentiment.from_dict(closest_date)
    
    # If not in cache or no recent data, fetch from API
    headers = {}
    if api_key := os.environ.get("ALTERNATIVE_DATA_API_KEY"):
        headers["X-API-KEY"] = api_key
    
    url = f"https://api.alternativedata.io/news-sentiment/?ticker={ticker}&date={date}"
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        data = response.json()
        
        if "data" in data and data["data"]:
            sentiment_data = NewsSentiment.from_dict(data["data"])
            
            # Cache the results
            if cached_data:
                cached_data.append(sentiment_data.to_dict())
            else:
                cached_data = [sentiment_data.to_dict()]
            
            cache.set_news_sentiment(ticker, cached_data)
            
            return sentiment_data
        else:
            # Fallback to mock data if API returns empty data
            return generate_mock_news_sentiment(ticker, date)
    except Exception as e:
        print(f"Error fetching news sentiment data for {ticker}: {e}")
        
        # Fallback to mock data if API call fails
        return generate_mock_news_sentiment(ticker, date)


def generate_mock_news_sentiment(ticker: str, date: str) -> NewsSentiment:
    """
    Generate mock news sentiment data for testing.
    
    Args:
        ticker: Stock ticker symbol
        date: Date (YYYY-MM-DD)
        
    Returns:
        NewsSentiment object with mock data
    """
    # Seed random number generator with ticker and date for consistency
    random.seed(sum(ord(c) for c in ticker) + sum(ord(c) for c in date))
    
    # Generate mock sentiment score (-1.0 to 1.0)
    sentiment_score = random.uniform(-1.0, 1.0)
    
    # Generate mock sentiment magnitude (0.0 to 5.0)
    sentiment_magnitude = random.uniform(0.0, 5.0)
    
    # Generate mock article counts
    article_count = random.randint(5, 50)
    
    # Determine positive/negative/neutral counts based on sentiment score
    if sentiment_score > 0.3:
        # Positive sentiment
        positive_pct = random.uniform(0.5, 0.8)
        negative_pct = random.uniform(0.05, 0.2)
    elif sentiment_score < -0.3:
        # Negative sentiment
        positive_pct = random.uniform(0.05, 0.2)
        negative_pct = random.uniform(0.5, 0.8)
    else:
        # Neutral sentiment
        positive_pct = random.uniform(0.2, 0.4)
        negative_pct = random.uniform(0.2, 0.4)
    
    neutral_pct = 1.0 - positive_pct - negative_pct
    
    positive_count = int(article_count * positive_pct)
    negative_count = int(article_count * negative_pct)
    neutral_count = article_count - positive_count - negative_count
    
    # Generate mock top articles
    news_sources = [
        "Bloomberg",
        "CNBC",
        "Reuters",
        "Wall Street Journal",
        "Financial Times",
        "MarketWatch",
        "Seeking Alpha",
        "Barron's",
        "Yahoo Finance",
        "Motley Fool",
    ]
    
    article_titles = [
        f"{ticker} Reports Strong Quarterly Earnings",
        f"{ticker} Announces New Product Launch",
        f"{ticker} CEO Discusses Future Growth Plans",
        f"Analysts Upgrade {ticker} Stock",
        f"{ticker} Expands into New Markets",
        f"Investors React to {ticker}'s Latest Announcement",
        f"{ticker} Faces Increased Competition",
        f"Regulatory Challenges for {ticker}",
        f"{ticker} Stock Rises on Positive News",
        f"{ticker} Stock Falls Amid Market Concerns",
    ]
    
    categories = [
        "Earnings",
        "Product",
        "Management",
        "Analyst",
        "Expansion",
        "Investor",
        "Competition",
        "Regulatory",
        "Stock Movement",
        "Market Trend",
    ]
    
    # Generate top articles
    num_articles = min(article_count, 5)
    top_articles = []
    
    for i in range(num_articles):
        # Generate article sentiment based on overall sentiment
        if i < positive_count:
            article_sentiment = random.uniform(0.3, 1.0)
        elif i < positive_count + negative_count:
            article_sentiment = random.uniform(-1.0, -0.3)
        else:
            article_sentiment = random.uniform(-0.3, 0.3)
        
        # Generate article
        article = NewsArticle(
            title=random.choice(article_titles),
            url=f"https://example.com/news/{ticker.lower()}/{random.randint(1000, 9999)}",
            source=random.choice(news_sources),
            published_date=date,
            sentiment_score=article_sentiment,
            relevance_score=random.uniform(0.5, 1.0),
            summary=f"This is a mock summary for {ticker} news article.",
            categories=[random.choice(categories)],
            entities=[
                {
                    "name": ticker,
                    "type": "Organization",
                    "sentiment": article_sentiment,
                }
            ],
        )
        
        top_articles.append(article)
    
    # Generate mock trending topics
    topics = [
        "earnings",
        "growth",
        "valuation",
        "dividend",
        "buyback",
        "CEO",
        "product",
        "competition",
        "regulation",
        "lawsuit",
        "innovation",
        "partnership",
        "acquisition",
        "debt",
        "analyst",
    ]
    
    num_topics = random.randint(3, 7)
    trending_topics = random.sample(topics, num_topics)
    
    return NewsSentiment(
        ticker=ticker,
        date=date,
        sentiment_score=sentiment_score,
        sentiment_magnitude=sentiment_magnitude,
        article_count=article_count,
        positive_count=positive_count,
        negative_count=negative_count,
        neutral_count=neutral_count,
        top_articles=top_articles,
        trending_topics=trending_topics,
        source="mock",
    )


def get_historical_news_sentiment(
    ticker: str,
    start_date: str,
    end_date: str,
    interval: str = "daily",
) -> pd.DataFrame:
    """
    Get historical news sentiment data for a company.
    
    Args:
        ticker: Stock ticker symbol
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
        interval: Data interval ("daily" or "weekly")
        
    Returns:
        DataFrame with historical sentiment data
    """
    # Parse dates
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate date range
    if interval == "daily":
        dates = [start + timedelta(days=i) for i in range((end - start).days + 1)]
    else:  # weekly
        dates = []
        current = start
        while current <= end:
            dates.append(current)
            current += timedelta(days=7)
    
    # Generate sentiment data for each date
    data = []
    for date in dates:
        date_str = date.strftime("%Y-%m-%d")
        sentiment = get_news_sentiment(ticker, date_str)
        
        if sentiment:
            data.append({
                "date": date_str,
                "sentiment_score": sentiment.sentiment_score,
                "sentiment_magnitude": sentiment.sentiment_magnitude,
                "article_count": sentiment.article_count,
                "positive_count": sentiment.positive_count,
                "negative_count": sentiment.negative_count,
                "neutral_count": sentiment.neutral_count,
            })
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Set date as index
    if not df.empty:
        df["date"] = pd.to_datetime(df["date"])
        df.set_index("date", inplace=True)
    
    return df


def analyze_news_trends(
    ticker: str,
    start_date: str,
    end_date: str,
    interval: str = "daily",
) -> Dict[str, Any]:
    """
    Analyze news sentiment trends for a company.
    
    Args:
        ticker: Stock ticker symbol
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
        interval: Data interval ("daily" or "weekly")
        
    Returns:
        Dictionary with analysis results
    """
    # Get historical sentiment data
    sentiment_data = get_historical_news_sentiment(ticker, start_date, end_date, interval)
    
    if sentiment_data.empty:
        return {
            "status": "error",
            "message": f"No news sentiment data available for {ticker}",
        }
    
    # Calculate average sentiment
    avg_sentiment = sentiment_data["sentiment_score"].mean()
    
    # Calculate sentiment trend
    if len(sentiment_data) >= 2:
        first_half = sentiment_data.iloc[:len(sentiment_data)//2]["sentiment_score"].mean()
        second_half = sentiment_data.iloc[len(sentiment_data)//2:]["sentiment_score"].mean()
        sentiment_trend = second_half - first_half
    else:
        sentiment_trend = 0.0
    
    # Calculate article count trend
    if len(sentiment_data) >= 2:
        first_half_count = sentiment_data.iloc[:len(sentiment_data)//2]["article_count"].mean()
        second_half_count = sentiment_data.iloc[len(sentiment_data)//2:]["article_count"].mean()
        article_count_trend = second_half_count - first_half_count
    else:
        article_count_trend = 0.0
    
    # Calculate sentiment volatility
    sentiment_volatility = sentiment_data["sentiment_score"].std()
    
    # Determine overall trend
    if sentiment_trend > 0.2:
        trend_description = "strongly improving"
    elif sentiment_trend > 0.05:
        trend_description = "improving"
    elif sentiment_trend < -0.2:
        trend_description = "strongly deteriorating"
    elif sentiment_trend < -0.05:
        trend_description = "deteriorating"
    else:
        trend_description = "stable"
    
    # Determine news volume trend
    if article_count_trend > 5:
        volume_description = "significantly increasing"
    elif article_count_trend > 1:
        volume_description = "increasing"
    elif article_count_trend < -5:
        volume_description = "significantly decreasing"
    elif article_count_trend < -1:
        volume_description = "decreasing"
    else:
        volume_description = "stable"
    
    # Generate summary
    summary = (
        f"News sentiment for {ticker} is {trend_description} with an average sentiment score of {avg_sentiment:.2f}. "
        f"News volume is {volume_description} with an average of {sentiment_data['article_count'].mean():.1f} articles per day. "
        f"Sentiment volatility is {sentiment_volatility:.2f}."
    )
    
    # Generate signal
    if avg_sentiment > 0.3 and sentiment_trend > 0.05:
        signal = "bullish"
        confidence = min(80.0, 50.0 + avg_sentiment * 50.0 + sentiment_trend * 100.0)
    elif avg_sentiment < -0.3 and sentiment_trend < -0.05:
        signal = "bearish"
        confidence = min(80.0, 50.0 + abs(avg_sentiment) * 50.0 + abs(sentiment_trend) * 100.0)
    elif avg_sentiment > 0.1:
        signal = "slightly bullish"
        confidence = min(60.0, 40.0 + avg_sentiment * 50.0)
    elif avg_sentiment < -0.1:
        signal = "slightly bearish"
        confidence = min(60.0, 40.0 + abs(avg_sentiment) * 50.0)
    else:
        signal = "neutral"
        confidence = 50.0 - abs(avg_sentiment) * 50.0
    
    # Return results
    return {
        "status": "success",
        "ticker": ticker,
        "start_date": start_date,
        "end_date": end_date,
        "interval": interval,
        "avg_sentiment": float(avg_sentiment),
        "sentiment_trend": float(sentiment_trend),
        "article_count_trend": float(article_count_trend),
        "sentiment_volatility": float(sentiment_volatility),
        "trend_description": trend_description,
        "volume_description": volume_description,
        "summary": summary,
        "signal": signal,
        "confidence": float(confidence),
        "sentiment_data": sentiment_data.reset_index().to_dict(orient="records"),
    }


def plot_news_sentiment_chart(
    sentiment_data: pd.DataFrame,
    ticker: str,
    chart_type: str = "trend",
    title: Optional[str] = None,
) -> str:
    """
    Plot news sentiment data.
    
    Args:
        sentiment_data: DataFrame with sentiment data
        ticker: Stock ticker symbol
        chart_type: Type of chart ("trend", "distribution", or "volume")
        title: Plot title (optional)
        
    Returns:
        Base64-encoded image of the plot
    """
    if sentiment_data.empty:
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, "No sentiment data available", ha="center", va="center", fontsize=14)
        ax.set_title(title or f"News Sentiment for {ticker}")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    if chart_type == "trend":
        # Create sentiment trend chart
        fig, ax1 = plt.subplots(figsize=(12, 6))
        
        # Plot sentiment score
        ax1.plot(sentiment_data.index, sentiment_data["sentiment_score"], color="blue", linewidth=2, label="Sentiment Score")
        ax1.set_xlabel("Date")
        ax1.set_ylabel("Sentiment Score", color="blue")
        ax1.tick_params(axis="y", labelcolor="blue")
        ax1.axhline(y=0, color="gray", linestyle="--", alpha=0.5)
        
        # Create second y-axis for article count
        ax2 = ax1.twinx()
        ax2.bar(sentiment_data.index, sentiment_data["article_count"], alpha=0.3, color="green", label="Article Count")
        ax2.set_ylabel("Article Count", color="green")
        ax2.tick_params(axis="y", labelcolor="green")
        
        # Add legend
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc="upper left")
        
        # Format the plot
        plt.title(title or f"News Sentiment Trend for {ticker}")
        fig.autofmt_xdate()  # Rotate date labels
        plt.grid(True, alpha=0.3)
        
    elif chart_type == "distribution":
        # Create sentiment distribution chart
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Calculate average counts
        avg_positive = sentiment_data["positive_count"].mean()
        avg_negative = sentiment_data["negative_count"].mean()
        avg_neutral = sentiment_data["neutral_count"].mean()
        
        # Create pie chart
        labels = ["Positive", "Negative", "Neutral"]
        sizes = [avg_positive, avg_negative, avg_neutral]
        colors = ["green", "red", "gray"]
        explode = (0.1, 0.1, 0)  # Explode positive and negative slices
        
        ax.pie(
            sizes,
            explode=explode,
            labels=labels,
            colors=colors,
            autopct="%1.1f%%",
            shadow=True,
            startangle=90,
        )
        ax.axis("equal")  # Equal aspect ratio ensures that pie is drawn as a circle
        
        # Format the plot
        plt.title(title or f"News Sentiment Distribution for {ticker}")
        
    elif chart_type == "volume":
        # Create article volume chart
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Plot article counts by sentiment
        ax.bar(sentiment_data.index, sentiment_data["positive_count"], color="green", label="Positive")
        ax.bar(
            sentiment_data.index,
            sentiment_data["neutral_count"],
            bottom=sentiment_data["positive_count"],
            color="gray",
            label="Neutral",
        )
        ax.bar(
            sentiment_data.index,
            sentiment_data["negative_count"],
            bottom=sentiment_data["positive_count"] + sentiment_data["neutral_count"],
            color="red",
            label="Negative",
        )
        
        # Format the plot
        ax.set_xlabel("Date")
        ax.set_ylabel("Article Count")
        ax.legend()
        plt.title(title or f"News Volume by Sentiment for {ticker}")
        fig.autofmt_xdate()  # Rotate date labels
        plt.grid(True, axis="y", alpha=0.3)
        
    else:  # Default case
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, f"Unknown chart type: {chart_type}", ha="center", va="center", fontsize=14)
        ax.set_title(title or f"News Sentiment for {ticker}")
        ax.axis("off")
    
    # Convert plot to base64-encoded image
    buffer = io.BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
    plt.close()
    
    return image_base64
