{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_percentage_error\n", "import matplotlib.pyplot as plt\n", "from pytrends.request import TrendReq\n", "import yfinance as yf\n", "from datetime import datetime, timedelta\n", "import faiss"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 1. 数据获取与预处理\n", "class FinancialDataProcessor:\n", "    def __init__(self, ticker, start_date, end_date):\n", "        self.ticker = ticker\n", "        self.start_date = start_date\n", "        self.end_date = end_date\n", "        self.price_data = None\n", "        self.trend_data = None\n", "        self.combined_data = None\n", "        self.scaler_price = MinMaxScaler(feature_range=(0, 1))\n", "        self.scaler_trend = MinMaxScaler(feature_range=(0, 1))\n", "\n", "    def fetch_price_data(self):\n", "        \"\"\"获取价格数据\"\"\"\n", "        self.price_data = yf.download(self.ticker, start=self.start_date, end=self.end_date)\n", "        return self.price_data\n", "\n", "    def fetch_trend_data(self, keywords):\n", "        \"\"\"获取Google Trends数据\"\"\"\n", "        pytrend = TrendReq(hl='en-US', tz=360)\n", "        pytrend.build_payload(keywords, timeframe=f'{self.start_date} {self.end_date}')\n", "        self.trend_data = pytrend.interest_over_time()\n", "        return self.trend_data\n", "\n", "    def combine_data(self):\n", "        \"\"\"合并价格和趋势数据\"\"\"\n", "        if self.price_data is None or self.trend_data is None:\n", "            raise ValueError(\"Price data and trend data must be fetched before combining\")\n", "\n", "        # 确保两个数据集的日期一致\n", "        self.trend_data = self.trend_data.resample('D').mean().fillna(method='ffill')\n", "        self.price_data = self.price_data.resample('D').last().fillna(method='ffill')\n", "\n", "        # 合并数据\n", "        self.combined_data = pd.merge(\n", "            self.price_data[['Close']], \n", "            self.trend_data, \n", "            left_index=True, \n", "            right_index=True, \n", "            how='inner'\n", "        )\n", "        return self.combined_data\n", "\n", "    def preprocess_data(self, seq_length=30):\n", "        \"\"\"预处理数据，创建时间序列样本\"\"\"\n", "        if self.combined_data is None:\n", "            raise ValueError(\"Data must be combined before preprocessing\")\n", "\n", "        # 标准化数据\n", "        price_values = self.combined_data[['Close']].values\n", "        trend_values = self.combined_data.iloc[:, 1:].values\n", "\n", "        scaled_price = self.scaler_price.fit_transform(price_values)\n", "        scaled_trend = self.scaler_trend.fit_transform(trend_values)\n", "\n", "        # 创建时间序列样本\n", "        X_price, X_trend, y = [], [], []\n", "\n", "        for i in range(len(scaled_price) - seq_length):\n", "            X_price.append(scaled_price[i:i+seq_length])\n", "            X_trend.append(scaled_trend[i:i+seq_length])\n", "            y.append(scaled_price[i+seq_length])\n", "\n", "        return np.array(X_price), np.array(X_trend), np.array(y)\n", "\n", "# 2. 时间序列数据集\n", "class TimeSeriesDataset(Dataset):\n", "    def __init__(self, X_price, X_trend, y):\n", "        self.X_price = torch.FloatTensor(X_price)\n", "        self.X_trend = torch.FloatTensor(X_trend)\n", "        self.y = torch.FloatTensor(y)\n", "\n", "    def __len__(self):\n", "        return len(self.y)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.X_price[idx], self.X_trend[idx], self.y[idx]\n", "\n", "# 3. 检索组件\n", "class TimeSeriesRetriever:\n", "    def __init__(self, embedding_dim, n_neighbors=5):\n", "        self.embedding_dim = embedding_dim\n", "        self.n_neighbors = n_neighbors\n", "        self.index = None\n", "        self.price_embeddings = None\n", "        self.trend_embeddings = None\n", "        self.targets = None\n", "\n", "    def build_index(self, price_embeddings, trend_embeddings, targets):\n", "        \"\"\"构建FAISS索引\"\"\"\n", "        self.price_embeddings = price_embeddings\n", "        self.trend_embeddings = trend_embeddings\n", "        self.targets = targets\n", "\n", "        # 合并价格和趋势嵌入\n", "        combined_embeddings = np.concatenate([price_embeddings, trend_embeddings], axis=1)\n", "\n", "        # 构建索引\n", "        self.index = faiss.IndexFlatL2(combined_embeddings.shape[1])\n", "        self.index.add(combined_embeddings)\n", "\n", "    def retrieve(self, price_query, trend_query):\n", "        \"\"\"检索最相似的历史序列\"\"\"\n", "        combined_query = np.concatenate([price_query, trend_query], axis=1)\n", "\n", "        # 执行检索\n", "        distances, indices = self.index.search(combined_query, self.n_neighbors)\n", "\n", "        # 返回检索结果\n", "        retrieved_prices = self.price_embeddings[indices]\n", "        retrieved_trends = self.trend_embeddings[indices]\n", "        retrieved_targets = self.targets[indices]\n", "\n", "        return retrieved_prices, retrieved_trends, retrieved_targets, distances\n", "\n", "# 4. TimeRAG编码器\n", "class TimeSeriesEncoder(nn.Module):\n", "    def __init__(self, input_dim, hidden_dim, num_layers=2):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.hidden_dim = hidden_dim\n", "        self.num_layers = num_layers\n", "\n", "        # LSTM编码器\n", "        self.lstm = nn.LSTM(\n", "            input_dim, \n", "            hidden_dim, \n", "            num_layers=num_layers, \n", "            batch_first=True, \n", "            bidirectional=True\n", "        )\n", "\n", "        # 注意力机制\n", "        self.attention = nn.Sequential(\n", "            nn.Linear(hidden_dim * 2, hidden_dim),\n", "            nn.<PERSON>(),\n", "            nn.Linear(hidden_dim, 1),\n", "            nn.<PERSON><PERSON>(dim=1)\n", "        )\n", "\n", "    def forward(self, x):\n", "        # LSTM编码\n", "        lstm_out, _ = self.lstm(x)\n", "\n", "        # 注意力加权\n", "        attention_weights = self.attention(lstm_out)\n", "        context_vector = torch.sum(attention_weights * lstm_out, dim=1)\n", "\n", "        return context_vector\n", "\n", "# 5. TimeRAG生成器\n", "class TimeSeriesGenerator(nn.Module):\n", "    def __init__(self, context_dim, hidden_dim, output_dim):\n", "        super(TimeSeries<PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.context_processor = nn.Sequential(\n", "            nn.Linear(context_dim, hidden_dim),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.2)\n", "        )\n", "\n", "        self.retrieval_processor = nn.Sequential(\n", "            nn.Linear(context_dim, hidden_dim),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.2)\n", "        )\n", "\n", "        self.fusion_layer = nn.Sequential(\n", "            nn.Linear(hidden_dim * 2, hidden_dim),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.2)\n", "        )\n", "\n", "        self.output_layer = nn.Sequential(\n", "            nn.Linear(hidden_dim, output_dim),\n", "            nn.Sigmoid()  # 使用Sigmoid确保输出在0-1范围内（因为使用了MinMaxScaler）\n", "        )\n", "\n", "    def forward(self, context_embedding, retrieval_embedding):\n", "        # 处理上下文嵌入\n", "        processed_context = self.context_processor(context_embedding)\n", "\n", "        # 处理检索嵌入\n", "        processed_retrieval = self.retrieval_processor(retrieval_embedding)\n", "\n", "        # 融合\n", "        fusion = torch.cat([processed_context, processed_retrieval], dim=1)\n", "        fusion_output = self.fusion_layer(fusion)\n", "\n", "        # 生成预测\n", "        prediction = self.output_layer(fusion_output)\n", "\n", "        return prediction\n", "\n", "# 6. 完整的TimeRAG模型\n", "class TimeRAG(nn.Module):\n", "    def __init__(self, price_dim, trend_dim, hidden_dim, output_dim, retriever, n_neighbors=5):\n", "        super(<PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        # 编码器\n", "        self.price_encoder = TimeSeriesEncoder(price_dim, hidden_dim)\n", "        self.trend_encoder = TimeSeriesEncoder(trend_dim, hidden_dim)\n", "\n", "        # 检索器\n", "        self.retriever = retriever\n", "        self.n_neighbors = n_neighbors\n", "\n", "        # 生成器\n", "        context_dim = hidden_dim * 2  # 因为LSTM是双向的\n", "        self.generator = TimeSeriesGenerator(context_dim * 2, hidden_dim, output_dim)\n", "\n", "    def encode(self, price_seq, trend_seq):\n", "        \"\"\"编码输入序列\"\"\"\n", "        price_embedding = self.price_encoder(price_seq)\n", "        trend_embedding = self.trend_encoder(trend_seq)\n", "        return price_embedding, trend_embedding\n", "\n", "    def forward(self, price_seq, trend_seq, is_training=True):\n", "        # 编码输入序列\n", "        price_embedding, trend_embedding = self.encode(price_seq, trend_seq)\n", "\n", "        if is_training:\n", "            # 在训练时，使用批处理\n", "            batch_size = price_seq.size(0)\n", "\n", "            # 转换为numpy以用于FAISS检索\n", "            price_np = price_embedding.detach().cpu().numpy()\n", "            trend_np = trend_embedding.detach().cpu().numpy()\n", "\n", "            # 执行检索\n", "            retrieved_prices, retrieved_trends, _, _ = self.retriever.retrieve(price_np, trend_np)\n", "\n", "            # 转换回PyTorch张量并重塑维度\n", "            retrieved_prices = torch.FloatTensor(retrieved_prices).to(price_seq.device)\n", "            retrieved_trends = torch.FloatTensor(retrieved_trends).to(trend_seq.device)\n", "\n", "            # 调整形状为 [batch_size, n_neighbors, embedding_dim]\n", "            retrieved_prices = retrieved_prices.view(batch_size, self.n_neighbors, -1)\n", "            retrieved_trends = retrieved_trends.view(batch_size, self.n_neighbors, -1)\n", "\n", "            # 为每个样本计算平均检索嵌入\n", "            avg_retrieved_price = torch.mean(retrieved_prices, dim=1)\n", "            avg_retrieved_trend = torch.mean(retrieved_trends, dim=1)\n", "\n", "            # 合并编码嵌入和检索嵌入\n", "            context_embedding = torch.cat([price_embedding, trend_embedding], dim=1)\n", "            retrieval_embedding = torch.cat([avg_retrieved_price, avg_retrieved_trend], dim=1)\n", "\n", "            # 生成预测\n", "            prediction = self.generator(context_embedding, retrieval_embedding)\n", "\n", "        else:\n", "            # 在推理时，使用单例处理\n", "\n", "            # 转换为numpy以用于FAISS检索\n", "            price_np = price_embedding.detach().cpu().numpy()\n", "            trend_np = trend_embedding.detach().cpu().numpy()\n", "\n", "            # 执行检索\n", "            retrieved_prices, retrieved_trends, _, _ = self.retriever.retrieve(price_np, trend_np)\n", "\n", "            # 转换回PyTorch张量\n", "            retrieved_prices = torch.FloatTensor(retrieved_prices).to(price_seq.device)\n", "            retrieved_trends = torch.FloatTensor(retrieved_trends).to(trend_seq.device)\n", "\n", "            # 计算平均检索嵌入\n", "            avg_retrieved_price = torch.mean(retrieved_prices, dim=1)\n", "            avg_retrieved_trend = torch.mean(retrieved_trends, dim=1)\n", "\n", "            # 合并编码嵌入和检索嵌入\n", "            context_embedding = torch.cat([price_embedding, trend_embedding], dim=1)\n", "            retrieval_embedding = torch.cat([avg_retrieved_price, avg_retrieved_trend], dim=1)\n", "\n", "            # 生成预测\n", "            prediction = self.generator(context_embedding, retrieval_embedding)\n", "\n", "        return prediction\n", "\n", "# 7. 训练与评估\n", "def train_timerag(model, train_loader, val_loader, criterion, optimizer, device, epochs=50):\n", "    \"\"\"训练TimeRAG模型\"\"\"\n", "    model.to(device)\n", "    best_val_loss = float('inf')\n", "    train_losses, val_losses = [], []\n", "\n", "    for epoch in range(epochs):\n", "        # 训练阶段\n", "        model.train()\n", "        train_loss = 0\n", "\n", "        for price_seq, trend_seq, target in train_loader:\n", "            price_seq, trend_seq, target = price_seq.to(device), trend_seq.to(device), target.to(device)\n", "\n", "            # 清除梯度\n", "            optimizer.zero_grad()\n", "\n", "            # 前向传播\n", "            output = model(price_seq, trend_seq)\n", "\n", "            # 计算损失\n", "            loss = criterion(output, target)\n", "\n", "            # 反向传播\n", "            loss.backward()\n", "\n", "            # 更新参数\n", "            optimizer.step()\n", "\n", "            train_loss += loss.item()\n", "\n", "        train_loss /= len(train_loader)\n", "        train_losses.append(train_loss)\n", "\n", "        # 验证阶段\n", "        model.eval()\n", "        val_loss = 0\n", "\n", "        with torch.no_grad():\n", "            for price_seq, trend_seq, target in val_loader:\n", "                price_seq, trend_seq, target = price_seq.to(device), trend_seq.to(device), target.to(device)\n", "\n", "                # 前向传播\n", "                output = model(price_seq, trend_seq, is_training=False)\n", "\n", "                # 计算损失\n", "                loss = criterion(output, target)\n", "                val_loss += loss.item()\n", "\n", "        val_loss /= len(val_loader)\n", "        val_losses.append(val_loss)\n", "\n", "        # 打印进度\n", "        print(f'Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}')\n", "\n", "        # 保存最佳模型\n", "        if val_loss < best_val_loss:\n", "            best_val_loss = val_loss\n", "            torch.save(model.state_dict(), 'best_timerag_model.pth')\n", "\n", "    return train_losses, val_losses\n", "\n", "def evaluate_timerag(model, test_loader, scaler, device):\n", "    \"\"\"评估TimeRAG模型\"\"\"\n", "    model.to(device)\n", "    model.eval()\n", "    predictions, actuals = [], []\n", "\n", "    with torch.no_grad():\n", "        for price_seq, trend_seq, target in test_loader:\n", "            price_seq, trend_seq, target = price_seq.to(device), trend_seq.to(device), target.to(device)\n", "\n", "            # 前向传播\n", "            output = model(price_seq, trend_seq, is_training=False)\n", "\n", "            # 转换回CPU\n", "            output = output.cpu().numpy()\n", "            target = target.cpu().numpy()\n", "\n", "            # 逆标准化\n", "            output = scaler.inverse_transform(output)\n", "            target = scaler.inverse_transform(target)\n", "\n", "            predictions.extend(output)\n", "            actuals.extend(target)\n", "\n", "    # 计算评估指标\n", "    predictions = np.array(predictions)\n", "    actuals = np.array(actuals)\n", "\n", "    rmse = np.sqrt(mean_squared_error(actuals, predictions))\n", "    mape = mean_absolute_percentage_error(actuals, predictions) * 100\n", "\n", "    # 计算方向准确率\n", "    direction_actual = np.diff(actuals.flatten())\n", "    direction_pred = np.diff(predictions.flatten())\n", "    direction_accuracy = np.mean((direction_actual * direction_pred) > 0) * 100\n", "\n", "    return rmse, mape, direction_accuracy, predictions, actuals\n", "\n", "# 8. 回测交易策略\n", "def backtest_strategy(predictions, actuals, initial_capital=10000):\n", "    \"\"\"回测简单的交易策略\"\"\"\n", "    capital = initial_capital\n", "    position = 0\n", "    trades = []\n", "\n", "    for i in range(1, len(predictions)):\n", "        # 预测方向\n", "        pred_direction = predictions[i] - actuals[i-1]\n", "\n", "        # 实际方向\n", "        actual_direction = actuals[i] - actuals[i-1]\n", "\n", "        # 交易逻辑：预测上涨则买入，预测下跌则卖出\n", "        if pred_direction > 0 and position <= 0:\n", "            # 买入\n", "            position = 1\n", "            entry_price = actuals[i-1]\n", "            trades.append(('buy', i-1, entry_price))\n", "        elif pred_direction < 0 and position >= 0:\n", "            # 卖出\n", "            position = -1\n", "            entry_price = actuals[i-1]\n", "            trades.append(('sell', i-1, entry_price))\n", "\n", "    # 计算交易绩效\n", "    returns = []\n", "    current_position = None\n", "\n", "    for i in range(len(trades)):\n", "        action, day, price = trades[i]\n", "\n", "        if i < len(trades) - 1:\n", "            next_action, next_day, next_price = trades[i+1]\n", "\n", "            if action == 'buy' and next_action == 'sell':\n", "                # 完成一次买入-卖出交易\n", "                returns.append((next_price - price) / price)\n", "            elif action == 'sell' and next_action == 'buy':\n", "                # 完成一次卖出-买入交易\n", "                returns.append((price - next_price) / price)\n", "\n", "    # 计算绩效指标\n", "    returns = np.array(returns)\n", "    cumulative_return = np.prod(1 + returns) - 1\n", "    annual_return = (1 + cumulative_return) ** (252 / len(actuals)) - 1\n", "    volatility = np.std(returns) * np.sqrt(252)\n", "    sharpe_ratio = annual_return / volatility if volatility != 0 else 0\n", "\n", "    # 计算最大回撤\n", "    cumulative_returns = np.cumprod(1 + returns) - 1\n", "    peak = np.maximum.accumulate(cumulative_returns)\n", "    drawdown = (peak - cumulative_returns) / (1 + peak)\n", "    max_drawdown = np.max(drawdown)\n", "\n", "    # 计算胜率\n", "    win_rate = np.mean(returns > 0) * 100\n", "\n", "    return {\n", "        'cumulative_return': cumulative_return * 100,\n", "        'annual_return': annual_return * 100,\n", "        'sharpe_ratio': sharpe_ratio,\n", "        'max_drawdown': max_drawdown * 100,\n", "        'win_rate': win_rate\n", "    }\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "# 9. 主函数\n", "# def main():\n", "# 设置设备\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "\n", "# 1. 数据准备\n", "processor = FinancialDataProcessor(\n", "    ticker='^TWII',  # 台湾加权指数\n", "    start_date='2015-01-01',\n", "    end_date='2022-12-31'\n", ")\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["YF.download() has changed argument auto_adjust default to True\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed\n"]}], "source": ["\n", "# 获取价格数据\n", "price_data = processor.fetch_price_data()\n", "\n", "# 获取Google Trends数据\n", "trend_data = processor.fetch_trend_data(\n", "    keywords=['stock market', 'financial crisis', 'economic recession', 'bull market']\n", ")\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stock market</th>\n", "      <th>financial crisis</th>\n", "      <th>economic recession</th>\n", "      <th>bull market</th>\n", "      <th>isPartial</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-01-01</th>\n", "      <td>15</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-02-01</th>\n", "      <td>15</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-03-01</th>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-01</th>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-05-01</th>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-01</th>\n", "      <td>23</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-01</th>\n", "      <td>29</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-01</th>\n", "      <td>26</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-11-01</th>\n", "      <td>24</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-12-01</th>\n", "      <td>24</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>96 rows × 5 columns</p>\n", "</div>"], "text/plain": ["            stock market  financial crisis  economic recession  bull market  \\\n", "date                                                                          \n", "2015-01-01            15                 1                   0            0   \n", "2015-02-01            15                 1                   0            0   \n", "2015-03-01            14                 1                   0            0   \n", "2015-04-01            14                 1                   0            0   \n", "2015-05-01            12                 1                   0            0   \n", "...                  ...               ...                 ...          ...   \n", "2022-08-01            23                 1                   0            1   \n", "2022-09-01            29                 1                   0            1   \n", "2022-10-01            26                 1                   0            1   \n", "2022-11-01            24                 1                   0            1   \n", "2022-12-01            24                 1                   0            1   \n", "\n", "            isPartial  \n", "date                   \n", "2015-01-01      False  \n", "2015-02-01      False  \n", "2015-03-01      False  \n", "2015-04-01      False  \n", "2015-05-01      False  \n", "...               ...  \n", "2022-08-01      False  \n", "2022-09-01      False  \n", "2022-10-01      <PERSON><PERSON>e  \n", "2022-11-01      <PERSON><PERSON>e  \n", "2022-12-01      F<PERSON>e  \n", "\n", "[96 rows x 5 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["trend_data"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_24676\\1852020732.py:31: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  self.trend_data = self.trend_data.resample('D').mean().fillna(method='ffill')\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_24676\\1852020732.py:32: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  self.price_data = self.price_data.resample('D').last().fillna(method='ffill')\n"]}, {"ename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "evalue": "Not allowed to merge between different levels. (2 levels on the left, 1 on the right)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mMergeError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# 合并数据\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m combined_data \u001b[38;5;241m=\u001b[39m \u001b[43mprocessor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcombine_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;66;03m# 预处理数据\u001b[39;00m\n\u001b[0;32m      5\u001b[0m X_price, X_trend, y \u001b[38;5;241m=\u001b[39m processor\u001b[38;5;241m.\u001b[39mpreprocess_data(seq_length\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m30\u001b[39m)\n", "Cell \u001b[1;32mIn[2], line 35\u001b[0m, in \u001b[0;36mFinancialDataProcessor.combine_data\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     32\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprice_data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprice_data\u001b[38;5;241m.\u001b[39mresample(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mD\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mlast()\u001b[38;5;241m.\u001b[39mfillna(method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mffill\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     34\u001b[0m \u001b[38;5;66;03m# 合并数据\u001b[39;00m\n\u001b[1;32m---> 35\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcombined_data \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmerge\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m     36\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mprice_data\u001b[49m\u001b[43m[\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mClose\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m     37\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrend_data\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m     38\u001b[0m \u001b[43m    \u001b[49m\u001b[43mleft_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m     39\u001b[0m \u001b[43m    \u001b[49m\u001b[43mright_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[0;32m     40\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhow\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43minner\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\n\u001b[0;32m     41\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     42\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcombined_data\n", "File \u001b[1;32md:\\miniconda3\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py:170\u001b[0m, in \u001b[0;36mmerge\u001b[1;34m(left, right, how, on, left_on, right_on, left_index, right_index, sort, suffixes, copy, indicator, validate)\u001b[0m\n\u001b[0;32m    155\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _cross_merge(\n\u001b[0;32m    156\u001b[0m         left_df,\n\u001b[0;32m    157\u001b[0m         right_df,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    167\u001b[0m         copy\u001b[38;5;241m=\u001b[39mcopy,\n\u001b[0;32m    168\u001b[0m     )\n\u001b[0;32m    169\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 170\u001b[0m     op \u001b[38;5;241m=\u001b[39m \u001b[43m_MergeOperation\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    171\u001b[0m \u001b[43m        \u001b[49m\u001b[43mleft_df\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    172\u001b[0m \u001b[43m        \u001b[49m\u001b[43mright_df\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    173\u001b[0m \u001b[43m        \u001b[49m\u001b[43mhow\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhow\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    174\u001b[0m \u001b[43m        \u001b[49m\u001b[43mon\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mon\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    175\u001b[0m \u001b[43m        \u001b[49m\u001b[43mleft_on\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mleft_on\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    176\u001b[0m \u001b[43m        \u001b[49m\u001b[43mright_on\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mright_on\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    177\u001b[0m \u001b[43m        \u001b[49m\u001b[43mleft_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mleft_index\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    178\u001b[0m \u001b[43m        \u001b[49m\u001b[43mright_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mright_index\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    179\u001b[0m \u001b[43m        \u001b[49m\u001b[43msort\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    180\u001b[0m \u001b[43m        \u001b[49m\u001b[43msuffixes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msuffixes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    181\u001b[0m \u001b[43m        \u001b[49m\u001b[43mindicator\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindicator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    182\u001b[0m \u001b[43m        \u001b[49m\u001b[43mvalidate\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvalidate\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    183\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    184\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m op\u001b[38;5;241m.\u001b[39mget_result(copy\u001b[38;5;241m=\u001b[39mcopy)\n", "File \u001b[1;32md:\\miniconda3\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py:784\u001b[0m, in \u001b[0;36m_MergeOperation.__init__\u001b[1;34m(self, left, right, how, on, left_on, right_on, left_index, right_index, sort, suffixes, indicator, validate)\u001b[0m\n\u001b[0;32m    778\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m _left\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m!=\u001b[39m _right\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels:\n\u001b[0;32m    779\u001b[0m     msg \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m    780\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNot allowed to merge between different levels. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    781\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m(\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m_left\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m levels on the left, \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    782\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m_right\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m on the right)\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    783\u001b[0m     )\n\u001b[1;32m--> 784\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m MergeError(msg)\n\u001b[0;32m    786\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mleft_on, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mright_on \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_left_right_on(left_on, right_on)\n\u001b[0;32m    788\u001b[0m (\n\u001b[0;32m    789\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mleft_join_keys,\n\u001b[0;32m    790\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mright_join_keys,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    793\u001b[0m     right_drop,\n\u001b[0;32m    794\u001b[0m ) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_merge_keys()\n", "\u001b[1;31mMergeError\u001b[0m: Not allowed to merge between different levels. (2 levels on the left, 1 on the right)"]}], "source": ["\n", "# 合并数据\n", "combined_data = processor.combine_data()\n", "\n", "# 预处理数据\n", "X_price, X_trend, y = processor.preprocess_data(seq_length=30)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 分割数据集\n", "train_size = int(len(X_price) * 0.7)\n", "val_size = int(len(X_price) * 0.1)\n", "test_size = len(X_price) - train_size - val_size\n", "\n", "X_price_train, X_price_val, X_price_test = X_price[:train_size], X_price[train_size:train_size+val_size], X_price[train_size+val_size:]\n", "X_trend_train, X_trend_val, X_trend_test = X_trend[:train_size], X_trend[train_size:train_size+val_size], X_trend[train_size+val_size:]\n", "y_train, y_val, y_test = y[:train_size], y[train_size:train_size+val_size], y[train_size+val_size:]\n", "\n", "# 创建数据加载器\n", "train_dataset = TimeSeriesDataset(X_price_train, X_trend_train, y_train)\n", "val_dataset = TimeSeriesDataset(X_price_val, X_trend_val, y_val)\n", "test_dataset = TimeSeriesDataset(X_price_test, X_trend_test, y_test)\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)\n", "val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 2. 构建检索器\n", "# 首先需要为训练集生成嵌入\n", "hidden_dim = 128\n", "price_encoder = TimeSeriesEncoder(1, hidden_dim).to(device)\n", "trend_encoder = TimeSeriesEncoder(X_trend.shape[2], hidden_dim).to(device)\n", "\n", "# 生成嵌入\n", "price_embeddings, trend_embeddings = [], []\n", "targets = []\n", "\n", "with torch.no_grad():\n", "    for price_seq, trend_seq, target in train_loader:\n", "        price_seq, trend_seq = price_seq.to(device), trend_seq.to(device)\n", "        price_emb = price_encoder(price_seq).cpu().numpy()\n", "        trend_emb = trend_encoder(trend_seq).cpu().numpy()\n", "\n", "        price_embeddings.append(price_emb)\n", "        trend_embeddings.append(trend_emb)\n", "        targets.append(target.numpy())\n", "\n", "price_embeddings = np.vstack(price_embeddings)\n", "trend_embeddings = np.vstack(trend_embeddings)\n", "targets = np.vstack(targets)\n", "\n", "# 构建检索器\n", "retriever = TimeSeriesRetriever(embedding_dim=hidden_dim*2, n_neighbors=5)\n", "retriever.build_index(price_embeddings, trend_embeddings, targets)\n", "\n", "# 3. 构建TimeRAG模型\n", "timerag_model = TimeRAG(\n", "    price_dim=1,\n", "    trend_dim=X_trend.shape[2],\n", "    hidden_dim=hidden_dim,\n", "    output_dim=1,\n", "    retriever=retriever,\n", "    n_neighbors=5\n", ")\n", "\n", "# 4. 训练模型\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.Adam(timerag_model.parameters(), lr=0.001)\n", "\n", "train_losses, val_losses = train_timerag(\n", "    model=timerag_model,\n", "    train_loader=train_loader,\n", "    val_loader=val_loader,\n", "    criterion=criterion,\n", "    optimizer=optimizer,\n", "    device=device,\n", "    epochs=50\n", ")\n", "\n", "# 5. 评估模型\n", "# 加载最佳模型\n", "timerag_model.load_state_dict(torch.load('best_timerag_model.pth'))\n", "\n", "rmse, mape, direction_accuracy, predictions, actuals = evaluate_timerag(\n", "    model=timerag_model,\n", "    test_loader=test_loader,\n", "    scaler=processor.scaler_price,\n", "    device=device\n", ")\n", "\n", "print(f'Test RMSE: {rmse:.2f}')\n", "print(f'Test MAPE: {mape:.2f}%')\n", "print(f'Direction Accuracy: {direction_accuracy:.2f}%')\n", "\n", "# 6. 回测交易策略\n", "performance = backtest_strategy(predictions, actuals)\n", "\n", "print(f'Cumulative Return: {performance[\"cumulative_return\"]:.2f}%')\n", "print(f'<PERSON>: {performance[\"sharpe_ratio\"]:.2f}')\n", "print(f'Maximum Drawdown: {performance[\"max_drawdown\"]:.2f}%')\n", "print(f'Win Rate: {performance[\"win_rate\"]:.2f}%')\n", "\n", "# 7. 绘制结果\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(actuals, label='Actual')\n", "plt.plot(predictions, label='Predicted')\n", "plt.legend()\n", "plt.title('TimeRAG - Price Prediction')\n", "plt.xlabel('Time')\n", "plt.ylabel('Price')\n", "plt.savefig('timerag_prediction.png')\n", "plt.close()\n", "\n", "# 绘制训练和验证损失\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(train_losses, label='Train Loss')\n", "plt.plot(val_losses, label='Validation Loss')\n", "plt.legend()\n", "plt.title('TimeRAG - Training and Validation Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.savefig('timerag_loss.png')\n", "plt.close()\n", "\n", "# if __name__ == '__main__':\n", "#     main()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}