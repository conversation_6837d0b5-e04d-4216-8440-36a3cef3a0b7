"""
Portfolio Manager agent for the AI Hedge Fund module.

This agent makes final trading decisions based on signals from other agents.
"""

import json
from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field

from ..graph.state import AgentState, show_agent_reasoning
from ..utils.llm import call_llm


class PortfolioDecision(BaseModel):
    """Decision made by the Portfolio Manager agent."""
    action: Literal["buy", "sell", "short", "cover", "hold"]
    quantity: int = Field(description="Number of shares to trade")
    confidence: float = Field(description="Confidence in the decision, between 0.0 and 100.0")
    reasoning: str = Field(description="Reasoning for the decision")


class PortfolioManagerOutput(BaseModel):
    """Output from the Portfolio Manager agent."""
    decisions: Dict[str, PortfolioDecision] = Field(description="Dictionary of ticker to trading decisions")


def portfolio_management_agent(state: AgentState) -> Dict[str, Any]:
    """
    Makes final trading decisions and generates orders for multiple tickers.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with portfolio management decisions
    """
    # Get the portfolio and analyst signals
    portfolio = state["data"]["portfolio"]
    analyst_signals = state["data"]["analyst_signals"]
    tickers = state["data"]["tickers"]
    
    # Update progress status if available
    if "progress" in state["metadata"]:
        state["metadata"]["progress"].update_status("portfolio_management_agent", None, "Analyzing signals")
    
    # Get position limits, current prices, and signals for every ticker
    position_limits = {}
    current_prices = {}
    max_shares = {}
    signals_by_ticker = {}
    
    for ticker in tickers:
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("portfolio_management_agent", ticker, "Processing analyst signals")
        
        # Get position limits and current prices for the ticker
        risk_data = analyst_signals.get("risk_management_agent", {}).get(ticker, {})
        position_limits[ticker] = risk_data.get("remaining_position_limit", 10000)  # Default to $10,000 if not available
        current_prices[ticker] = risk_data.get("current_price", 100)  # Default to $100 if not available
        
        # Calculate maximum shares allowed based on position limit and price
        if current_prices[ticker] > 0:
            max_shares[ticker] = int(position_limits[ticker] / current_prices[ticker])
        else:
            max_shares[ticker] = 0
        
        # Get signals for the ticker
        ticker_signals = {}
        for agent, signals in analyst_signals.items():
            if agent != "risk_management_agent" and ticker in signals:
                ticker_signals[agent] = {
                    "signal": signals[ticker]["signal"],
                    "confidence": signals[ticker]["confidence"]
                }
        signals_by_ticker[ticker] = ticker_signals
    
    if "progress" in state["metadata"]:
        state["metadata"]["progress"].update_status("portfolio_management_agent", None, "Making trading decisions")
    
    # Generate the trading decision
    result = generate_trading_decision(
        tickers=tickers,
        signals_by_ticker=signals_by_ticker,
        current_prices=current_prices,
        max_shares=max_shares,
        portfolio=portfolio,
        model_name=state["metadata"]["model_name"],
        model_provider=state["metadata"]["model_provider"],
    )
    
    # Print the decision if the flag is set
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(
            {ticker: decision.model_dump() for ticker, decision in result.decisions.items()},
            "Portfolio Management Agent"
        )
    
    if "progress" in state["metadata"]:
        state["metadata"]["progress"].update_status("portfolio_management_agent", None, "Done")
    
    # Store the decisions in the state
    state["data"]["decisions"] = {ticker: decision.model_dump() for ticker, decision in result.decisions.items()}
    
    return state


def generate_trading_decision(
    tickers: List[str],
    signals_by_ticker: Dict[str, Dict[str, Any]],
    current_prices: Dict[str, float],
    max_shares: Dict[str, int],
    portfolio: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> PortfolioManagerOutput:
    """
    Generate trading decisions based on signals from other agents.
    
    Args:
        tickers: List of stock ticker symbols
        signals_by_ticker: Dictionary of ticker to signals from other agents
        current_prices: Dictionary of ticker to current prices
        max_shares: Dictionary of ticker to maximum shares allowed
        portfolio: Current portfolio state
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        PortfolioManagerOutput object with trading decisions
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a portfolio manager making final trading decisions based on multiple tickers.

    Trading Rules:
    - For long positions:
      * Only buy if you have available cash
      * Only sell if you currently hold long shares of that ticker
      * Sell quantity must be ≤ current long position shares
      * Buy quantity must be ≤ max_shares for that ticker

    - For short positions:
      * Only short if you have available margin (position value × margin requirement)
      * Only cover if you currently have short shares of that ticker
      * Cover quantity must be ≤ current short position shares
      * Short quantity must respect margin requirements

    - The max_shares values are pre-calculated to respect position limits
    - Consider both long and short opportunities based on signals
    - Maintain appropriate risk management with both long and short exposure

    Available Actions:
    - "buy": Open or add to long position
    - "sell": Close or reduce long position
    - "short": Open or add to short position
    - "cover": Close or reduce short position
    - "hold": No action

    Inputs:
    - signals_by_ticker: dictionary of ticker → signals
    - max_shares: maximum shares allowed per ticker
    - portfolio_cash: current cash in portfolio
    - portfolio_positions: current positions (both long and short)
    - current_prices: current prices for each ticker
    - margin_requirement: current margin requirement for short positions (e.g., 0.5 means 50%)
    - total_margin_used: total margin currently in use
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the team's analysis, make your trading decisions for each ticker.

    Here are the signals by ticker:
    {json.dumps(signals_by_ticker, indent=2)}

    Current Prices:
    {json.dumps(current_prices, indent=2)}

    Maximum Shares Allowed For Purchases:
    {json.dumps(max_shares, indent=2)}

    Portfolio Cash: {portfolio.get('cash', 0):.2f}
    Current Positions: {json.dumps(portfolio.get('positions', {}), indent=2)}
    Current Margin Requirement: {portfolio.get('margin_requirement', 0):.2f}
    Total Margin Used: {portfolio.get('margin_used', 0):.2f}

    Output strictly in JSON with the following structure:
    {{
      "decisions": {{
        "TICKER1": {{
          "action": "buy/sell/short/cover/hold",
          "quantity": integer,
          "confidence": float between 0 and 100,
          "reasoning": "string"
        }},
        "TICKER2": {{
          ...
        }},
        ...
      }}
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Create default factory for PortfolioManagerOutput
    def create_default_portfolio_output():
        decisions = {}
        
        for ticker in tickers:
            # Count bullish and bearish signals
            ticker_signals = signals_by_ticker.get(ticker, {})
            bullish_count = sum(1 for s in ticker_signals.values() if s.get("signal") == "bullish")
            bearish_count = sum(1 for s in ticker_signals.values() if s.get("signal") == "bearish")
            
            # Default action based on signal counts
            if bullish_count > bearish_count and bullish_count > 0:
                action = "buy"
                quantity = min(max_shares.get(ticker, 0), 10)  # Default to 10 shares or max_shares, whichever is smaller
                confidence = 60.0
                reasoning = f"Default bullish decision based on {bullish_count} bullish signals vs {bearish_count} bearish signals."
            elif bearish_count > bullish_count and bearish_count > 0:
                # Check if we have shares to sell
                position = portfolio.get("positions", {}).get(ticker, {})
                long_shares = position.get("long", 0)
                
                if long_shares > 0:
                    action = "sell"
                    quantity = min(long_shares, 10)  # Default to 10 shares or current position, whichever is smaller
                    confidence = 60.0
                    reasoning = f"Default bearish decision based on {bearish_count} bearish signals vs {bullish_count} bullish signals. Selling existing position."
                else:
                    action = "hold"
                    quantity = 0
                    confidence = 50.0
                    reasoning = f"Default bearish decision based on {bearish_count} bearish signals vs {bullish_count} bullish signals, but no existing position to sell."
            else:
                action = "hold"
                quantity = 0
                confidence = 50.0
                reasoning = f"Default hold decision based on balanced or insufficient signals ({bullish_count} bullish, {bearish_count} bearish)."
            
            decisions[ticker] = PortfolioDecision(
                action=action,
                quantity=quantity,
                confidence=confidence,
                reasoning=reasoning
            )
        
        return PortfolioManagerOutput(decisions=decisions)
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=PortfolioManagerOutput,
        agent_name="portfolio_management_agent",
        default_factory=create_default_portfolio_output,
    )
