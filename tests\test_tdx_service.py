"""
TdxDataService 测试模块

测试 TdxDataService 的主要功能，包括：
1. 连接到行情服务器
2. 获取股票数据
3. 获取行情数据
4. WebSocket 功能
"""

import unittest
import asyncio
import json
import os
import sys
import time
from unittest.mock import MagicMock, patch
from fastapi.testclient import TestClient
from fastapi import FastAPI, WebSocket
import pytest

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入被测试的模块
from rest_api.services.tdx_dc import TdxDataService
from rest_api.services.tdx_market_api import TdxStockMarketAPI, TdxFutureMarketAPI
from rest_api.routes.tdx_routes import router, get_tdx_service

# 创建测试用的 FastAPI 应用
app = FastAPI()
app.include_router(router, prefix="/api/v1/tdx")

# 创建测试客户端
client = TestClient(app)

# 模拟 TdxDataService 实例
@app.dependency_override
def get_tdx_service_override():
    # 创建 TdxDataService 的模拟对象
    mock_service = MagicMock()
    
    # 模拟 stock_api 和 future_api
    mock_service.stock_api = MagicMock()
    mock_service.future_api = MagicMock()
    
    # 模拟 get_stock_data_async 方法
    async def mock_get_stock_data_async(market, code, period, count):
        return [
            {
                "datetime": "2023-01-01 09:30:00",
                "open": 10.0,
                "high": 11.0,
                "low": 9.5,
                "close": 10.5,
                "volume": 10000,
                "amount": 100000,
                "market": market,
                "code": code
            }
        ]
    
    mock_service.get_stock_data_async = mock_get_stock_data_async
    
    # 模拟 stock_api.get_security_quotes 方法
    mock_service.stock_api.get_security_quotes.return_value = [
        {
            "price": 10.5,
            "open": 10.0,
            "high": 11.0,
            "low": 9.5,
            "volume": 10000,
            "amount": 100000,
            "bid1": 10.4,
            "bid2": 10.3,
            "bid3": 10.2,
            "bid4": 10.1,
            "bid5": 10.0,
            "bid1_vol": 100,
            "bid2_vol": 200,
            "bid3_vol": 300,
            "bid4_vol": 400,
            "bid5_vol": 500,
            "ask1": 10.6,
            "ask2": 10.7,
            "ask3": 10.8,
            "ask4": 10.9,
            "ask5": 11.0,
            "ask1_vol": 100,
            "ask2_vol": 200,
            "ask3_vol": 300,
            "ask4_vol": 400,
            "ask5_vol": 500
        }
    ]
    
    # 模拟 stock_api.get_index_bars 方法
    mock_service.stock_api.get_index_bars.return_value = [
        {
            "datetime": "2023-01-01 09:30:00",
            "open": 3000.0,
            "high": 3050.0,
            "low": 2980.0,
            "close": 3020.0,
            "volume": 1000000,
            "amount": 10000000
        }
    ]
    
    # 模拟 get_stock_list 方法
    mock_service.get_stock_list.return_value = [
        {"market": 0, "code": "000001", "name": "平安银行"},
        {"market": 1, "code": "600000", "name": "浦发银行"}
    ]
    
    # 模拟 future_api.get_future_list 方法
    mock_service.future_api.get_future_list.return_value = [
        {"market": 28, "code": "IF2401", "name": "沪深300期货2401"},
        {"market": 28, "code": "IC2401", "name": "中证500期货2401"}
    ]
    
    # 模拟 connected 和 ex_connected 属性
    mock_service.connected = True
    mock_service.stock_api.connected = True
    mock_service.ex_connected = True
    mock_service.future_api.connected = True
    
    return mock_service

# 覆盖依赖
app.dependency_overrides[get_tdx_service] = get_tdx_service_override

class TestTdxDataService(unittest.TestCase):
    """TdxDataService 测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建 TdxDataService 实例
        self.tsdb_path = "test_tsdb"
        
        # 使用 patch 模拟 TimeSeriesDB
        self.tsdb_patcher = patch('rest_api.services.tdx_dc.TimeSeriesDB')
        self.mock_tsdb = self.tsdb_patcher.start()
        
        # 使用 patch 模拟 TdxStockMarketAPI
        self.stock_api_patcher = patch('rest_api.services.tdx_dc.TdxStockMarketAPI')
        self.mock_stock_api = self.stock_api_patcher.start()
        self.mock_stock_api_instance = self.mock_stock_api.return_value
        
        # 使用 patch 模拟 TdxFutureMarketAPI
        self.future_api_patcher = patch('rest_api.services.tdx_dc.TdxFutureMarketAPI')
        self.mock_future_api = self.future_api_patcher.start()
        self.mock_future_api_instance = self.mock_future_api.return_value
        
        # 创建 TdxDataService 实例
        self.service = TdxDataService(self.tsdb_path)
        
    def tearDown(self):
        """测试后的清理工作"""
        # 停止所有 patch
        self.tsdb_patcher.stop()
        self.stock_api_patcher.stop()
        self.future_api_patcher.stop()
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.service.tsdb_path, self.tsdb_path)
        self.assertEqual(self.service.max_workers, 5)
        self.assertFalse(self.service.connected)
        self.assertFalse(self.service.ex_connected)
        self.assertIsNone(self.service.market_codes)
        self.assertEqual(self.service.instruments_cache, {})
        self.assertEqual(self.service.active_connections, set())
        self.assertEqual(self.service.subscription_map, {})
        self.assertEqual(self.service.connection_subscriptions, {})
        self.assertIsNone(self.service.quote_update_task)
    
    def test_connect(self):
        """测试连接方法"""
        # 模拟连接成功
        self.mock_stock_api_instance.connect.return_value = True
        self.mock_future_api_instance.connect.return_value = True
        
        # 调用连接方法
        result = self.service.connect()
        
        # 验证结果
        self.assertTrue(result)
        self.assertTrue(self.service.connected)
        self.assertTrue(self.service.ex_connected)
        self.mock_stock_api_instance.connect.assert_called()
        self.mock_future_api_instance.connect.assert_called()
    
    def test_disconnect(self):
        """测试断开连接方法"""
        # 设置连接状态
        self.service.connected = True
        self.service.ex_connected = True
        
        # 调用断开连接方法
        self.service.disconnect()
        
        # 验证结果
        self.assertFalse(self.service.connected)
        self.assertFalse(self.service.ex_connected)
        self.mock_stock_api_instance.disconnect.assert_called()
        self.mock_future_api_instance.disconnect.assert_called()
    
    @patch('rest_api.services.tdx_dc.asyncio.get_event_loop')
    def test_get_stock_data_async(self, mock_get_event_loop):
        """测试异步获取股票数据方法"""
        # 模拟事件循环
        mock_loop = MagicMock()
        mock_get_event_loop.return_value = mock_loop
        
        # 模拟 run_in_executor 方法
        mock_loop.run_in_executor.return_value = asyncio.Future()
        mock_loop.run_in_executor.return_value.set_result([{"datetime": "2023-01-01", "close": 10.5}])
        
        # 调用异步方法
        result = asyncio.run(self.service.get_stock_data_async(0, "000001", "day", 100))
        
        # 验证结果
        self.assertEqual(result, [{"datetime": "2023-01-01", "close": 10.5}])
        mock_loop.run_in_executor.assert_called_once()
    
    def test_get_stock_data_sync(self):
        """测试同步获取股票数据方法"""
        # 模拟 get_security_bars 方法
        self.mock_stock_api_instance.get_security_bars.return_value = [
            {"datetime": "2023-01-01", "open": 10.0, "high": 11.0, "low": 9.5, "close": 10.5}
        ]
        
        # 调用同步方法
        result = self.service._get_stock_data_sync(0, "000001", "day", 100)
        
        # 验证结果
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["datetime"], "2023-01-01")
        self.assertEqual(result[0]["close"], 10.5)
        self.mock_stock_api_instance.get_security_bars.assert_called_once_with(9, 0, "000001", 0, 100)

# API 测试
def test_get_stock_data_api():
    """测试获取股票数据 API"""
    response = client.get("/api/v1/tdx/stock/SZ/000001?period=day&count=100")
    assert response.status_code == 200
    data = response.json()
    assert data["market"] == "SZ"
    assert data["code"] == "000001"
    assert data["period"] == "day"
    assert len(data["data"]) > 0
    assert "datetime" in data["data"][0]
    assert "open" in data["data"][0]
    assert "high" in data["data"][0]
    assert "low" in data["data"][0]
    assert "close" in data["data"][0]

def test_get_stock_quote_api():
    """测试获取股票行情 API"""
    response = client.get("/api/v1/tdx/quote/SZ/000001")
    assert response.status_code == 200
    data = response.json()
    assert "price" in data
    assert "open" in data
    assert "high" in data
    assert "low" in data
    assert "volume" in data
    assert "amount" in data
    assert "market" in data
    assert "code" in data
    assert "timestamp" in data

def test_get_index_data_api():
    """测试获取指数数据 API"""
    response = client.get("/api/v1/tdx/index/SH/000001?period=day&count=100")
    assert response.status_code == 200
    data = response.json()
    assert data["market"] == "SH"
    assert data["code"] == "000001"
    assert data["period"] == "day"
    assert len(data["data"]) > 0
    assert "datetime" in data["data"][0]
    assert "open" in data["data"][0]
    assert "high" in data["data"][0]
    assert "low" in data["data"][0]
    assert "close" in data["data"][0]

def test_get_stocks_api():
    """测试获取股票列表 API"""
    response = client.get("/api/v1/tdx/stocks")
    assert response.status_code == 200
    data = response.json()
    assert "count" in data
    assert "stocks" in data
    assert data["count"] > 0
    assert len(data["stocks"]) > 0
    assert "market" in data["stocks"][0]
    assert "code" in data["stocks"][0]
    assert "name" in data["stocks"][0]

def test_get_futures_api():
    """测试获取期货列表 API"""
    response = client.get("/api/v1/tdx/futures")
    assert response.status_code == 200
    data = response.json()
    assert "count" in data
    assert "futures" in data
    assert data["count"] > 0
    assert len(data["futures"]) > 0
    assert "market" in data["futures"][0]
    assert "code" in data["futures"][0]
    assert "name" in data["futures"][0]

# WebSocket 测试
@pytest.mark.asyncio
async def test_websocket_connection():
    """测试 WebSocket 连接"""
    # 这个测试需要在实际环境中运行，这里只是示例
    # 在实际测试中，可以使用 pytest-asyncio 和 websockets 库进行测试
    pass

if __name__ == "__main__":
    unittest.main()
