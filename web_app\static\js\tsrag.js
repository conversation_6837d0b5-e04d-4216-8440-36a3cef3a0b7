// 全局变量
let currentInputData = null;
let searchResults = null;
let inputChart = null;
let overlayChart = null;
let resultCharts = [];

// API基础URL
const API_BASE_URL = '/api';

// 访问令牌
let accessToken = null;
let username = null;

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化事件监听器
    initEventListeners();

    // 检查本地存储中是否有登录信息
    checkLoginStatus();

    // 初始化输入图表
    initInputChart();

    // 设置当前日期为结束日期
    const today = new Date();
    document.getElementById('endDate').valueAsDate = today;

    // 设置30天前为开始日期
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    document.getElementById('startDate').valueAsDate = thirtyDaysAgo;

    // 自动加载默认股票代码的数据
    const defaultTicker = document.getElementById('tickerInput').value;
    if (defaultTicker) {
        console.log(`自动加载默认股票代码: ${defaultTicker}`);
        loadDefaultStockData(defaultTicker);
    }
});

// 初始化事件监听器
function initEventListeners() {
    // 输入方式切换事件
    document.getElementById('inputMethod').addEventListener('change', function() {
        const method = this.value;

        // 隐藏所有输入区域
        document.getElementById('tickerInputSection').style.display = 'none';
        document.getElementById('manualInputSection').style.display = 'none';
        document.getElementById('uploadSection').style.display = 'none';

        // 显示选中的输入区域
        if (method === 'ticker') {
            document.getElementById('tickerInputSection').style.display = 'block';
        } else if (method === 'manual') {
            document.getElementById('manualInputSection').style.display = 'block';
        } else if (method === 'upload') {
            document.getElementById('uploadSection').style.display = 'block';
        }
    });

    // K线周期切换事件
    document.getElementById('klineInterval').addEventListener('change', function() {
        const ticker = document.getElementById('tickerInput').value;
        if (ticker) {
            loadDefaultStockData(ticker);
        }
    });

    // K线时间范围切换事件
    document.getElementById('klinePeriod').addEventListener('change', function() {
        const ticker = document.getElementById('tickerInput').value;
        if (ticker) {
            loadDefaultStockData(ticker);
        }
    });

    // 股票代码输入框失去焦点事件
    document.getElementById('tickerInput').addEventListener('blur', function() {
        const ticker = this.value.trim();
        if (ticker) {
            loadDefaultStockData(ticker);
        }
    });

    // 股票代码输入框回车事件
    document.getElementById('tickerInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const ticker = this.value.trim();
            if (ticker) {
                loadDefaultStockData(ticker);
            }
        }
    });

    // 视图切换事件
    document.querySelectorAll('[data-view]').forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有按钮的active类
            document.querySelectorAll('[data-view]').forEach(btn => {
                btn.classList.remove('active');
            });

            // 添加当前按钮的active类
            this.classList.add('active');

            // 获取选中的视图类型
            const viewType = this.getAttribute('data-view');

            // 切换视图
            if (viewType === 'separate') {
                document.getElementById('separateView').style.display = 'block';
                document.getElementById('overlayView').style.display = 'none';
            } else if (viewType === 'overlay') {
                document.getElementById('separateView').style.display = 'none';
                document.getElementById('overlayView').style.display = 'block';

                // 如果有搜索结果，更新叠加图表
                if (searchResults) {
                    updateOverlayChart();
                }
            }
        });
    });

    // 搜索按钮点击事件
    document.getElementById('searchBtn').addEventListener('click', function() {
        performSearch();
    });

    // CSV文件上传事件
    document.getElementById('csvFile').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const csvData = event.target.result;
                const parsedData = parseCSV(csvData);

                // 更新输入图表
                updateInputChart(parsedData);

                // 存储当前输入数据
                currentInputData = parsedData;
            };
            reader.readAsText(file);
        }
    });

    // 登录按钮点击事件
    document.getElementById('loginBtn').addEventListener('click', function() {
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    });

    // 注册按钮点击事件
    document.getElementById('registerBtn').addEventListener('click', function() {
        const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
        registerModal.show();
    });

    // 登录表单提交事件
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;

        login(username, password);
    });

    // 注册表单提交事件
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('registerUsername').value;
        const email = document.getElementById('registerEmail').value;
        const fullName = document.getElementById('registerFullName').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('registerConfirmPassword').value;

        if (password !== confirmPassword) {
            alert('两次输入的密码不一致');
            return;
        }

        register(username, email, fullName, password);
    });
}

// 检查登录状态
function checkLoginStatus() {
    // 从本地存储中获取访问令牌和用户名
    accessToken = localStorage.getItem('accessToken');
    username = localStorage.getItem('username');

    if (accessToken && username) {
        // 更新UI显示已登录状态
        document.getElementById('loginBtn').textContent = username;
        document.getElementById('registerBtn').textContent = '退出';

        // 修改注册按钮为退出功能
        document.getElementById('registerBtn').removeEventListener('click', null);
        document.getElementById('registerBtn').addEventListener('click', function() {
            logout();
        });
    }
}

// 登录函数
async function login(username, password) {
    try {
        const response = await fetch(`${API_BASE_URL}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        if (!response.ok) {
            throw new Error(`登录失败: ${response.status}`);
        }

        const data = await response.json();

        // 存储访问令牌和用户名
        localStorage.setItem('accessToken', data.access_token);
        localStorage.setItem('username', username);

        // 更新全局变量
        accessToken = data.access_token;

        // 更新UI
        document.getElementById('loginBtn').textContent = username;
        document.getElementById('registerBtn').textContent = '退出';

        // 关闭模态框
        const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
        loginModal.hide();

        // 修改注册按钮为退出功能
        document.getElementById('registerBtn').removeEventListener('click', null);
        document.getElementById('registerBtn').addEventListener('click', function() {
            logout();
        });

    } catch (error) {
        console.error('登录失败:', error);
        alert(`登录失败: ${error.message}`);
    }
}

// 注册函数
async function register(username, email, fullName, password) {
    try {
        const response = await fetch(`${API_BASE_URL}/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                email: email,
                full_name: fullName,
                password: password
            })
        });

        if (!response.ok) {
            throw new Error(`注册失败: ${response.status}`);
        }

        await response.json();

        // 关闭模态框
        const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
        registerModal.hide();

        // 提示注册成功
        alert('注册成功，请登录');

        // 打开登录模态框
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();

    } catch (error) {
        console.error('注册失败:', error);
        alert(`注册失败: ${error.message}`);
    }
}

// 退出登录
function logout() {
    // 清除本地存储中的登录信息
    localStorage.removeItem('accessToken');
    localStorage.removeItem('username');

    // 更新全局变量
    accessToken = null;
    username = null;

    // 更新UI
    document.getElementById('loginBtn').textContent = '登录';
    document.getElementById('registerBtn').textContent = '注册';

    // 恢复注册按钮功能
    document.getElementById('registerBtn').removeEventListener('click', null);
    document.getElementById('registerBtn').addEventListener('click', function() {
        const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
        registerModal.show();
    });
}

// 初始化输入图表
function initInputChart() {
    const chartContainer = document.getElementById('inputChart');

    // 显示加载中提示
    chartContainer.innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-3 text-muted">正在加载数据...</p></div>';

    // 创建图表
    inputChart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: 300,
        layout: {
            backgroundColor: '#ffffff',
            textColor: '#333',
        },
        grid: {
            vertLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
            horzLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
        },
        timeScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
            timeVisible: true,
        },
        // 禁用鼠标滚轮缩放，避免影响页面滚动
        handleScroll: {
            mouseWheel: false,
        },
        // 禁用鼠标滚轮缩放
        handleScale: {
            mouseWheel: false,
        },
    });

    // 添加响应式调整
    window.addEventListener('resize', () => {
        inputChart.applyOptions({ width: chartContainer.clientWidth });
    });
}

// 更新输入图表
function updateInputChart(data) {
    const chartContainer = document.getElementById('inputChart');
    chartContainer.innerHTML = '';

    // 重新创建图表
    inputChart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: 300,
        layout: {
            backgroundColor: '#ffffff',
            textColor: '#333',
        },
        grid: {
            vertLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
            horzLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
        },
        timeScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
            timeVisible: true,
        },
        // 禁用鼠标滚轮缩放，避免影响页面滚动
        handleScroll: {
            mouseWheel: false,
        },
        // 禁用鼠标滚轮缩放
        handleScale: {
            mouseWheel: false,
        },
    });

    // 添加K线图
    const candlestickSeries = inputChart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
    });

    // 准备数据
    const chartData = data.map(item => {
        // 处理不同格式的日期
        let timeValue;
        if (typeof item.date === 'string') {
            // 如果是分钟级别数据，包含时间部分
            if (item.date.includes(' ')) {
                const [datePart, timePart] = item.date.split(' ');
                timeValue = datePart + 'T' + timePart + ':00Z';
            } else {
                // 日线及以上级别只有日期部分
                timeValue = item.date;
            }
        } else {
            // 如果是时间戳或Date对象
            timeValue = new Date(item.date).toISOString().split('T')[0];
        }

        return {
            time: timeValue,
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close
        };
    });

    // 设置数据
    candlestickSeries.setData(chartData);

    // 调整可见范围
    inputChart.timeScale().fitContent();

    // 添加响应式调整
    window.addEventListener('resize', () => {
        inputChart.applyOptions({ width: chartContainer.clientWidth });
    });
}

// 更新结果图表
function updateResultCharts(results) {
    const resultChartsContainer = document.getElementById('resultCharts');
    resultChartsContainer.innerHTML = '';
    resultCharts = [];

    // 为每个结果创建图表
    results.forEach((result, index) => {
        // 创建图表容器
        const chartContainer = document.createElement('div');
        chartContainer.className = 'mb-4';
        chartContainer.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h5>结果 #${index + 1}: ${result.ticker || '未知'} (${result.similarity.toFixed(4)})</h5>
                <span class="badge ${result.future_trend > 0 ? 'bg-success' : 'bg-danger'}">后续${result.future_trend > 0 ? '上涨' : '下跌'} ${Math.abs(result.future_trend).toFixed(2)}%</span>
            </div>
            <div id="resultChart${index}" style="width: 100%; height: 250px;"></div>
        `;
        resultChartsContainer.appendChild(chartContainer);

        // 创建图表
        const chart = LightweightCharts.createChart(document.getElementById(`resultChart${index}`), {
            width: chartContainer.clientWidth,
            height: 250,
            layout: {
                backgroundColor: '#ffffff',
                textColor: '#333',
            },
            grid: {
                vertLines: {
                    color: 'rgba(197, 203, 206, 0.5)',
                },
                horzLines: {
                    color: 'rgba(197, 203, 206, 0.5)',
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
            },
            timeScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
                timeVisible: true,
            },
            // 禁用鼠标滚轮缩放，避免影响页面滚动
            handleScroll: {
                mouseWheel: false,
            },
            // 禁用鼠标滚轮缩放
            handleScale: {
                mouseWheel: false,
            },
        });

        // 添加K线图
        const candlestickSeries = chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
        });

        // 准备数据
        const chartData = result.data.map(item => ({
            time: typeof item.date === 'string' ? item.date.split('T')[0] : new Date(item.date).toISOString().split('T')[0],
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close
        }));

        // 设置数据
        candlestickSeries.setData(chartData);

        // 如果有未来数据，添加未来数据线
        if (result.future_data && result.future_data.length > 0) {
            const futureSeries = chart.addLineSeries({
                color: '#2962FF',
                lineWidth: 2,
                lineStyle: LightweightCharts.LineStyle.Dotted,
                title: '未来走势',
            });

            const futureData = result.future_data.map(item => ({
                time: typeof item.date === 'string' ? item.date.split('T')[0] : new Date(item.date).toISOString().split('T')[0],
                value: item.close
            }));

            futureSeries.setData(futureData);
        }

        // 调整可见范围
        chart.timeScale().fitContent();

        // 添加响应式调整
        window.addEventListener('resize', () => {
            chart.applyOptions({ width: chartContainer.clientWidth });
        });

        // 保存图表引用
        resultCharts.push(chart);
    });
}

// 更新叠加图表
function updateOverlayChart() {
    const chartContainer = document.getElementById('overlayChart');
    chartContainer.innerHTML = '';

    // 创建图表
    overlayChart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: 400,
        layout: {
            backgroundColor: '#ffffff',
            textColor: '#333',
        },
        grid: {
            vertLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
            horzLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
        },
        timeScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
            timeVisible: true,
        },
    });

    // 添加输入数据线
    const inputSeries = overlayChart.addLineSeries({
        color: '#000000',
        lineWidth: 2,
        title: '输入数据',
    });

    // 准备输入数据
    const inputData = currentInputData.map((item, index) => ({
        time: index, // 使用索引作为时间，以便对齐所有序列
        value: item.close
    }));

    // 设置输入数据
    inputSeries.setData(inputData);

    // 添加结果数据线
    const colors = ['#2962FF', '#FF6D00', '#7B1FA2', '#388E3C', '#D32F2F'];

    searchResults.forEach((result, resultIndex) => {
        const resultSeries = overlayChart.addLineSeries({
            color: colors[resultIndex % colors.length],
            lineWidth: 2,
            title: `结果 #${resultIndex + 1}`,
        });

        // 准备结果数据
        const resultData = result.data.map((item, index) => ({
            time: index, // 使用索引作为时间，以便对齐所有序列
            value: item.close
        }));

        // 设置结果数据
        resultSeries.setData(resultData);
    });

    // 调整可见范围
    overlayChart.timeScale().fitContent();

    // 添加图例
    const legend = document.createElement('div');
    legend.className = 'd-flex flex-wrap justify-content-center mt-3';
    legend.innerHTML = `
        <div class="me-4 mb-2"><span class="legend-marker" style="background-color: #000000;"></span> 输入数据</div>
    `;

    searchResults.forEach((result, resultIndex) => {
        legend.innerHTML += `
            <div class="me-4 mb-2"><span class="legend-marker" style="background-color: ${colors[resultIndex % colors.length]};"></span> 结果 #${resultIndex + 1}</div>
        `;
    });

    chartContainer.parentNode.appendChild(legend);

    // 添加响应式调整
    window.addEventListener('resize', () => {
        overlayChart.applyOptions({ width: chartContainer.clientWidth });
    });
}

// 解析CSV数据
function parseCSV(csvData) {
    const lines = csvData.split('\n');
    const headers = lines[0].split(',');
    const result = [];

    // 检查必要的列
    const dateIndex = headers.indexOf('date');
    const openIndex = headers.indexOf('open');
    const highIndex = headers.indexOf('high');
    const lowIndex = headers.indexOf('low');
    const closeIndex = headers.indexOf('close');
    const volumeIndex = headers.indexOf('volume');

    if (dateIndex === -1 || openIndex === -1 || highIndex === -1 || lowIndex === -1 || closeIndex === -1) {
        throw new Error('CSV文件缺少必要的列: date, open, high, low, close');
    }

    // 解析数据行
    for (let i = 1; i < lines.length; i++) {
        if (!lines[i].trim()) continue; // 跳过空行

        const values = lines[i].split(',');

        result.push({
            date: values[dateIndex],
            open: parseFloat(values[openIndex]),
            high: parseFloat(values[highIndex]),
            low: parseFloat(values[lowIndex]),
            close: parseFloat(values[closeIndex]),
            volume: volumeIndex !== -1 ? parseFloat(values[volumeIndex]) : null
        });
    }

    return result;
}

// 解析JSON数据
function parseJSON(jsonData) {
    try {
        const data = JSON.parse(jsonData);

        // 验证数据格式
        if (!Array.isArray(data)) {
            throw new Error('数据必须是数组格式');
        }

        // 验证每个项目
        data.forEach((item, index) => {
            if (!item.date || !item.open || !item.high || !item.low || !item.close) {
                throw new Error(`第${index + 1}项缺少必要的字段: date, open, high, low, close`);
            }
        });

        return data;
    } catch (error) {
        if (error instanceof SyntaxError) {
            throw new Error('JSON格式错误: ' + error.message);
        }
        throw error;
    }
}

// 获取股票数据
async function fetchStockData(ticker, startDate, endDate, interval = '1d', period = '1mo') {
    try {
        // 先尝试使用datahub的candlestick接口
        let url = `${API_BASE_URL}/v1/datahub/candlestick?label=${ticker}&interval=${interval}&period=${period}`;

        console.log(`正在获取股票数据，URL: ${url}`);

        // 调用API
        let response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (response.ok) {
            const data = await response.json();

            if (!data || !Array.isArray(data)) {
                throw new Error('返回数据格式错误');
            }

            // 将数据转换为我们需要的格式
            const formattedData = data.map(item => {
                // 根据不同的时间周期处理时间戳
                let dateStr;
                if (interval.includes('m')) {
                    // 分钟级别数据使用原始时间戳
                    dateStr = new Date(item.time * 1000).toISOString().split('T')[0] + ' ' +
                              new Date(item.time * 1000).toISOString().split('T')[1].substring(0, 5);
                } else {
                    // 日线及以上级别使用天数时间戳
                    dateStr = new Date(item.time * 86400 * 1000).toISOString().split('T')[0];
                }

                return {
                    date: dateStr,
                    open: item.open,
                    high: item.high,
                    low: item.low,
                    close: item.close,
                    volume: item.volume
                };
            });

            return formattedData;
        }

        // 如果第一个API失败，尝试使用旧的API端点
        url = `${API_BASE_URL}/stock/${ticker}/candlestick?interval=${interval}&period=${period}`;
        console.log(`尝试使用旧API，URL: ${url}`);

        response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (response.ok) {
            const data = await response.json();

            if (!data || !Array.isArray(data)) {
                throw new Error('返回数据格式错误');
            }

            // 将数据转换为我们需要的格式
            const formattedData = data.map(item => {
                // 根据不同的时间周期处理时间戳
                let dateStr;
                if (interval.includes('m')) {
                    // 分钟级别数据使用原始时间戳
                    dateStr = new Date(item.time * 1000).toISOString().split('T')[0] + ' ' +
                              new Date(item.time * 1000).toISOString().split('T')[1].substring(0, 5);
                } else {
                    // 日线及以上级别使用天数时间戳
                    dateStr = new Date(item.time * 86400 * 1000).toISOString().split('T')[0];
                }

                return {
                    date: dateStr,
                    open: item.open,
                    high: item.high,
                    low: item.low,
                    close: item.close,
                    volume: item.volume
                };
            });

            return formattedData;
        }

        // 如果两个API都失败，尝试使用旧的history接口
        url = `${API_BASE_URL}/stock/${ticker}/history?start_date=${startDate}&end_date=${endDate}`;
        console.log(`尝试使用history接口，URL: ${url}`);

        response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const historyData = await response.json();

        if (!historyData || !historyData.data || !Array.isArray(historyData.data)) {
            throw new Error('返回数据格式错误');
        }

        return historyData.data;
    } catch (error) {
        console.error('获取股票数据失败:', error);
        throw error;
    }
}

// 模拟生成股票数据
function generateMockStockData(length = 30) {
    const result = [];
    const today = new Date();
    let price = Math.random() * 500 + 100; // 初始价格

    for (let i = length - 1; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);

        // 生成随机涨跌
        const change = (Math.random() - 0.5) * 5;
        const open = price;
        const close = price + change;
        const high = Math.max(open, close) + Math.random() * 2;
        const low = Math.min(open, close) - Math.random() * 2;
        const volume = Math.floor(Math.random() * 10000000) + 1000000;

        result.push({
            date: date.toISOString().split('T')[0],
            open: parseFloat(open.toFixed(2)),
            high: parseFloat(high.toFixed(2)),
            low: parseFloat(low.toFixed(2)),
            close: parseFloat(close.toFixed(2)),
            volume: volume
        });

        price = close; // 下一天的开盘价为前一天的收盘价
    }

    return result;
}

// 执行搜索
async function performSearch() {
    try {
        // 获取输入参数
        const inputMethod = document.getElementById('inputMethod').value;
        const sequenceLength = parseInt(document.getElementById('sequenceLength').value);
        const topK = parseInt(document.getElementById('topK').value);
        const similarityMetric = document.getElementById('similarityMetric').value;

        // 验证参数
        if (isNaN(sequenceLength) || sequenceLength < 5 || sequenceLength > 100) {
            alert('序列长度必须在 5 到 100 之间');
            return;
        }

        if (isNaN(topK) || topK < 1 || topK > 10) {
            alert('返回结果数量必须在 1 到 10 之间');
            return;
        }

        // 显示加载状态
        document.getElementById('searchBtn').disabled = true;
        document.getElementById('searchBtn').innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 搜索中...';
        document.getElementById('searchStats').innerHTML = '<div class="text-center py-5"><div class="spinner-border" role="status"></div><p class="mt-3">正在检索相似序列...</p></div>';

        // 获取输入数据
        let inputData = null;

        if (inputMethod === 'ticker') {
            // 从股票代码获取数据
            const ticker = document.getElementById('tickerInput').value.trim();
            const interval = document.getElementById('klineInterval').value;
            const period = document.getElementById('klinePeriod').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!ticker) {
                alert('请输入股票代码');
                resetSearchButton();
                return;
            }

            if (!startDate || !endDate) {
                alert('请选择日期范围');
                resetSearchButton();
                return;
            }

            try {
                // 使用选择的K线周期获取数据
                inputData = await fetchStockData(ticker, startDate, endDate, interval, period);
            } catch (error) {
                console.error('获取股票数据失败:', error);

                // 使用模拟数据
                alert(`获取股票数据失败: ${error.message}\n将使用模拟数据代替`);
                inputData = generateMockCandlestickData(ticker, interval);
            }
        } else if (inputMethod === 'manual') {
            // 从手动输入获取数据
            const jsonData = document.getElementById('manualInput').value.trim();

            if (!jsonData) {
                alert('请输入K线数据');
                resetSearchButton();
                return;
            }

            try {
                inputData = parseJSON(jsonData);
            } catch (error) {
                alert(`解析JSON数据失败: ${error.message}`);
                resetSearchButton();
                return;
            }
        } else if (inputMethod === 'upload') {
            // 从上传的CSV文件获取数据
            const fileInput = document.getElementById('csvFile');

            if (!fileInput.files || fileInput.files.length === 0) {
                alert('请选择CSV文件');
                resetSearchButton();
                return;
            }

            const file = fileInput.files[0];
            const reader = new FileReader();

            try {
                const csvData = await new Promise((resolve, reject) => {
                    reader.onload = event => resolve(event.target.result);
                    reader.onerror = error => reject(error);
                    reader.readAsText(file);
                });

                inputData = parseCSV(csvData);
            } catch (error) {
                alert(`解析CSV文件失败: ${error.message}`);
                resetSearchButton();
                return;
            }
        }

        // 验证数据长度
        if (!inputData || inputData.length < sequenceLength) {
            alert(`输入数据长度不足 ${sequenceLength} 个数据点`);
            resetSearchButton();
            return;
        }

        // 如果数据超过指定长度，取最近的N个数据点
        if (inputData.length > sequenceLength) {
            inputData = inputData.slice(inputData.length - sequenceLength);
        }

        // 更新输入图表
        updateInputChart(inputData);

        // 存储当前输入数据
        currentInputData = inputData;

        // 调用时间序列检索API
        try {
            // 构建 API URL
            let url = `${API_BASE_URL}/tsrag/search`;

            // 准备请求数据
            const requestData = {
                input_data: inputData,
                top_k: topK,
                similarity_metric: similarityMetric
            };

            // 调用API
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`API错误: ${response.status}`);
            }

            const data = await response.json();

            // 处理API响应
            if (data && data.results && Array.isArray(data.results)) {
                // 存储搜索结果
                searchResults = data.results;

                // 更新结果图表
                updateResultCharts(searchResults);

                // 更新相似度表格
                updateSimilarityTable(searchResults);

                // 更新搜索统计
                updateSearchStats(data.stats);

                // 显示结果区域
                document.getElementById('resultsSection').style.display = 'block';
            } else {
                throw new Error('返回数据格式错误');
            }
        } catch (error) {
            console.error('时间序列检索失败:', error);

            // 使用模拟数据
            alert(`时间序列检索失败: ${error.message}\n将使用模拟数据代替`);

            // 生成模拟结果
            searchResults = generateMockSearchResults(inputData, topK);

            // 更新结果图表
            updateResultCharts(searchResults);

            // 更新相似度表格
            updateSimilarityTable(searchResults);

            // 更新搜索统计
            updateSearchStats({
                search_time_ms: Math.floor(Math.random() * 500) + 100,
                database_size: Math.floor(Math.random() * 10000) + 5000,
                index_type: 'FAISS (L2)',
                dimension: 128
            });

            // 显示结果区域
            document.getElementById('resultsSection').style.display = 'block';
        }
    } catch (error) {
        console.error('执行搜索失败:', error);
        alert(`执行搜索失败: ${error.message}`);
    } finally {
        // 重置搜索按钮
        resetSearchButton();
    }
}

// 重置搜索按钮
function resetSearchButton() {
    document.getElementById('searchBtn').disabled = false;
    document.getElementById('searchBtn').innerHTML = '开始检索';
}

// 更新搜索统计
function updateSearchStats(stats) {
    const container = document.getElementById('searchStats');

    if (stats) {
        container.innerHTML = `
            <div class="row">
                <div class="col-md-6 mb-2">
                    <div class="stat-item">
                        <div class="stat-label">检索时间</div>
                        <div class="stat-value">${stats.search_time_ms} ms</div>
                    </div>
                </div>
                <div class="col-md-6 mb-2">
                    <div class="stat-item">
                        <div class="stat-label">数据库大小</div>
                        <div class="stat-value">${stats.database_size} 条</div>
                    </div>
                </div>
                <div class="col-md-6 mb-2">
                    <div class="stat-item">
                        <div class="stat-label">索引类型</div>
                        <div class="stat-value">${stats.index_type}</div>
                    </div>
                </div>
                <div class="col-md-6 mb-2">
                    <div class="stat-item">
                        <div class="stat-label">特征维度</div>
                        <div class="stat-value">${stats.dimension}</div>
                    </div>
                </div>
            </div>
        `;
    } else {
        container.innerHTML = '<div class="text-center py-5"><p class="text-muted">统计数据不可用</p></div>';
    }
}

// 更新相似度表格
function updateSimilarityTable(results) {
    const tableBody = document.getElementById('similarityTable');
    tableBody.innerHTML = '';

    results.forEach((result, index) => {
        const row = document.createElement('tr');

        // 计算涨跌幅
        const firstClose = result.data[0].close;
        const lastClose = result.data[result.data.length - 1].close;
        const changePercent = ((lastClose - firstClose) / firstClose * 100).toFixed(2);
        const changeClass = parseFloat(changePercent) >= 0 ? 'text-success' : 'text-danger';

        // 计算日期范围
        const startDate = new Date(result.data[0].date).toLocaleDateString();
        const endDate = new Date(result.data[result.data.length - 1].date).toLocaleDateString();

        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${result.ticker || '未知'}</td>
            <td>${startDate} - ${endDate}</td>
            <td>${result.similarity.toFixed(4)}</td>
            <td class="${changeClass}">${changePercent}%</td>
            <td>
                <span class="badge ${result.future_trend > 0 ? 'bg-success' : 'bg-danger'}">
                    ${result.future_trend > 0 ? '上涨' : '下跌'} ${Math.abs(result.future_trend).toFixed(2)}%
                </span>
            </td>
        `;

        tableBody.appendChild(row);
    });
}

// 自动加载默认股票数据
async function loadDefaultStockData(ticker) {
    try {
        // 获取K线周期和时间范围
        const interval = document.getElementById('klineInterval').value;
        const period = document.getElementById('klinePeriod').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        // 构建 API URL - 使用datahub的candlestick接口
        const url = `${API_BASE_URL}/v1/datahub/candlestick?label=${ticker}&interval=${interval}&period=${period}`;

        console.log(`正在获取K线图数据，URL: ${url}`);

        // 调用API
        const response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const data = await response.json();

        if (!data || !Array.isArray(data)) {
            throw new Error('返回数据格式错误');
        }

        // 将数据转换为我们需要的格式
        const formattedData = data.map(item => {
            // 根据不同的时间周期处理时间戳
            let dateStr;
            if (interval.includes('m')) {
                // 分钟级别数据使用原始时间戳
                dateStr = new Date(item.time * 1000).toISOString().split('T')[0] + ' ' +
                          new Date(item.time * 1000).toISOString().split('T')[1].substring(0, 5);
            } else {
                // 日线及以上级别使用天数时间戳
                dateStr = new Date(item.time * 86400 * 1000).toISOString().split('T')[0];
            }

            return {
                date: dateStr,
                open: item.open,
                high: item.high,
                low: item.low,
                close: item.close,
                volume: item.volume
            };
        });

        // 更新输入图表
        updateInputChart(formattedData);

        // 存储当前输入数据
        currentInputData = formattedData;

        console.log(`成功加载${ticker}的K线数据，共${formattedData.length}条记录，周期: ${interval}`);
    } catch (error) {
        console.error('获取默认股票数据失败:', error);

        // 如果失败，尝试使用旧的API端点
        try {
            const interval = document.getElementById('klineInterval').value;
            const period = document.getElementById('klinePeriod').value;

            const fallbackUrl = `${API_BASE_URL}/stock/${ticker}/candlestick?interval=${interval}&period=${period}`;
            const fallbackResponse = await fetch(fallbackUrl, {
                headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
            });

            if (fallbackResponse.ok) {
                const fallbackData = await fallbackResponse.json();

                // 将数据转换为我们需要的格式
                const formattedData = fallbackData.map(item => {
                    // 根据不同的时间周期处理时间戳
                    let dateStr;
                    if (interval.includes('m')) {
                        // 分钟级别数据使用原始时间戳
                        dateStr = new Date(item.time * 1000).toISOString().split('T')[0] + ' ' +
                                  new Date(item.time * 1000).toISOString().split('T')[1].substring(0, 5);
                    } else {
                        // 日线及以上级别使用天数时间戳
                        dateStr = new Date(item.time * 86400 * 1000).toISOString().split('T')[0];
                    }

                    return {
                        date: dateStr,
                        open: item.open,
                        high: item.high,
                        low: item.low,
                        close: item.close,
                        volume: item.volume
                    };
                });

                // 更新输入图表
                updateInputChart(formattedData);

                // 存储当前输入数据
                currentInputData = formattedData;

                console.log(`成功使用旧API获取${ticker}的K线数据，共${formattedData.length}条记录，周期: ${interval}`);
                return;
            }
        } catch (fallbackError) {
            console.error('备选API也失败:', fallbackError);
        }

        // 如果两个API都失败，使用模拟数据
        console.log('使用模拟数据代替');
        const interval = document.getElementById('klineInterval').value;
        const mockData = generateMockCandlestickData(ticker, interval);
        updateInputChart(mockData);
        currentInputData = mockData;
    }
}

// 生成模拟的K线图数据
function generateMockCandlestickData(ticker, interval = '1d') {
    console.log(`为${ticker}生成模拟的K线图数据，周期: ${interval}`);
    const data = [];
    const now = new Date();

    // 根据不同的周期设置数据点数量和时间间隔
    let numPoints = 30;
    let timeUnit = 'day';
    let timeStep = 1;

    if (interval === '1m') {
        numPoints = 60;
        timeUnit = 'minute';
        timeStep = 1;
        now.setMinutes(0, 0, 0);
    } else if (interval === '5m') {
        numPoints = 60;
        timeUnit = 'minute';
        timeStep = 5;
        now.setMinutes(0, 0, 0);
    } else if (interval === '15m') {
        numPoints = 60;
        timeUnit = 'minute';
        timeStep = 15;
        now.setMinutes(0, 0, 0);
    } else if (interval === '30m') {
        numPoints = 48;
        timeUnit = 'minute';
        timeStep = 30;
        now.setMinutes(0, 0, 0);
    } else if (interval === '60m' || interval === '1h') {
        numPoints = 48;
        timeUnit = 'hour';
        timeStep = 1;
        now.setMinutes(0, 0, 0);
    } else if (interval === '1d') {
        numPoints = 30;
        timeUnit = 'day';
        timeStep = 1;
        now.setHours(0, 0, 0, 0);
    } else if (interval === '1wk') {
        numPoints = 20;
        timeUnit = 'week';
        timeStep = 1;
        now.setHours(0, 0, 0, 0);
    }

    // 初始价格，假设是期货
    let price = 3000 + Math.random() * 1000;

    for (let i = numPoints - 1; i >= 0; i--) {
        const date = new Date(now);

        // 根据时间单位调整日期
        if (timeUnit === 'minute') {
            date.setMinutes(date.getMinutes() - i * timeStep);
        } else if (timeUnit === 'hour') {
            date.setHours(date.getHours() - i * timeStep);
        } else if (timeUnit === 'day') {
            date.setDate(date.getDate() - i * timeStep);
        } else if (timeUnit === 'week') {
            date.setDate(date.getDate() - i * 7 * timeStep);
        }

        // 生成随机涨跌，分钟级别的波动幅度小一些
        let changeScale = 1.0;
        if (timeUnit === 'minute') {
            changeScale = 0.2;
        } else if (timeUnit === 'hour') {
            changeScale = 0.5;
        }

        const change = (Math.random() - 0.5) * 50 * changeScale;
        const open = price;
        const close = price + change;
        const high = Math.max(open, close) + Math.random() * 20 * changeScale;
        const low = Math.min(open, close) - Math.random() * 20 * changeScale;
        const volume = Math.floor(Math.random() * 10000000) + 1000000;

        // 格式化日期
        let dateStr;
        if (timeUnit === 'minute' || timeUnit === 'hour') {
            dateStr = date.toISOString().split('T')[0] + ' ' +
                     date.toTimeString().substring(0, 5);
        } else {
            dateStr = date.toISOString().split('T')[0];
        }

        data.push({
            date: dateStr,
            open: parseFloat(open.toFixed(2)),
            high: parseFloat(high.toFixed(2)),
            low: parseFloat(low.toFixed(2)),
            close: parseFloat(close.toFixed(2)),
            volume: volume
        });

        price = close; // 下一个周期的开盘价为前一个周期的收盘价
    }

    return data;
}

// 生成模拟搜索结果
function generateMockSearchResults(inputData, topK) {
    const results = [];

    for (let i = 0; i < topK; i++) {
        // 生成相似度分数，越靠前越高
        const similarity = 1 - (i * 0.05 + Math.random() * 0.05);

        // 生成股票代码
        const tickers = ['600000', '601398', '601988', '601288', '601628', '601318', '600036', '600519', '000858', 'IF9999.SF'];
        const ticker = tickers[Math.floor(Math.random() * tickers.length)];

        // 生成相似的K线数据，但有一定的变化
        const data = inputData.map(item => {
            const variationPercent = (Math.random() - 0.5) * 0.1; // 添加小的变化
            const basePrice = item.close * (1 + variationPercent);

            return {
                date: item.date,
                open: basePrice * (1 + (Math.random() - 0.5) * 0.01),
                high: basePrice * (1 + Math.random() * 0.01),
                low: basePrice * (1 - Math.random() * 0.01),
                close: basePrice,
                volume: item.volume ? item.volume * (0.8 + Math.random() * 0.4) : Math.floor(Math.random() * 10000000) + 1000000
            };
        });

        // 生成未来走势
        const futureTrend = (Math.random() - 0.4) * 10; // 偏向上涨的概率更大

        // 生成未来数据
        const futureData = [];
        if (Math.random() > 0.3) { // 70%的概率有未来数据
            let lastClose = data[data.length - 1].close;

            for (let j = 1; j <= 5; j++) {
                const date = new Date(data[data.length - 1].date);
                date.setDate(date.getDate() + j);

                const dailyChange = (futureTrend / 100 / 5) + (Math.random() - 0.5) * 0.01;
                lastClose = lastClose * (1 + dailyChange);

                futureData.push({
                    date: date.toISOString().split('T')[0],
                    close: lastClose
                });
            }
        }

        results.push({
            ticker: ticker,
            similarity: similarity,
            data: data,
            future_data: futureData,
            future_trend: futureTrend
        });
    }

    return results;
}
