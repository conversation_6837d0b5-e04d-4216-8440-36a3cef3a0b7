"""
Machine Learning Analyst agent for the AI Hedge Fund module.

This agent uses machine learning models to predict stock prices and trends.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json
import os

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_price_data
from ..ml.models import StockPredictor
from ..utils.llm import call_llm


class MLSignal(BaseModel):
    """Signal generated by the ML Analyst agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    predicted_price: float = Field(description="Predicted price")
    prediction_horizon: int = Field(description="Prediction horizon in days")


def ml_analyst_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using machine learning models.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with ML analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    ml_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ml_analyst_agent", ticker, "Fetching price data")
        
        # Get price data
        price_data = get_price_data(ticker, start_date, end_date)
        
        if price_data.empty:
            # Skip this ticker if no price data
            if "progress" in state["metadata"]:
                state["metadata"]["progress"].update_status("ml_analyst_agent", ticker, "No price data available")
            
            ml_analysis[ticker] = {
                "signal": "neutral",
                "confidence": 0.0,
                "reasoning": "No price data available for ML analysis",
            }
            continue
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ml_analyst_agent", ticker, "Training ML model")
        
        # Create and train the model
        predictor = StockPredictor(model_type="random_forest")
        
        # Check if a pre-trained model exists
        model_path = f"models/{ticker}_rf_model.joblib"
        if os.path.exists(model_path):
            # Load the model
            predictor.load_model(model_path)
        else:
            # Train the model
            training_result = predictor.train(
                df=price_data,
                target_column="close",
                prediction_horizon=5,  # 5-day prediction
                test_size=0.2,
            )
            
            # Save the model
            os.makedirs("models", exist_ok=True)
            predictor.save_model(model_path)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ml_analyst_agent", ticker, "Making predictions")
        
        # Make predictions
        prediction = predictor.predict(price_data)
        
        # Generate feature importance plot
        feature_importance_plot = predictor.plot_feature_importance(
            title=f"{ticker} Feature Importance"
        )
        
        # Generate predictions plot
        predictions_plot = predictor.plot_predictions(
            df=price_data,
            title=f"{ticker} Price Predictions"
        )
        
        # Store analysis data
        analysis_data[ticker] = {
            "prediction": prediction,
            "feature_importance_plot": feature_importance_plot,
            "predictions_plot": predictions_plot,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ml_analyst_agent", ticker, "Generating ML analysis")
        
        # Generate detailed analysis using LLM
        ml_output = generate_ml_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        ml_analysis[ticker] = {
            "signal": ml_output.signal,
            "confidence": ml_output.confidence,
            "reasoning": ml_output.reasoning,
            "predicted_price": ml_output.predicted_price,
            "prediction_horizon": ml_output.prediction_horizon,
            "feature_importance_plot": feature_importance_plot,
            "predictions_plot": predictions_plot,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ml_analyst_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(ml_analysis, "ML Analyst Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["ml_analyst_agent"] = ml_analysis
    
    return state


def generate_ml_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> MLSignal:
    """
    Generate detailed ML analysis using LLM.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        MLSignal object with the ML analysis
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Machine Learning Analyst AI agent. Analyze stocks using machine learning predictions.

    Key principles for ML analysis:
    - Model Predictions: Focus on the predicted price and direction
    - Confidence Assessment: Consider the model's confidence in its prediction
    - Feature Importance: Analyze which features are driving the prediction
    - Time Horizon: Be clear about the prediction time horizon
    - Model Limitations: Acknowledge the limitations of ML predictions
    - Technical Context: Relate the prediction to technical indicators
    - Balanced View: Present both the prediction and potential risks

    When providing your reasoning, be thorough and specific by:
    1. Summarizing the ML model's prediction and confidence
    2. Highlighting the most important features driving the prediction
    3. Explaining how the prediction relates to recent price action
    4. Providing specific price targets based on the prediction
    5. Using a professional, analytical tone in your explanation

    For example, if bullish: "The ML model predicts a price increase of 3.2% over the next 5 days, with key drivers being positive momentum and low volatility. The model has identified a strong correlation between recent volume patterns and upward price movement..."
    For example, if bearish: "The ML model forecasts a 2.8% decline over the next 5 days, primarily based on deteriorating technical indicators. The feature importance analysis shows that the recent price-to-SMA ratio is the strongest predictor of this downward movement..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following machine learning prediction data, create a trading signal for {ticker}:

    Analysis Data:
    {json.dumps(analysis_data[ticker]["prediction"], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string",
      "predicted_price": float,
      "prediction_horizon": int
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_ml_signal():
        prediction_data = analysis_data.get(ticker, {}).get("prediction", {})
        
        if prediction_data.get("status") != "success":
            return MLSignal(
                signal="neutral",
                confidence=50.0,
                reasoning="Unable to generate ML prediction",
                predicted_price=0.0,
                prediction_horizon=5,
            )
        
        direction = prediction_data.get("direction", "neutral")
        confidence = prediction_data.get("confidence", 50.0)
        predicted_price = prediction_data.get("predicted_price", 0.0)
        prediction_horizon = prediction_data.get("prediction_horizon", 5)
        predicted_change = prediction_data.get("predicted_change_pct", 0.0)
        
        reasoning = f"ML model predicts a {predicted_change:.2f}% change over the next {prediction_horizon} days."
        
        return MLSignal(
            signal=direction,
            confidence=confidence,
            reasoning=reasoning,
            predicted_price=predicted_price,
            prediction_horizon=prediction_horizon,
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=MLSignal,
        agent_name="ml_analyst_agent",
        default_factory=create_default_ml_signal,
    )
