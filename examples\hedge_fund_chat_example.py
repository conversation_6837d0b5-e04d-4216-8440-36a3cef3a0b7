"""
Example of integrating the AI Hedge Fund module with a chat interface.

This file demonstrates how to integrate the AI Hedge Fund module with a simple
chat interface.
"""

import sys
import os
import re
from typing import Dict, List, Any, Optional, Callable

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.ai_hedge_fund.chat_integration import create_chat_handler


class SimpleChatBot:
    """
    Simple chat bot for demonstration purposes.
    
    This class provides a simple chat interface for demonstrating the
    integration of the AI Hedge Fund module.
    """
    
    def __init__(self):
        """Initialize the chat bot."""
        self.handlers: List[Callable[[str], Optional[str]]] = []
        self.modules: Dict[str, Any] = {}
    
    def register_handler(self, handler: Callable[[str], Optional[str]]) -> None:
        """
        Register a message handler.
        
        Args:
            handler: Function that takes a message and returns a response
        """
        self.handlers.append(handler)
    
    def register_module(self, name: str, module: Any) -> None:
        """
        Register a module.
        
        Args:
            name: Name of the module
            module: Module instance
        """
        self.modules[name] = module
        if hasattr(module, "handle_message"):
            self.register_handler(module.handle_message)
    
    def handle_message(self, message: str) -> str:
        """
        Handle a chat message.
        
        Args:
            message: The chat message to handle
            
        Returns:
            Response message
        """
        # Check if the message is asking for help
        if re.search(r"(帮助|help|指令|命令|功能|你能做什么|你会什么)", message):
            return self.get_help()
        
        # Try each handler
        for handler in self.handlers:
            response = handler(message)
            if response:
                return response
        
        # Default response
        return "抱歉，我不明白您的意思。请尝试输入"帮助"查看可用命令。"
    
    def get_help(self) -> str:
        """
        Get help information.
        
        Returns:
            Help information
        """
        help_text = """
🤖 智能助手使用说明

我是一个智能助手，可以帮助您完成各种任务。目前我支持以下功能：

1. 股票分析和投资建议
   - 分析股票：AAPL, MSFT, GOOGL
   - 回测股票：AAPL, MSFT, GOOGL
   - 输入"股票帮助"查看详细使用说明

2. 更多功能正在开发中...

请输入您的问题或命令，我会尽力帮助您。
        """
        
        return help_text
    
    def run(self) -> None:
        """Run the chat bot."""
        print("🤖 智能助手已启动，输入'退出'结束对话")
        print("输入'帮助'查看可用命令")
        
        while True:
            user_input = input("\n> ")
            
            if user_input.lower() in ["退出", "exit", "quit"]:
                print("再见！")
                break
            
            response = self.handle_message(user_input)
            print(response)


def main():
    """Main function for the example."""
    # Create a chat bot
    chat_bot = SimpleChatBot()
    
    # Create a hedge fund chat handler
    hedge_fund_handler = create_chat_handler()
    
    # Register the handler with the chat bot
    chat_bot.register_module("hedge_fund", hedge_fund_handler)
    
    # Run the chat bot
    chat_bot.run()


if __name__ == "__main__":
    main()
