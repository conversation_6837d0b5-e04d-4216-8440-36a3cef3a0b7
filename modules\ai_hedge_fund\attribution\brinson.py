"""
Brinson attribution model for the AI Hedge Fund module.

This file provides functions for Brinson-based performance attribution analysis.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import matplotlib.pyplot as plt
import io
import base64

from ..tools.api import get_price_data, get_sector_data


def brinson_attribution(
    portfolio: Dict[str, Any],
    benchmark: Optional[str] = "SPY",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    attribution_level: str = "sector",
) -> Dict[str, Any]:
    """
    Perform Brinson-based performance attribution analysis.
    
    Args:
        portfolio: Portfolio dictionary with positions and weights
        benchmark: Benchmark ticker symbol or dictionary with weights
        start_date: Start date for analysis (YYYY-MM-DD)
        end_date: End date for analysis (YYYY-MM-DD)
        attribution_level: Level of attribution ('sector', 'industry', 'security')
        
    Returns:
        Dictionary with Brinson attribution analysis results
    """
    # Initialize results
    results = {
        "status": "success",
        "message": "Brinson attribution analysis completed successfully",
        "portfolio_return": 0.0,
        "benchmark_return": 0.0,
        "active_return": 0.0,
        "allocation_effect": 0.0,
        "selection_effect": 0.0,
        "interaction_effect": 0.0,
        "attribution_details": {},
    }
    
    # Extract portfolio positions
    positions = portfolio.get("positions", {})
    
    if not positions:
        return {
            "status": "error",
            "message": "Portfolio has no positions",
        }
    
    # Calculate portfolio weights
    total_value = portfolio.get("total_value", 0.0)
    
    if total_value <= 0:
        return {
            "status": "error",
            "message": "Invalid portfolio value",
        }
    
    portfolio_weights = {}
    tickers = []
    
    for ticker, position in positions.items():
        # Skip cash positions
        if ticker == "cash":
            continue
        
        # Calculate position value
        long_value = position.get("long_value", 0.0)
        short_value = position.get("short_value", 0.0)
        position_value = long_value - short_value
        
        # Calculate weight
        weight = position_value / total_value
        
        portfolio_weights[ticker] = weight
        tickers.append(ticker)
    
    # Get benchmark weights
    benchmark_weights = {}
    
    if isinstance(benchmark, dict):
        # Benchmark is provided as a dictionary of weights
        benchmark_weights = benchmark
    elif isinstance(benchmark, str):
        # Benchmark is provided as a ticker symbol
        # For simplicity, we'll use a predefined sector breakdown for common benchmarks
        if benchmark == "SPY":
            # Get S&P 500 constituents and weights
            # In a real implementation, this would be fetched from an API or database
            # For now, we'll use a simplified approach
            benchmark_weights = {
                "AAPL": 0.07,
                "MSFT": 0.06,
                "AMZN": 0.04,
                "GOOGL": 0.02,
                "GOOG": 0.02,
                "META": 0.02,
                "TSLA": 0.02,
                "NVDA": 0.02,
                "BRK.B": 0.02,
                "JPM": 0.01,
                # Add more constituents as needed
            }
        elif benchmark == "QQQ":
            # Get Nasdaq-100 constituents and weights
            benchmark_weights = {
                "AAPL": 0.12,
                "MSFT": 0.10,
                "AMZN": 0.07,
                "GOOGL": 0.04,
                "GOOG": 0.04,
                "META": 0.04,
                "TSLA": 0.04,
                "NVDA": 0.04,
                "ADBE": 0.02,
                "PYPL": 0.02,
                # Add more constituents as needed
            }
    
    # Get returns for portfolio and benchmark securities
    security_returns = {}
    
    all_tickers = list(set(tickers + list(benchmark_weights.keys())))
    
    for ticker in all_tickers:
        price_data = get_price_data(ticker, start_date, end_date)
        
        if price_data.empty:
            continue
        
        # Calculate return
        start_price = price_data["close"].iloc[0]
        end_price = price_data["close"].iloc[-1]
        ticker_return = (end_price / start_price - 1)
        
        security_returns[ticker] = ticker_return
    
    # Get classification data based on attribution level
    classifications = {}
    
    if attribution_level == "sector":
        # Get sector data for all securities
        for ticker in all_tickers:
            sector = get_sector_data(ticker)
            if sector:
                classifications[ticker] = sector
    elif attribution_level == "industry":
        # Get industry data for all securities
        # In a real implementation, this would be fetched from an API or database
        # For now, we'll use sectors as a proxy
        for ticker in all_tickers:
            sector = get_sector_data(ticker)
            if sector:
                classifications[ticker] = sector
    elif attribution_level == "security":
        # Use securities themselves as the classification
        for ticker in all_tickers:
            classifications[ticker] = ticker
    
    # Calculate portfolio and benchmark returns by classification
    portfolio_class_weights = {}
    benchmark_class_weights = {}
    portfolio_class_returns = {}
    benchmark_class_returns = {}
    
    # Calculate portfolio classification weights and returns
    for ticker, weight in portfolio_weights.items():
        classification = classifications.get(ticker, "Unknown")
        ticker_return = security_returns.get(ticker, 0.0)
        
        if classification in portfolio_class_weights:
            portfolio_class_weights[classification] += weight
            portfolio_class_returns[classification] += weight * ticker_return
        else:
            portfolio_class_weights[classification] = weight
            portfolio_class_returns[classification] = weight * ticker_return
    
    # Normalize portfolio classification returns
    for classification in portfolio_class_returns:
        if portfolio_class_weights[classification] > 0:
            portfolio_class_returns[classification] /= portfolio_class_weights[classification]
    
    # Calculate benchmark classification weights and returns
    for ticker, weight in benchmark_weights.items():
        classification = classifications.get(ticker, "Unknown")
        ticker_return = security_returns.get(ticker, 0.0)
        
        if classification in benchmark_class_weights:
            benchmark_class_weights[classification] += weight
            benchmark_class_returns[classification] += weight * ticker_return
        else:
            benchmark_class_weights[classification] = weight
            benchmark_class_returns[classification] = weight * ticker_return
    
    # Normalize benchmark classification returns
    for classification in benchmark_class_returns:
        if benchmark_class_weights[classification] > 0:
            benchmark_class_returns[classification] /= benchmark_class_weights[classification]
    
    # Calculate portfolio and benchmark total returns
    portfolio_return = sum(portfolio_weights.get(ticker, 0) * security_returns.get(ticker, 0) for ticker in portfolio_weights)
    benchmark_return = sum(benchmark_weights.get(ticker, 0) * security_returns.get(ticker, 0) for ticker in benchmark_weights)
    
    # Calculate active return
    active_return = portfolio_return - benchmark_return
    
    # Calculate Brinson attribution effects
    allocation_effect_total = 0.0
    selection_effect_total = 0.0
    interaction_effect_total = 0.0
    attribution_details = {}
    
    for classification in set(list(portfolio_class_weights.keys()) + list(benchmark_class_weights.keys())):
        portfolio_weight = portfolio_class_weights.get(classification, 0.0)
        benchmark_weight = benchmark_class_weights.get(classification, 0.0)
        portfolio_return = portfolio_class_returns.get(classification, 0.0)
        benchmark_return = benchmark_class_returns.get(classification, 0.0)
        
        # Calculate allocation effect
        allocation_effect = (portfolio_weight - benchmark_weight) * benchmark_return
        
        # Calculate selection effect
        selection_effect = benchmark_weight * (portfolio_return - benchmark_return)
        
        # Calculate interaction effect
        interaction_effect = (portfolio_weight - benchmark_weight) * (portfolio_return - benchmark_return)
        
        # Calculate total effect
        total_effect = allocation_effect + selection_effect + interaction_effect
        
        # Add to totals
        allocation_effect_total += allocation_effect
        selection_effect_total += selection_effect
        interaction_effect_total += interaction_effect
        
        # Store details
        attribution_details[classification] = {
            "portfolio_weight": portfolio_weight,
            "benchmark_weight": benchmark_weight,
            "portfolio_return": portfolio_return,
            "benchmark_return": benchmark_return,
            "allocation_effect": allocation_effect,
            "selection_effect": selection_effect,
            "interaction_effect": interaction_effect,
            "total_effect": total_effect,
        }
    
    # Store results
    results["portfolio_return"] = portfolio_return
    results["benchmark_return"] = benchmark_return
    results["active_return"] = active_return
    results["allocation_effect"] = allocation_effect_total
    results["selection_effect"] = selection_effect_total
    results["interaction_effect"] = interaction_effect_total
    results["attribution_details"] = attribution_details
    
    return results


def plot_brinson_attribution(
    attribution_result: Dict[str, Any],
    title: Optional[str] = None,
) -> str:
    """
    Plot Brinson attribution analysis results.
    
    Args:
        attribution_result: Dictionary with Brinson attribution analysis results
        title: Plot title (optional)
        
    Returns:
        Base64-encoded image of the plot
    """
    if attribution_result.get("status") != "success":
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, attribution_result.get("message", "Unknown error"), ha="center", va="center", fontsize=14)
        ax.set_title(title or "Brinson Attribution Analysis")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Extract data
    active_return = attribution_result.get("active_return", 0.0)
    allocation_effect = attribution_result.get("allocation_effect", 0.0)
    selection_effect = attribution_result.get("selection_effect", 0.0)
    interaction_effect = attribution_result.get("interaction_effect", 0.0)
    
    # Create the plot
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Plot attribution effects
    effects = ["Allocation", "Selection", "Interaction", "Active Return"]
    values = [allocation_effect, selection_effect, interaction_effect, active_return]
    colors = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728"]
    
    # Create bar chart
    bars = ax.bar(effects, values, color=colors, alpha=0.7)
    
    # Add value labels
    for bar in bars:
        height = bar.get_height()
        ax.annotate(
            f"{height:.2%}",
            xy=(bar.get_x() + bar.get_width() / 2, height),
            xytext=(0, 3 if height >= 0 else -3),
            textcoords="offset points",
            ha="center",
            va="bottom" if height >= 0 else "top",
        )
    
    # Format the plot
    ax.set_ylabel("Contribution to Active Return")
    ax.set_title(title or "Brinson Attribution Analysis")
    ax.grid(True, alpha=0.3, axis="y")
    
    # Add horizontal line at y=0
    ax.axhline(y=0, color="gray", linestyle="--", alpha=0.5)
    
    # Format y-axis as percentage
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f"{y:.1%}"))
    
    # Adjust layout
    plt.tight_layout()
    
    # Convert plot to base64-encoded image
    buffer = io.BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
    plt.close()
    
    return image_base64


def plot_attribution_details(
    attribution_result: Dict[str, Any],
    effect_type: str = "total",
    top_n: int = 10,
    title: Optional[str] = None,
) -> str:
    """
    Plot detailed attribution effects by classification.
    
    Args:
        attribution_result: Dictionary with Brinson attribution analysis results
        effect_type: Type of effect to plot ('allocation', 'selection', 'interaction', 'total')
        top_n: Number of top contributors to show
        title: Plot title (optional)
        
    Returns:
        Base64-encoded image of the plot
    """
    if attribution_result.get("status") != "success":
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, attribution_result.get("message", "Unknown error"), ha="center", va="center", fontsize=14)
        ax.set_title(title or f"{effect_type.capitalize()} Effect by Classification")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Extract attribution details
    attribution_details = attribution_result.get("attribution_details", {})
    
    if not attribution_details:
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, "No attribution details available", ha="center", va="center", fontsize=14)
        ax.set_title(title or f"{effect_type.capitalize()} Effect by Classification")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Map effect type to key
    effect_key_map = {
        "allocation": "allocation_effect",
        "selection": "selection_effect",
        "interaction": "interaction_effect",
        "total": "total_effect",
    }
    
    effect_key = effect_key_map.get(effect_type.lower(), "total_effect")
    
    # Extract effect values by classification
    classifications = []
    effect_values = []
    
    for classification, details in attribution_details.items():
        classifications.append(classification)
        effect_values.append(details.get(effect_key, 0.0))
    
    # Create DataFrame for sorting
    df = pd.DataFrame({
        "Classification": classifications,
        "Effect": effect_values,
    })
    
    # Sort by absolute effect value and take top N
    df["AbsEffect"] = df["Effect"].abs()
    df = df.sort_values("AbsEffect", ascending=False).head(top_n)
    
    # Sort by effect value for display
    df = df.sort_values("Effect")
    
    # Create the plot
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Create color map based on values
    colors = ["green" if val >= 0 else "red" for val in df["Effect"]]
    
    # Plot the effects as a horizontal bar chart
    bars = ax.barh(df["Classification"], df["Effect"], color=colors, alpha=0.7)
    
    # Add value labels
    for bar in bars:
        width = bar.get_width()
        label_x = width + 0.001 if width >= 0 else width - 0.001
        ax.text(
            label_x,
            bar.get_y() + bar.get_height() / 2,
            f"{width:.2%}",
            ha="left" if width >= 0 else "right",
            va="center",
        )
    
    # Format the plot
    ax.set_xlabel(f"{effect_type.capitalize()} Effect")
    ax.set_title(title or f"{effect_type.capitalize()} Effect by Classification")
    ax.grid(True, alpha=0.3, axis="x")
    
    # Add vertical line at x=0
    ax.axvline(x=0, color="gray", linestyle="--", alpha=0.5)
    
    # Format x-axis as percentage
    ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f"{x:.1%}"))
    
    # Adjust layout
    plt.tight_layout()
    
    # Convert plot to base64-encoded image
    buffer = io.BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
    plt.close()
    
    return image_base64
