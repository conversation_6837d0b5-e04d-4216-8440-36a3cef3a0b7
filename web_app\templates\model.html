<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型测试 - 智能体投顾助手</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
</head>
<body>
    {% include 'components/navbar.html' %}

    <div class="container mt-4">
        <!-- 欢迎区域 -->
        <div class="row mb-4" id="welcomeSection">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title">股票预测模型测试</h2>
                        <p class="card-text">本页面提供股票价格预测模型的测试功能，您可以输入股票代码，选择预测模型和参数，查看预测结果。</p>
                        <div class="d-flex">
                            <input type="text" class="form-control me-2" id="searchTicker" placeholder="输入股票代码 (例如: 600000)">
                            <button class="btn btn-primary" id="searchBtn">搜索</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 模型选择和参数设置区域 -->
            <div class="col-md-4" id="modelSettingsSection">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4>模型预测设置</h4>
                    </div>
                    <div class="card-body">
                        <form id="predictionForm">
                            <div class="mb-3">
                                <label for="modelSelect" class="form-label">选择模型</label>
                                <select class="form-select" id="modelSelect">
                                    <option value="">加载中...</option>
                                </select>
                                <div id="modelInfo" class="form-text"></div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="temperature" class="form-label">温度 (0.1-1.0)</label>
                                    <input type="number" class="form-control" id="temperature" value="0.1" min="0.1" max="1.0" step="0.1">
                                </div>
                                <div class="col-md-6">
                                    <label for="topK" class="form-label">Top-K</label>
                                    <input type="number" class="form-control" id="topK" value="1" min="1" max="10" step="1">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="atrMult" class="form-label">ATR乘数</label>
                                    <input type="number" class="form-control" id="atrMult" value="0.88" min="0.1" max="2.0" step="0.01">
                                </div>
                                <div class="col-md-6">
                                    <label for="scale" class="form-label">缩放比例</label>
                                    <input type="number" class="form-control" id="scale" value="10" min="1" max="100" step="1">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="forecastSteps" class="form-label">预测步数 (-10至10)</label>
                                <div class="input-group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="decrementForecastSteps()">−</button>
                                    <input type="number" class="form-control" id="forecastSteps" value="3" min="-10" max="10" step="1" onchange="validateForecastSteps(this)" oninput="allowNegativeInput(this)">
                                    <button type="button" class="btn btn-outline-secondary" onclick="incrementForecastSteps()">+</button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="toggleForecastStepsSign()">正/负</button>
                                </div>
                                <div class="form-text">
                                    正值表示预测未来走势，负值表示向前退回步数进行比较。
                                    <span class="text-danger">范围: -10到-1或者1到10，不能为0</span>
                                </div>
                            </div>

                            <script>
                                // 验证forecastSteps输入框
                                function validateForecastSteps(input) {
                                    const value = parseInt(input.value);
                                    if (value === 0) {
                                        alert('预测步数不能为0，请输入-10到-1或者1到10的整数');
                                        input.value = input.value < 0 ? -1 : 1; // 如果之前是负数则设为-1，否则设为1
                                    } else if (value < -10) {
                                        alert('预测步数不能小于-10');
                                        input.value = -10;
                                    } else if (value > 10) {
                                        alert('预测步数不能大于10');
                                        input.value = 10;
                                    }
                                }

                                // 允许输入负数
                                function allowNegativeInput(input) {
                                    // 如果用户输入了负号，但没有数字，则不进行验证
                                    if (input.value === '-') {
                                        return;
                                    }

                                    // 如果用户输入了负数，确保它在有效范围内
                                    const value = parseInt(input.value);
                                    if (!isNaN(value)) {
                                        if (value === 0) {
                                            // 如果用户输入了0，则根据之前的值决定是设为1还是-1
                                            const prevValue = parseFloat(input.dataset.prevValue || '3');
                                            input.value = prevValue < 0 ? -1 : 1;
                                        } else if (value < -10) {
                                            input.value = -10;
                                        } else if (value > 10) {
                                            input.value = 10;
                                        }
                                    }

                                    // 存储当前值作为下一次的参考
                                    input.dataset.prevValue = input.value;
                                }

                                // 增加forecastSteps值
                                function incrementForecastSteps() {
                                    const input = document.getElementById('forecastSteps');
                                    let value = parseInt(input.value) || 0;

                                    // 如果当前值为-1，则跳过0直接变成1
                                    if (value === -1) {
                                        value = 1;
                                    } else if (value < 10) {
                                        value++;
                                    }

                                    // 确保不为0
                                    if (value === 0) {
                                        value = 1;
                                    }

                                    input.value = value;
                                    input.dataset.prevValue = value;
                                }

                                // 减少forecastSteps值
                                function decrementForecastSteps() {
                                    const input = document.getElementById('forecastSteps');
                                    let value = parseInt(input.value) || 0;

                                    // 如果当前值为1，则跳过0直接变成-1
                                    if (value === 1) {
                                        value = -1;
                                    } else if (value > -10) {
                                        value--;
                                    }

                                    // 确保不为0
                                    if (value === 0) {
                                        value = -1;
                                    }

                                    input.value = value;
                                    input.dataset.prevValue = value;
                                }

                                // 切换forecastSteps的正负号
                                function toggleForecastStepsSign() {
                                    const input = document.getElementById('forecastSteps');
                                    let value = parseInt(input.value) || 0;

                                    // 如果当前值为0，设置为-3
                                    if (value === 0) {
                                        value = -3;
                                    } else {
                                        // 否则取反数，并确保在有效范围内
                                        value = -value;

                                        // 确保不为0
                                        if (value === 0) {
                                            value = 3;
                                        }

                                        // 确保在范围内
                                        if (value < -10) value = -10;
                                        if (value > 10) value = 10;
                                    }

                                    input.value = value;
                                    input.dataset.prevValue = value;
                                }

                                // 页面加载时初始化
                                document.addEventListener('DOMContentLoaded', function() {
                                    const forecastStepsInput = document.getElementById('forecastSteps');
                                    if (forecastStepsInput) {
                                        forecastStepsInput.dataset.prevValue = forecastStepsInput.value;
                                    }
                                });
                            </script>

                            <div class="d-grid gap-2">
                                <button type="button" id="runModelBtn" class="btn btn-primary">运行模型预测</button>
                            </div>

                            <div class="mt-3 text-center" id="predictionStatus"></div>
                        </form>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h4>预测结果表</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>时间</th>
                                        <th>开盘价</th>
                                        <th>最高价</th>
                                        <th>最低价</th>
                                        <th>收盘价</th>
                                        <th>涨跌幅</th>
                                    </tr>
                                </thead>
                                <tbody id="predictionTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center">运行模型后显示预测结果</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预测结果和图表区域 -->
            <div class="col-md-8" id="predictionResultsSection">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0" id="stockInfoTitle">股票信息</h3>
                        <div class="badge bg-primary" id="stockCode"></div>
                    </div>
                    <div class="card-body">
                        <div id="stockInfo" class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>当前价格:</strong> <span id="currentPrice">--</span></p>
                                    <p><strong>涨跌幅:</strong> <span id="priceChange">--</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>成交量:</strong> <span id="volume">--</span></p>
                                    <p><strong>更新时间:</strong> <span id="updateTime">--</span></p>
                                </div>
                            </div>
                        </div>
                        <div id="historicalChartContainer" style="width: 100%; height: 300px;"></div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h4>预测结果图表</h4>
                    </div>
                    <div class="card-body">
                        <div id="predictionChartContainer" style="width: 100%; height: 400px;"></div>
                        <div class="mt-3 small text-muted">
                            <p class="mb-1"><i class="bi bi-info-circle"></i> 蓝色标记的K线为模型预测结果，其他为历史数据。</p>
                            <p class="mb-0">预测结果仅供参考，不构成投资建议。</p>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h4>技术指标</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">ATR指标</h5>
                                        <p class="card-text" id="atrValue">--</p>
                                        <p class="small text-muted">真实波动幅度，反映市场波动性</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">预测准确度</h5>
                                        <p class="card-text" id="accuracyValue">--</p>
                                        <p class="small text-muted">模型预测的历史准确度</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">预测方向</h5>
                                        <p class="card-text" id="predictionDirection">--</p>
                                        <p class="small text-muted">根据预测结果判断的趋势方向</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">登录</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="mb-3">
                            <label for="registerUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">电子邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerFullName" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="registerFullName">
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerConfirmPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="registerConfirmPassword" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">注册</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 智能体投顾助手. 保留所有权利.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../static/js/navbar.js"></script>
    <script src="../static/js/advanced-charts.js"></script>
    <script src="../static/js/model-prediction.js"></script>
    <script src="../static/js/model.js"></script>
    <script src="../static/js/autocomplete.js"></script>
    <script src="../static/js/stock-comparison.js"></script>
</body>
</html>
