import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.config_loader import ConfigLoader
from redis import Redis
import logging
from qtunnel import DataSource, RunMode
from logging.handlers import RotatingFileHandler
from dotenv import load_dotenv
load_dotenv()

class Environment(object):
    _env = None

    def __init__(self):
        Environment._env = self
        self.host_url = ""
        self.redis = None
        # 使用绝对路径指定配置文件
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config", "config.json")
        self.config = ConfigLoader(config_file=config_path)
        # 从配置中获取USER_TAGS
        self.usertags = self.config.get("USER_TAGS", {})
        self.tagusers ={}
        self.app = None
        self.setup()
        self.ds = DataSource(RunMode.passive)

    @classmethod
    def get_instance(cls):
        """
        返回已经创建的 Environment 对象
        """
        if Environment._env is None:
            raise RuntimeError(u"Environment has not been created. Please Use `Environment.get_instance()` after init")
        # print(Environment._env)
        return Environment._env

    def setup(self):
        self.setup_logging()
        self.setup_redis()

    def setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger('ai_agent')
        self.logger.setLevel(logging.DEBUG)

        file_handler = RotatingFileHandler(
            'ai_agent.log',
            maxBytes=1024*1024,
            backupCount=5
        )
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        )
        self.logger.addHandler(file_handler)

        console_handler = logging.StreamHandler()
        console_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        )
        self.logger.addHandler(console_handler)

    def setup_redis(self):
        try:
            self.redis = Redis(
                host=os.getenv("REDIS_HOST", "localhost"),
                password=os.getenv("REDIS_PASSWORD", ""),
                db=os.getenv("REDIS_DB", 0),
                port=os.getenv("REDIS_PORT", 6379),
                socket_connect_timeout=5,  # 连接超时时间
                socket_timeout=5,  # 操作超时时间
                retry_on_timeout=True  # 超时重试
            )
            self.logger.info("初始化Redis客户端成功")
        except Exception as e:
            self.logger.error(f"初始化Redis客户端失败: {str(e)}")
            # 创建一个空的Redis对象，避免程序崩溃
            self.redis = None

    def check_redis_connection(self):
        """
        检查Redis连接是否成功

        Returns:
            bool: 连接成功返回True，否则返回False
        """
        if self.redis is None:
            self.logger.error("Redis客户端未初始化，无法检查连接")
            return False

        try:
            # 尝试执行一个简单的Redis命令来检查连接
            self.redis.ping()
            self.logger.info("Redis连接成功")
            return True
        except Exception as e:
            self.logger.error(f"Redis连接失败: {str(e)}")
            return False

    def set_app(self, app):
        self.app = app

    def set_logger(self, logger):
        self.logger = logger

    def set_redis(self, redis):
        self.redis = redis

    def set_username(self, username):
        self.username = username