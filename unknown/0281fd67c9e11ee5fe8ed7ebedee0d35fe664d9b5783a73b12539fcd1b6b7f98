<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体投顾助手</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
</head>
<body>
    {% include 'components/navbar.html' %}

    <div class="container mt-4">
        <!-- 欢迎区域 -->
        <div class="row mb-4" id="welcomeSection">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title">欢迎使用智能体投顾助手</h2>
                        <p class="card-text">本平台提供股票表现可视化、特定属性数据检索和新闻聚合等功能，帮助您做出更明智的投资决策。</p>
                        <div class="d-flex">
                            <input type="text" class="form-control me-2" id="searchTicker" placeholder="输入股票代码 (例如: 600000.SH,000001.SZ,IF9999.SF)">
                            <button class="btn btn-primary" id="searchBtn">搜索</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自选股区域 -->
        <div class="row mb-4 favorites-section" id="favoritesSection">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">自选股</h3>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-outline-secondary me-2" id="refreshFavoritesBtn">
                                <i class="bi bi-arrow-clockwise"></i> 刷新数据
                            </button>
                            <button class="btn btn-sm btn-outline-primary me-2" id="addToFavoritesBtn">添加自选</button>
                            <button class="btn btn-sm btn-outline-danger" id="manageFavoritesBtn">管理自选</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="favoritesTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="stocks-tab" data-bs-toggle="tab" data-bs-target="#stocks" type="button" role="tab">A股</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="futures-tab" data-bs-toggle="tab" data-bs-target="#futures" type="button" role="tab">期货</button>
                            </li>
                        </ul>
                        <div class="tab-content mt-3" id="favoritesTabContent">
                            <div class="tab-pane fade show active" id="stocks" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>代码</th>
                                                <th>名称</th>
                                                <th>最新价</th>
                                                <th>涨跌幅</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="stockFavoritesList">
                                            <!-- 股票自选列表将通过JavaScript动态填充 -->
                                            <tr>
                                                <td colspan="5" class="text-center">暂无自选股票</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="futures" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>代码</th>
                                                <th>名称</th>
                                                <th>最新价</th>
                                                <th>涨跌幅</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="futureFavoritesList">
                                            <!-- 期货自选列表将通过JavaScript动态填充 -->
                                            <tr>
                                                <td colspan="5" class="text-center">暂无自选期货</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 股票数据区域 -->
            <div class="col-md-8" id="stockDataSection" style="display: none;">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0" id="stockTitle">股票数据</h3>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-outline-success me-2" id="compareStocksBtn">股票比较</button>
                            <button class="btn btn-sm btn-outline-primary me-2" id="showAdvancedChartsBtn">显示/隐藏高级图表</button>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-secondary active" data-period="1d">日</button>
                                <button class="btn btn-sm btn-outline-secondary" data-period="1w">周</button>
                                <button class="btn btn-sm btn-outline-secondary" data-period="1m">月</button>
                                <button class="btn btn-sm btn-outline-secondary" data-period="3m">季</button>
                                <button class="btn btn-sm btn-outline-secondary" data-period="1y">年</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="stockChart" height="300"></canvas>
                    </div>
                </div>

                <!-- 技术指标面板（已合并到高级图表面板） -->

                <!-- 股票比较面板 -->
                <div class="card mb-4" id="stockComparisonPanel" style="display: none;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">股票比较</h3>
                        <button type="button" class="btn-close" id="closeComparisonBtn"></button>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="comparisonTickerInput" placeholder="输入股票代码">
                                    <button class="btn btn-primary" id="addComparisonStockBtn">添加</button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3 mb-md-0">
                                <div class="card">
                                    <div class="card-header">比较列表</div>
                                    <div class="card-body">
                                        <div id="comparisonStocksList">
                                            <!-- 比较列表将通过JavaScript动态填充 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div id="comparisonChartContainer">
                                    <div class="text-center py-5">
                                        <p class="text-muted">请添加股票进行比较</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高级图表面板 -->
                <div class="card mb-4" id="advancedChartsPanel">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">高级图表分析</h3>
                        <button type="button" class="btn-close" id="closeAdvancedChartsBtn"></button>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="chartTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="price-charts-tab" data-bs-toggle="tab" data-bs-target="#price-charts" type="button" role="tab">价格图表</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="technical-indicators-tab" data-bs-toggle="tab" data-bs-target="#technical-indicators" type="button" role="tab">技术指标</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="extended-indicators-tab" data-bs-toggle="tab" data-bs-target="#extended-indicators" type="button" role="tab">扩展指标</button>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="chartTabsContent">
                            <!-- 价格图表选项卡 -->
                            <div class="tab-pane fade show active" id="price-charts" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <h5 class="mb-0">历史走势图</h5>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-select" id="timeInterval">
                                                    <option value="1d">日线</option>
                                                    <option value="1h">小时线</option>
                                                    <option value="30m">30分钟</option>
                                                    <option value="15m">15分钟</option>
                                                    <option value="5m" selected>5分钟</option>
                                                    <option value="1m">1分钟</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <select class="form-select" id="timePeriod">
                                                    <option value="1d">1天</option>
                                                    <option value="5d">5天</option>
                                                    <option value="1mo">1个月</option>
                                                    <option value="3mo" selected>3个月</option>
                                                    <option value="6mo">6个月</option>
                                                    <option value="1y">1年</option>
                                                    <option value="max">全部</option>
                                                </select>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div id="advancedChartContainer" style="width: 100%; height: 400px;">
                                            <div class="text-center py-5">
                                                <p class="text-muted">请选择图表类型</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 技术指标选项卡 -->
                            <div class="tab-pane fade" id="technical-indicators" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="btn-group" role="group">
                                            <input type="radio" class="btn-check" name="indicator" id="maIndicator" value="ma" checked>
                                            <label class="btn btn-outline-primary" for="maIndicator">移动平均线</label>

                                            <input type="radio" class="btn-check" name="indicator" id="rsiIndicator" value="rsi">
                                            <label class="btn btn-outline-primary" for="rsiIndicator">RSI</label>

                                            <input type="radio" class="btn-check" name="indicator" id="macdIndicator" value="macd">
                                            <label class="btn btn-outline-primary" for="macdIndicator">MACD</label>

                                            <input type="radio" class="btn-check" name="indicator" id="bollingerIndicator" value="bollinger">
                                            <label class="btn btn-outline-primary" for="bollingerIndicator">布林带</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div id="indicatorChartContainer" style="width: 100%; height: 400px;">
                                            <div class="text-center py-5">
                                                <p class="text-muted">请选择指标类型</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 扩展指标选项卡 -->
                            <div class="tab-pane fade" id="extended-indicators" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="btn-group" role="group">
                                            <input type="radio" class="btn-check" name="extendedIndicator" id="volumeIndicator" value="volume" checked>
                                            <label class="btn btn-outline-primary" for="volumeIndicator">成交量</label>

                                            <input type="radio" class="btn-check" name="extendedIndicator" id="obvIndicator" value="obv">
                                            <label class="btn btn-outline-primary" for="obvIndicator">OBV</label>

                                            <input type="radio" class="btn-check" name="extendedIndicator" id="atrIndicator" value="atr">
                                            <label class="btn btn-outline-primary" for="atrIndicator">ATR</label>

                                            <input type="radio" class="btn-check" name="extendedIndicator" id="adxIndicator" value="adx">
                                            <label class="btn btn-outline-primary" for="adxIndicator">ADX</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div id="extendedIndicatorChart" style="width: 100%; height: 400px;">
                                            <div class="text-center py-5">
                                                <p class="text-muted">请选择指标类型</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 扩展技术指标面板（已合并到高级图表面板） -->

                <div class="card mb-4">
                    <div class="card-header">
                        <h4>股票详情</h4>
                    </div>
                    <div class="card-body">
                        <div class="row" id="stockDetails">
                            <!-- 股票详情将通过JavaScript动态填充 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新闻区域 -->
            <div class="col-md-4" id="newsSection" style="display: none;">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0" id="newsTitle">相关新闻</h3>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="newsTopicDropdown" data-bs-toggle="dropdown">全部主题</button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-topic="">全部主题</a></li>
                                <li><a class="dropdown-item" href="#" data-topic="财报">财报</a></li>
                                <li><a class="dropdown-item" href="#" data-topic="产品">产品</a></li>
                                <li><a class="dropdown-item" href="#" data-topic="市场">市场</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="newsList">
                            <!-- 新闻列表将通过JavaScript动态填充 -->
                            <div class="text-center py-5">
                                <p class="text-muted">请先搜索股票代码</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                            <div class="text-end mt-1">
                                <a href="#" id="forgotPasswordLink" class="small">忘记密码？</a>
                            </div>
                        </div>
                        <div class="alert alert-danger" id="loginError" style="display: none;">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span id="loginErrorMessage"></span>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">登录</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="mb-3">
                            <label for="registerUsername" class="form-label">用户名</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="registerUsername" required>
                                <button class="btn btn-outline-secondary" type="button" id="generateUsernameBtn">生成用户名</button>
                            </div>
                            <div class="form-text" id="usernameHelp">用户名不能重复，点击生成按钮可自动生成用户名</div>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">电子邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerFullName" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="registerFullName">
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required minlength="6">
                            <div class="form-text">密码至少需要 6 位字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="registerConfirmPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="registerConfirmPassword" required minlength="6">
                        </div>
                        <div class="alert alert-danger" id="registerError" style="display: none;">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span id="registerErrorMessage"></span>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">注册</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 重置密码模态框 -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">重置密码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="resetPasswordStep1">
                        <p>请输入您的用户名或注册邮箱，我们将向您发送密码重置链接。</p>
                        <div class="mb-3">
                            <label for="resetPasswordEmail" class="form-label">用户名或邮箱</label>
                            <input type="text" class="form-control" id="resetPasswordEmail" required>
                        </div>
                        <div class="alert alert-danger" id="resetPasswordError" style="display: none;">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span id="resetPasswordErrorMessage"></span>
                        </div>
                        <div class="d-grid">
                            <button type="button" id="sendResetLinkBtn" class="btn btn-primary">发送重置链接</button>
                        </div>
                    </div>
                    <div id="resetPasswordStep2" style="display: none;">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            重置链接已发送！请检查您的邮箱。
                        </div>
                        <p>我们已向您的邮箱发送了一封包含密码重置链接的邮件。请点击邮件中的链接完成密码重置。</p>
                        <p>如果您没有收到邮件，请检查垃圾邮件文件夹，或点击下方按钮重新发送。</p>
                        <div class="d-grid gap-2">
                            <button type="button" id="resendResetLinkBtn" class="btn btn-outline-primary">重新发送</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 智能体投顾助手. 保留所有权利.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../static/js/config.js"></script>
    <script src="../static/js/auth-interceptor.js"></script>
    <script src="../static/js/navbar.js"></script>
    <script src="../static/js/main.js"></script>
    <script src="../static/js/autocomplete.js"></script>
    <script src="../static/js/technical-indicators.js"></script>
    <script src="../static/js/stock-comparison.js"></script>
    <script src="../static/js/advanced-charts.js"></script>
    <script src="../static/js/extended-indicators.js"></script>
    <script src="../static/js/favorites.js"></script>
</body>
</html>
