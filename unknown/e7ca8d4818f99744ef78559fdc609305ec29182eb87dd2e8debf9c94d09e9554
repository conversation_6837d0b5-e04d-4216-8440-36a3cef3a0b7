// 自选股模块

// 全局变量
let stockFavorites = []; // A股自选列表
let futureFavorites = []; // 期货自选列表
let isManagingFavorites = false; // 是否处于管理模式

// 注意: API_BASE_URL已在main.js中定义

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', async function() {
    // 初始化自选股功能
    try {
        await initFavorites();
    } catch (error) {
        console.error('自选股初始化失败:', error);
    }
});

// 初始化自选股功能
async function initFavorites() {
    try {
        console.log('开始初始化自选股功能...');

        // 从服务器加载自选股数据
        const result = await loadFavoritesFromStorage();
        console.log('自选股数据加载结果:', result);

        // 更新自选股显示
        await updateFavoritesUI();

        // 添加事件监听器
        addFavoritesEventListeners();

        console.log('自选股功能初始化完成');
    } catch (error) {
        console.error('自选股功能初始化失败:', error);
        // 尝试使用默认数据
        useDefaultFavorites();
        updateFavoritesUI();
        addFavoritesEventListeners();
    }
}

// 从朌务器加载自选股数据
// 将函数暴露到全局作用域，便于其他页面调用
window.loadFavoritesFromStorage = async function() {
    try {
        // 检查用户是否登录
        const token = localStorage.getItem('accessToken');
        if (!token) {
            console.warn('用户未登录，使用默认自选股数据');
            useDefaultFavorites();
            return { stockCount: stockFavorites.length, futureCount: futureFavorites.length, source: 'default' };
        }

        console.log('开始从服务器获取自选股数据...');
        console.log('访问令牌:', token);

        // 添加时间戳参数避免缓存
        const timestamp = new Date().getTime();

        // 构建API URL
        const stockUrl = `${API_BASE_URL}/${API_VERSION}/favorites?type=stock&_t=${timestamp}`;
        const futureUrl = `${API_BASE_URL}/${API_VERSION}/favorites?type=future&_t=${timestamp}`;

        console.log('请求A股自选URL:', stockUrl);
        console.log('请求期货自选URL:', futureUrl);

        // 从朌务器获取A股自选
        const stockResponse = await fetchWithAuth(stockUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Pragma': 'no-cache',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            },
            cache: 'no-cache', // 禁用缓存
            credentials: 'same-origin'
        });

        // 从朌务器获取期货自选
        const futureResponse = await fetchWithAuth(futureUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Pragma': 'no-cache',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            },
            cache: 'no-cache', // 禁用缓存
            credentials: 'same-origin'
        });

        console.log('A股自选响应状态:', stockResponse.status);
        console.log('期货自选响应状态:', futureResponse.status);

        // 处理A股自选响应
        let stockData;
        if (stockResponse.ok) {
            try {
                stockData = await stockResponse.json();
                console.log('A股自选响应数据:', stockData);

                if (stockData.code === 200 && stockData.data) {
                    console.log(`成功获取 ${stockData.data.length} 支股票自选数据`);
                    stockFavorites = stockData.data.map(item => ({
                        symbol: item.symbol,
                        name: item.name
                    }));
                } else {
                    console.warn('A股自选响应数据格式不正确:', stockData);
                }
            } catch (e) {
                console.error('A股自选响应解析失败:', e);
                const text = await stockResponse.text();
                console.log('A股自选原始响应:', text);
            }
        } else {
            console.error('获取A股自选失败:', stockResponse.status);
            try {
                const text = await stockResponse.text();
                console.log('A股自选错误响应:', text);
            } catch (e) {
                console.error('无法获取A股自选错误详情');
            }
        }

        // 处理期货自选响应
        let futureData;
        if (futureResponse.ok) {
            try {
                futureData = await futureResponse.json();
                console.log('期货自选响应数据:', futureData);

                if (futureData.code === 200 && futureData.data) {
                    console.log(`成功获取 ${futureData.data.length} 支期货自选数据`);
                    futureFavorites = futureData.data.map(item => ({
                        symbol: item.symbol,
                        name: item.name
                    }));
                } else {
                    console.warn('期货自选响应数据格式不正确:', futureData);
                }
            } catch (e) {
                console.error('期货自选响应解析失败:', e);
                const text = await futureResponse.text();
                console.log('期货自选原始响应:', text);
            }
        } else {
            console.error('获取期货自选失败:', futureResponse.status);
            try {
                const text = await futureResponse.text();
                console.log('期货自选错误响应:', text);
            } catch (e) {
                console.error('无法获取期货自选错误详情');
            }
        }

        // 如果没有数据，使用默认数据
        if (stockFavorites.length === 0 && futureFavorites.length === 0) {
            console.warn('服务器没有自选股数据，使用默认数据');
            useDefaultFavorites();
            return { stockCount: stockFavorites.length, futureCount: futureFavorites.length, source: 'default' };
        }

        return {
            stockCount: stockFavorites.length,
            futureCount: futureFavorites.length,
            source: 'server',
            stockResponse: stockResponse.status,
            futureResponse: futureResponse.status
        };
    } catch (error) {
        console.error('加载自选股数据失败:', error);
        useDefaultFavorites();
        return {
            stockCount: stockFavorites.length,
            futureCount: futureFavorites.length,
            error: error.message,
            source: 'error'
        };
    }
}

// 使用默认自选股数据
function useDefaultFavorites() {
    // 默认A股自选
    stockFavorites = [
        { symbol: '600000.SH', name: '浦发银行' },
        { symbol: '000001.SZ', name: '平安银行' }
    ];

    // 默认期货自选
    futureFavorites = [
        { symbol: 'IF9999.SF', name: '沪深300指数期货' },
        { symbol: 'IC9999.SF', name: '中证500指数期货' }
    ];
}

// 保存自选股数据到朌务器
async function saveFavoritesToStorage() {
    // 检查用户是否登录
    const token = localStorage.getItem('accessToken');
    if (!token) {
        console.warn('用户未登录，无法保存自选股数据到朌务器');
        // 临时保存到本地存储
        localStorage.setItem('stockFavorites', JSON.stringify(stockFavorites));
        localStorage.setItem('futureFavorites', JSON.stringify(futureFavorites));
        return;
    }

    try {
        // 先清空现有自选股，然后批量添加
        // 注意：这里采用的是简化的方法，实际应用中可能需要更复杂的同步策略

        // 准备批量添加的数据
        const favorites = [
            ...stockFavorites.map(item => ({
                symbol: item.symbol,
                name: item.name,
                type: 'stock'
            })),
            ...futureFavorites.map(item => ({
                symbol: item.symbol,
                name: item.name,
                type: 'future'
            }))
        ];

        // 发送批量添加请求
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/favorites/batch`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ favorites })
        });

        if (!response.ok) {
            throw new Error(`朌务器响应错误: ${response.status}`);
        }

        const result = await response.json();
        console.log('自选股数据同步结果:', result);
    } catch (error) {
        console.error('保存自选股数据失败:', error);
        // 失败时临时保存到本地存储
        localStorage.setItem('stockFavorites', JSON.stringify(stockFavorites));
        localStorage.setItem('futureFavorites', JSON.stringify(futureFavorites));
    }
}

// 更新自选股UI显示
function updateFavoritesUI() {
    // 更新A股自选列表
    updateStockFavoritesUI();

    // 更新期货自选列表
    updateFutureFavoritesUI();
}

// 更新A股自选列表UI
async function updateStockFavoritesUI() {
    console.log('开始更新A股自选列表...');
    const stockFavoritesList = document.getElementById('stockFavoritesList');

    // 显示加载中状态
    stockFavoritesList.innerHTML = '<tr><td colspan="5" class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> 加载中...</td></tr>';

    // 如果没有自选股，显示提示信息
    if (stockFavorites.length === 0) {
        stockFavoritesList.innerHTML = '<tr><td colspan="5" class="text-center">暂无自选股票</td></tr>';
        return;
    }

    // 清空列表
    stockFavoritesList.innerHTML = '';

    // 并行获取所有股票数据
    const stockPromises = stockFavorites.map(stock => {
        return fetchStockInfo(stock.symbol)
            .then(stockData => ({ stock, stockData, error: null }))
            .catch(error => ({ stock, stockData: null, error }));
    });

    // 等待所有请求完成
    const stockResults = await Promise.all(stockPromises);
    console.log(`获取到 ${stockResults.length} 支股票数据`);

    // 遍历结果并添加到UI
    for (const result of stockResults) {
        const { stock, stockData, error } = result;

        // 创建表格行
        const row = document.createElement('tr');

        if (error || !stockData) {
            console.warn(`股票 ${stock.symbol} 数据获取失败:`, error);
            // 设置行内容（错误状态）
            row.innerHTML = `
                <td><a href="#" class="stock-link" data-symbol="${stock.symbol}">${stock.symbol}</a></td>
                <td>${stock.name}</td>
                <td>-</td>
                <td>-</td>
                <td>
                    ${isManagingFavorites ?
                        `<button class="btn btn-sm btn-danger remove-favorite" data-symbol="${stock.symbol}" data-type="stock">删除</button>` :
                        `<button class="btn btn-sm btn-primary view-stock" data-symbol="${stock.symbol}">查看</button>`
                    }
                </td>
            `;
        } else {
            // 设置行内容（正常状态）
            const price = stockData.price ? stockData.price.toFixed(2) : '-';
            const changePercent = stockData.changePercent || 0;
            const changePercentStr = changePercent > 0 ? `+${changePercent.toFixed(2)}%` : `${changePercent.toFixed(2)}%`;
            const changeClass = changePercent > 0 ? 'text-success' : changePercent < 0 ? 'text-danger' : '';

            row.innerHTML = `
                <td><a href="#" class="stock-link" data-symbol="${stock.symbol}">${stock.symbol}</a></td>
                <td>${stock.name}</td>
                <td>${price}</td>
                <td class="${changeClass}">${changePercentStr}</td>
                <td>
                    ${isManagingFavorites ?
                        `<button class="btn btn-sm btn-danger remove-favorite" data-symbol="${stock.symbol}" data-type="stock">删除</button>` :
                        `<button class="btn btn-sm btn-primary view-stock" data-symbol="${stock.symbol}">查看</button>`
                    }
                </td>
            `;
        }

        // 添加到列表
        stockFavoritesList.appendChild(row);
    }

    console.log('A股自选列表更新完成');

    // 添加事件监听器
    addStockLinksEventListeners();
}

// 更新期货自选列表UI
async function updateFutureFavoritesUI() {
    console.log('开始更新期货自选列表...');
    const futureFavoritesList = document.getElementById('futureFavoritesList');

    // 显示加载中状态
    futureFavoritesList.innerHTML = '<tr><td colspan="5" class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> 加载中...</td></tr>';

    // 如果没有自选期货，显示提示信息
    if (futureFavorites.length === 0) {
        futureFavoritesList.innerHTML = '<tr><td colspan="5" class="text-center">暂无自选期货</td></tr>';
        return;
    }

    // 清空列表
    futureFavoritesList.innerHTML = '';

    // 并行获取所有期货数据
    const futurePromises = futureFavorites.map(future => {
        return fetchStockInfo(future.symbol)
            .then(futureData => ({ future, futureData, error: null }))
            .catch(error => ({ future, futureData: null, error }));
    });

    // 等待所有请求完成
    const futureResults = await Promise.all(futurePromises);
    console.log(`获取到 ${futureResults.length} 支期货数据`);

    // 遍历结果并添加到UI
    for (const result of futureResults) {
        const { future, futureData, error } = result;

        // 创建表格行
        const row = document.createElement('tr');

        if (error || !futureData) {
            console.warn(`期货 ${future.symbol} 数据获取失败:`, error);
            // 设置行内容（错误状态）
            row.innerHTML = `
                <td><a href="#" class="stock-link" data-symbol="${future.symbol}">${future.symbol}</a></td>
                <td>${future.name}</td>
                <td>-</td>
                <td>-</td>
                <td>
                    ${isManagingFavorites ?
                        `<button class="btn btn-sm btn-danger remove-favorite" data-symbol="${future.symbol}" data-type="future">删除</button>` :
                        `<button class="btn btn-sm btn-primary view-stock" data-symbol="${future.symbol}">查看</button>`
                    }
                </td>
            `;
        } else {
            // 设置行内容（正常状态）
            const price = futureData.price ? futureData.price.toFixed(2) : '-';
            const changePercent = futureData.changePercent || 0;
            const changePercentStr = changePercent > 0 ? `+${changePercent.toFixed(2)}%` : `${changePercent.toFixed(2)}%`;
            const changeClass = changePercent > 0 ? 'text-success' : changePercent < 0 ? 'text-danger' : '';

            row.innerHTML = `
                <td><a href="#" class="stock-link" data-symbol="${future.symbol}">${future.symbol}</a></td>
                <td>${future.name}</td>
                <td>${price}</td>
                <td class="${changeClass}">${changePercentStr}</td>
                <td>
                    ${isManagingFavorites ?
                        `<button class="btn btn-sm btn-danger remove-favorite" data-symbol="${future.symbol}" data-type="future">删除</button>` :
                        `<button class="btn btn-sm btn-primary view-stock" data-symbol="${future.symbol}">查看</button>`
                    }
                </td>
            `;
        }

        // 添加到列表
        futureFavoritesList.appendChild(row);
    }

    console.log('期货自选列表更新完成');

    // 添加事件监听器
    addStockLinksEventListeners();
}

// 添加自选股事件监听器
function addFavoritesEventListeners() {
    // 添加自选按钮点击事件
    document.getElementById('addToFavoritesBtn').addEventListener('click', function() {
        showAddFavoriteModal();
    });

    // 管理自选按钮点击事件
    document.getElementById('manageFavoritesBtn').addEventListener('click', function() {
        toggleManageFavorites();
    });

    // 刷新数据按钮点击事件
    document.getElementById('refreshFavoritesBtn').addEventListener('click', async function() {
        // 显示加载状态
        const button = this;
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 刷新中...';
        button.disabled = true;

        try {
            // 强制刷新自选股数据
            console.log('手动刷新自选股数据...');

            // 清除所有缓存
            localStorage.removeItem('stockFavorites');
            localStorage.removeItem('futureFavorites');

            // 从服务器重新加载自选股数据
            const result = await loadFavoritesFromStorage();
            console.log('自选股数据加载结果:', result);

            // 更新UI
            await updateFavoritesUI();

            // 显示成功消息
            const totalCount = result.stockCount + result.futureCount;
            alert(`刷新成功！已加载 ${totalCount} 支自选股数据。`);
        } catch (error) {
            console.error('刷新自选股数据失败:', error);
            alert('刷新失败: ' + error.message);
        } finally {
            // 恢复按钮状态
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 500);
        }
    });

    // 监听自选股更新事件
    document.addEventListener('favoriteUpdate', function(event) {
        console.log('收到自选股更新事件:', event.detail);
        // 重新加载自选股数据
        loadFavoritesFromStorage().then(() => {
            // 更新UI
            updateFavoritesUI();
        });
    });
}

// 添加股票链接事件监听器
function addStockLinksEventListeners() {
    // 为所有股票链接添加点击事件
    document.querySelectorAll('.stock-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const symbol = this.getAttribute('data-symbol');
            searchStock(symbol);
        });
    });

    // 为所有查看按钮添加点击事件
    document.querySelectorAll('.view-stock').forEach(button => {
        button.addEventListener('click', function() {
            const symbol = this.getAttribute('data-symbol');
            console.log('点击查看按钮:', symbol); // 添加日志，帮助调试

            // 使用全局作用域中的searchStock函数
            if (typeof window.searchStock === 'function') {
                window.searchStock(symbol);
            } else {
                console.error('找不到searchStock函数，请确保main.js已加载');
                alert('系统错误，无法查看股票详情');
            }
        });
    });

    // 为所有删除按钮添加点击事件（仅在管理模式下可见）
    document.querySelectorAll('.remove-favorite').forEach(button => {
        button.addEventListener('click', function() {
            const symbol = this.getAttribute('data-symbol');
            const type = this.getAttribute('data-type');
            removeFavorite(symbol, type);
        });
    });
}

// 显示添加自选对话框
function showAddFavoriteModal() {
    // 检查是否已经存在模态框
    let modal = document.getElementById('addFavoriteModal');

    // 如果不存在，创建一个新的
    if (!modal) {
        // 创建模态框HTML
        const modalHTML = `
            <div class="modal fade" id="addFavoriteModal" tabindex="-1" aria-labelledby="addFavoriteModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addFavoriteModalLabel">添加自选</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="favoriteSymbol" class="form-label">股票/期货代码</label>
                                <input type="text" class="form-control" id="favoriteSymbol" placeholder="输入代码，如：600000.SH, IF9999.SF">
                            </div>
                            <div class="mb-3">
                                <label for="favoriteType" class="form-label">类型</label>
                                <select class="form-select" id="favoriteType">
                                    <option value="stock">A股</option>
                                    <option value="future">期货</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmAddFavorite">添加</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到文档
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 获取模态框引用
        modal = document.getElementById('addFavoriteModal');

        // 添加确认按钮点击事件
        document.getElementById('confirmAddFavorite').addEventListener('click', function() {
            const symbol = document.getElementById('favoriteSymbol').value.trim().toUpperCase();
            const type = document.getElementById('favoriteType').value;

            if (symbol) {
                addFavorite(symbol, type);
                // 关闭模态框
                const modalInstance = bootstrap.Modal.getInstance(modal);
                modalInstance.hide();
            } else {
                alert('请输入有效的股票/期货代码');
            }
        });
    }

    // 显示模态框
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

// 切换管理自选模式
function toggleManageFavorites() {
    isManagingFavorites = !isManagingFavorites;

    // 更新按钮文本
    const manageFavoritesBtn = document.getElementById('manageFavoritesBtn');
    manageFavoritesBtn.textContent = isManagingFavorites ? '完成管理' : '管理自选';
    manageFavoritesBtn.classList.toggle('btn-outline-danger', !isManagingFavorites);
    manageFavoritesBtn.classList.toggle('btn-danger', isManagingFavorites);

    // 更新UI
    updateFavoritesUI();
}

// 添加自选
async function addFavorite(symbol, type) {
    try {
        // 获取股票/期货信息
        const info = await fetchStockInfo(symbol);
        const name = info.name || symbol;

        // 检查用户是否登录
        const token = localStorage.getItem('accessToken');

        if (token) {
            // 已登录，使用API添加自选股
            const response = await fetch(`${API_BASE_URL}/${API_VERSION}/favorites`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    symbol: symbol,
                    name: name,
                    type: type
                })
            });

            if (!response.ok) {
                // 如果是409冲突，说明已经添加过
                if (response.status === 409 || response.status === 400) {
                    const errorData = await response.json();
                    alert(errorData.detail || `${symbol} 已在自选列表中`);
                    return;
                }
                throw new Error(`朌务器响应错误: ${response.status}`);
            }

            // 创建新的自选项
            const newFavorite = {
                symbol: symbol,
                name: name
            };

            // 根据类型添加到相应的列表
            if (type === 'stock') {
                stockFavorites.push(newFavorite);
            } else {
                futureFavorites.push(newFavorite);
            }
        } else {
            // 未登录，使用本地存储
            // 创建新的自选项
            const newFavorite = {
                symbol: symbol,
                name: name
            };

            // 根据类型添加到相应的列表
            if (type === 'stock') {
                // 检查是否已存在
                if (!stockFavorites.some(item => item.symbol === symbol)) {
                    stockFavorites.push(newFavorite);
                } else {
                    alert(`${symbol} 已在A股自选列表中`);
                    return;
                }
            } else {
                // 检查是否已存在
                if (!futureFavorites.some(item => item.symbol === symbol)) {
                    futureFavorites.push(newFavorite);
                } else {
                    alert(`${symbol} 已在期货自选列表中`);
                    return;
                }
            }

            // 保存到本地存储
            localStorage.setItem('stockFavorites', JSON.stringify(stockFavorites));
            localStorage.setItem('futureFavorites', JSON.stringify(futureFavorites));
        }

        // 更新UI
        updateFavoritesUI();

        // 显示成功消息
        alert(`已成功添加 ${symbol} 到自选列表`);
    } catch (error) {
        console.error(`添加自选 ${symbol} 失败:`, error);
        alert(`添加失败: ${error.message}`);
    }
}

// 删除自选
async function removeFavorite(symbol, type) {
    if (confirm(`确定要从自选列表中删除 ${symbol} 吗？`)) {
        try {
            // 检查用户是否登录
            const token = localStorage.getItem('accessToken');

            if (token) {
                // 已登录，使用API删除自选股
                const response = await fetch(`${API_BASE_URL}/${API_VERSION}/favorites/symbol/${symbol}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    // 如果是404，说明自选股不存在
                    if (response.status === 404) {
                        console.warn(`自选股 ${symbol} 不存在或不属于当前用户`);
                    } else {
                        throw new Error(`朌务器响应错误: ${response.status}`);
                    }
                }
            }

            // 无论是否登录，都需要更新前端缓存
            // 根据类型从相应的列表中删除
            if (type === 'stock') {
                stockFavorites = stockFavorites.filter(item => item.symbol !== symbol);
            } else {
                futureFavorites = futureFavorites.filter(item => item.symbol !== symbol);
            }

            // 如果未登录，保存到本地存储
            if (!token) {
                localStorage.setItem('stockFavorites', JSON.stringify(stockFavorites));
                localStorage.setItem('futureFavorites', JSON.stringify(futureFavorites));
            }

            // 更新UI
            updateFavoritesUI();
        } catch (error) {
            console.error(`删除自选 ${symbol} 失败:`, error);
            alert(`删除失败: ${error.message}`);
        }
    }
}

// 获取股票/期货信息
async function fetchStockInfo(symbol) {
    try {
        // 添加时间戳参数避免缓存
        const timestamp = new Date().getTime();
        // 构建API URL
        const url = `${API_BASE_URL}/${API_VERSION}/datahub/stock_info?label=${encodeURIComponent(symbol)}&_t=${timestamp}`;

        console.log(`获取股票/期货信息: ${symbol}`);

        // 发送请求
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Pragma': 'no-cache',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            },
            cache: 'no-cache', // 禁用缓存
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        // 解析响应
        const data = await response.json();
        console.log(`成功获取 ${symbol} 数据:`, data);

        // 返回股票/期货信息
        return data;
    } catch (error) {
        console.error(`获取 ${symbol} 信息失败:`, error);
        // 返回一个默认对象
        return {
            name: symbol,
            price: 0,
            change: 0,
            changePercent: 0
        };
    }
}

// 注释掉定时刷新自选股数据的功能，避免页面刷新抽动
// 用户可以通过点击刷新按钮手动刷新自选股数据

// 如果需要恢复定时刷新功能，请取消下面代码的注释
/*
setInterval(async function() {
    if (!isManagingFavorites) {  // 只在非管理模式下刷新
        console.log('定时刷新自选股数据...');

        try {
            // 从服务器重新加载自选股数据
            await loadFavoritesFromStorage();

            // 更新UI
            updateFavoritesUI();

            console.log('定时刷新完成');
        } catch (error) {
            console.error('定时刷新失败:', error);
        }
    }
}, 20000);  // 缩短刷新时间为20秒
*/
