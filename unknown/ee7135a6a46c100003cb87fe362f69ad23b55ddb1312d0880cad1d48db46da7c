"""
Example of using the AI Hedge Fund module in the Hua Bot AI Agent.

This file demonstrates how to use the AI Hedge Fund module to analyze stocks
and generate investment recommendations.
"""

import sys
import os
import argparse

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.ai_hedge_fund.hedge_fund import create_hedge_fund
from modules.ai_hedge_fund.web_app import main as web_app_main


def run_analysis_example():
    """Run a simple analysis example."""
    print("AI Hedge Fund Analysis Example")
    print("============================")

    # Create the AI Hedge Fund instance
    hedge_fund = create_hedge_fund(
        model_name="gpt-4o",
        model_provider="OpenAI",
        use_local_llm=False
    )

    # Define the tickers to analyze
    tickers = ["AAPL", "MSFT", "GOOGL"]

    # Run the analysis
    print(f"\nAnalyzing {', '.join(tickers)}...")
    result = hedge_fund.analyze_stocks(
        tickers=tickers,
        show_reasoning=True
    )

    # Print the formatted results
    print(hedge_fund.format_analysis(result))


def run_backtest_example():
    """Run a simple backtest example."""
    print("AI Hedge Fund Backtest Example")
    print("============================")

    # Create the AI Hedge Fund instance
    hedge_fund = create_hedge_fund(
        model_name="gpt-4o",
        model_provider="OpenAI",
        use_local_llm=False
    )

    # Define the tickers to analyze
    tickers = ["AAPL", "MSFT", "GOOGL"]

    # Run a backtest
    print(f"\nRunning backtest on {', '.join(tickers)}...")
    backtest_result = hedge_fund.run_backtest(
        tickers=tickers,
        initial_capital=100000.0
    )

    # Print the backtest results
    print(f"\nBacktest Results:")
    print(f"Tickers: {', '.join(tickers)}")
    print(f"Period: {backtest_result['start_date']} to {backtest_result['end_date']}")
    print(f"Initial Capital: ${backtest_result['initial_capital']:.2f}")
    print(f"Final Capital: ${backtest_result['final_capital']:.2f}")
    print(f"Return: {backtest_result['return_pct']:.2f}%")
    print(f"Sharpe Ratio: {backtest_result['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {backtest_result['max_drawdown']:.2f}%")


def main():
    """Main function for the example."""
    parser = argparse.ArgumentParser(description="AI Hedge Fund Examples")
    parser.add_argument("--web", action="store_true", help="Run the web interface")
    parser.add_argument("--analysis", action="store_true", help="Run the analysis example")
    parser.add_argument("--backtest", action="store_true", help="Run the backtest example")
    args = parser.parse_args()

    # If no arguments are provided, run both examples
    if not (args.web or args.analysis or args.backtest):
        run_analysis_example()
        print("\n" + "-" * 50 + "\n")
        run_backtest_example()
        return

    # Run the selected examples
    if args.web:
        web_app_main()
    if args.analysis:
        run_analysis_example()
    if args.backtest:
        run_backtest_example()


if __name__ == "__main__":
    main()
