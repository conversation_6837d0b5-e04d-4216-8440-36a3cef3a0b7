// 全局变量
let currentUser = null;
let accessToken = null;
let apiTokens = [];
let favoriteStocks = [];
let subscriptionPlans = [];
let currentSubscription = null;

// API基础URL
const API_BASE_URL = 'http://localhost:8080/api';

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkLoginStatus();

    // 初始化事件监听器
    initEventListeners();

    // 初始化令牌模态框
    initTokenModal();
});

// 检查登录状态
function checkLoginStatus() {
    const token = localStorage.getItem('accessToken');
    const user = localStorage.getItem('user');

    if (token && user) {
        accessToken = token;
        currentUser = JSON.parse(user);

        // 更新UI显示用户信息
        updateUserUI();

        // 获取API令牌
        fetchApiTokens();

        // 获取收藏的股票
        fetchFavoriteStocks();

        // 获取订阅计划
        fetchSubscriptionPlans();

        // 获取当前订阅
        fetchCurrentSubscription();
    } else {
        // 未登录，重定向到首页
        window.location.href = '/';
    }
}

// 初始化事件监听器
function initEventListeners() {
    // 个人资料表单提交事件
    document.getElementById('profileForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const fullName = document.getElementById('fullName').value;
        const phoneNumber = document.getElementById('phoneNumber').value;
        const address = document.getElementById('address').value;

        updateProfile(fullName, phoneNumber, address);
    });

    // 生成令牌按钮点击事件
    document.getElementById('generateTokenBtn').addEventListener('click', function() {
        generateApiToken();
    });

    // 用户下拉菜单点击事件
    document.getElementById('userDropdown').addEventListener('click', function() {
        if (confirm('确定要退出登录吗？')) {
            logout();
        }
    });

    // 订阅计划点击事件委托
    document.getElementById('subscriptionPlans').addEventListener('click', function(e) {
        // 检查是否点击了订阅按钮
        if (e.target.classList.contains('subscribe-btn')) {
            const tier = e.target.getAttribute('data-tier');
            showSubscriptionModal(tier);
        }
    });

    // 自动续费相关按钮事件
    document.body.addEventListener('click', function(e) {
        if (e.target.id === 'cancelAutoRenewBtn') {
            cancelAutoRenew();
        } else if (e.target.id === 'enableAutoRenewBtn') {
            enableAutoRenew();
        }
    });

    // 绑定微信按钮点击事件
    document.getElementById('bindWechatBtn').addEventListener('click', function() {
        bindThirdPartyAccount('wechat');
    });

    // 绑定支付宝按钮点击事件
    document.getElementById('bindAlipayBtn').addEventListener('click', function() {
        bindThirdPartyAccount('alipay');
    });
}

// 更新用户UI
function updateUserUI() {
    // 更新用户下拉菜单
    const userDropdown = document.getElementById('userDropdown');
    userDropdown.innerHTML = `
        <div class="user-avatar">${currentUser.username.charAt(0).toUpperCase()}</div>
        <span>${currentUser.username}</span>
    `;

    // 更新个人资料
    document.getElementById('profileAvatar').textContent = currentUser.username.charAt(0).toUpperCase();
    document.getElementById('profileUsername').textContent = currentUser.username;
    document.getElementById('profileEmail').textContent = currentUser.email;
    document.getElementById('fullName').value = currentUser.fullName || '';
    document.getElementById('phoneNumber').value = currentUser.phoneNumber || '';
    document.getElementById('address').value = currentUser.address || '';

    // 更新第三方账号绑定状态
    updateThirdPartyBindStatus();
}

// 获取API令牌
function fetchApiTokens() {
    // 显示加载动画
    const tokensList = document.getElementById('tokensList');
    tokensList.innerHTML = '<tr><td colspan="7" class="text-center py-3"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></td></tr>';

    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        apiTokens = [
            {
                id: '1',
                name: '数据分析应用',
                token: 'aicm_1a2b3c4d5e6f7g8h9i0j',
                fullToken: 'aicm_1a2b3c4d5e6f7g8h9i0j.eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ',
                permission: 'read',
                status: 'active',
                created: '2023-04-15',
                expires: '2024-04-15',
                lastUsed: '2023-05-20',
                description: '用于数据分析和可视化的只读令牌',
                scopes: ['market_data', 'model']
            },
            {
                id: '2',
                name: '自动交易系统',
                token: 'aicm_2b3c4d5e6f7g8h9i0j1k',
                fullToken: 'aicm_2b3c4d5e6f7g8h9i0j1k.eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ',
                permission: 'write',
                status: 'active',
                created: '2023-03-22',
                expires: '2023-09-22',
                lastUsed: '2023-05-25',
                description: '用于自动交易系统的读写令牌',
                scopes: ['market_data', 'portfolio', 'model']
            },
            {
                id: '3',
                name: '管理员令牌',
                token: 'aicm_3c4d5e6f7g8h9i0j1k2l',
                fullToken: 'aicm_3c4d5e6f7g8h9i0j1k2l.eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ',
                permission: 'admin',
                status: 'revoked',
                created: '2023-02-10',
                expires: '2024-02-10',
                lastUsed: '2023-03-15',
                description: '管理员全功能令牌（已撤销）',
                scopes: ['market_data', 'portfolio', 'model', 'user']
            }
        ];

        // 更新UI
        updateTokensUI();
    }, 1000);
}

// 更新令牌UI
function updateTokensUI() {
    const tokensList = document.getElementById('tokensList');

    if (apiTokens.length > 0) {
        tokensList.innerHTML = '';
        apiTokens.forEach(token => {
            const row = document.createElement('tr');

            // 根据状态设置样式
            let statusBadge = '';
            if (token.status === 'active') {
                statusBadge = '<span class="badge bg-success">有效</span>';
            } else if (token.status === 'revoked') {
                statusBadge = '<span class="badge bg-danger">已撤销</span>';
            } else if (token.status === 'expired') {
                statusBadge = '<span class="badge bg-warning text-dark">已过期</span>';
            }

            // 根据权限设置样式
            let permissionBadge = '';
            if (token.permission === 'read') {
                permissionBadge = '<span class="badge bg-info">只读</span>';
            } else if (token.permission === 'write') {
                permissionBadge = '<span class="badge bg-primary">读写</span>';
            } else if (token.permission === 'admin') {
                permissionBadge = '<span class="badge bg-danger">管理员</span>';
            }

            // 生成操作按钮
            let actionButtons = '';
            if (token.status === 'active') {
                actionButtons = `
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="showTokenDetail('${token.id}')"><i class="bi bi-eye"></i></button>
                    <button class="btn btn-sm btn-outline-danger" onclick="revokeToken('${token.id}')"><i class="bi bi-x-circle"></i> 撤销</button>
                `;
            } else {
                actionButtons = `
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="showTokenDetail('${token.id}')"><i class="bi bi-eye"></i></button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="deleteToken('${token.id}')"><i class="bi bi-trash"></i></button>
                `;
            }

            row.innerHTML = `
                <td>${token.name}</td>
                <td><code>${token.token}</code></td>
                <td>${permissionBadge}</td>
                <td>${statusBadge}</td>
                <td>${token.created}</td>
                <td>${token.expires === '0' ? '永不过期' : token.expires}</td>
                <td>
                    <div class="d-flex">
                        ${actionButtons}
                    </div>
                </td>
            `;
            tokensList.appendChild(row);
        });
    } else {
        tokensList.innerHTML = '<tr><td colspan="7" class="text-center py-3">暂无API令牌</td></tr>';
    }
}

// 获取收藏的股票
function fetchFavoriteStocks() {
    // 显示加载动画
    const favoritesList = document.getElementById('favoritesList');
    favoritesList.innerHTML = '<tr><td colspan="5" class="text-center py-3"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></td></tr>';

    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        favoriteStocks = [
            {
                ticker: 'AAPL',
                name: '苹果公司',
                price: 175.43,
                change: 2.15,
                changePercent: 1.24
            },
            {
                ticker: 'MSFT',
                name: '微软公司',
                price: 325.76,
                change: -1.32,
                changePercent: -0.40
            },
            {
                ticker: 'GOOG',
                name: 'Alphabet公司',
                price: 2732.42,
                change: 15.67,
                changePercent: 0.58
            }
        ];

        // 更新UI
        updateFavoritesUI();
    }, 1500);
}

// 更新收藏UI
function updateFavoritesUI() {
    const favoritesList = document.getElementById('favoritesList');

    if (favoriteStocks.length > 0) {
        favoritesList.innerHTML = '';
        favoriteStocks.forEach(stock => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><a href="/stock/${stock.ticker}">${stock.ticker}</a></td>
                <td>${stock.name}</td>
                <td>$${stock.price.toFixed(2)}</td>
                <td class="${stock.change > 0 ? 'text-success' : 'text-danger'}">
                    ${stock.change > 0 ? '+' : ''}${stock.change.toFixed(2)} (${stock.changePercent.toFixed(2)}%)
                </td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="removeFromFavorites('${stock.ticker}')">移除</button>
                </td>
            `;
            favoritesList.appendChild(row);
        });
    } else {
        favoritesList.innerHTML = '<tr><td colspan="5" class="text-center py-3">暂无收藏的股票</td></tr>';
    }
}

// 更新个人资料
function updateProfile(fullName, phoneNumber, address) {
    // 显示加载状态
    const submitBtn = document.querySelector('#profileForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 更新中...';
    submitBtn.disabled = true;

    // 模拟API调用
    setTimeout(() => {
        // 更新用户信息
        currentUser.fullName = fullName;
        currentUser.phoneNumber = phoneNumber;
        currentUser.address = address;

        // 更新本地存储
        localStorage.setItem('user', JSON.stringify(currentUser));

        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;

        // 显示成功消息
        showToast('个人资料更新成功！', 'success');
    }, 1000);
}

// 初始化令牌创建模态框
function initTokenModal() {
    // 添加事件监听器
    document.getElementById('createTokenBtn').addEventListener('click', function() {
        // 重置表单
        document.getElementById('createTokenForm').reset();
    });

    // 生成令牌按钮点击事件
    document.getElementById('generateTokenBtn').addEventListener('click', function() {
        generateApiToken();
    });
}

// 生成API令牌
function generateApiToken() {
    // 获取表单数据
    const tokenName = document.getElementById('tokenName').value;
    const tokenExpiration = document.getElementById('tokenExpiration').value;
    const tokenPermission = document.getElementById('tokenPermission').value;
    const tokenDescription = document.getElementById('tokenDescription').value;

    // 获取选中的范围
    const scopes = [];
    if (document.getElementById('scopeMarketData').checked) scopes.push('market_data');
    if (document.getElementById('scopePortfolio').checked) scopes.push('portfolio');
    if (document.getElementById('scopeModel').checked) scopes.push('model');
    if (document.getElementById('scopeUser').checked) scopes.push('user');

    // 验证表单
    if (!tokenName) {
        showToast('请输入令牌名称', 'danger');
        return;
    }

    if (scopes.length === 0) {
        showToast('请至少选择一个访问范围', 'danger');
        return;
    }

    // 显示加载状态
    const generateBtn = document.getElementById('generateTokenBtn');
    const originalBtnText = generateBtn.textContent;
    generateBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 生成中...';
    generateBtn.disabled = true;

    // 模拟API调用
    setTimeout(() => {
        // 生成令牌前缀
        const tokenPrefix = 'aicm_' + Math.random().toString(36).substring(2, 10);

        // 生成完整令牌
        const fullToken = tokenPrefix + '.' + Math.random().toString(36).substring(2, 15) + '.' + Math.random().toString(36).substring(2, 15);

        // 计算过期时间
        let expiresDate = '0';
        if (tokenExpiration !== '0') {
            const expireDate = new Date();
            expireDate.setDate(expireDate.getDate() + parseInt(tokenExpiration));
            expiresDate = expireDate.toISOString().split('T')[0];
        }

        // 生成新令牌
        const newToken = {
            id: 'token_' + Date.now(),
            name: tokenName,
            token: tokenPrefix,
            fullToken: fullToken,
            permission: tokenPermission,
            status: 'active',
            created: new Date().toISOString().split('T')[0],
            expires: expiresDate,
            lastUsed: null,
            description: tokenDescription,
            scopes: scopes
        };

        // 添加到令牌列表
        apiTokens.unshift(newToken);

        // 更新UI
        updateTokensUI();

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('createTokenModal'));
        modal.hide();

        // 显示令牌详情
        showNewTokenDetail(newToken);

        // 恢复按钮状态
        generateBtn.innerHTML = originalBtnText;
        generateBtn.disabled = false;
    }, 1500);
}

// 显示新生成的令牌详情
function showNewTokenDetail(token) {
    // 创建模态框
    const modalId = 'newTokenModal';
    let modal = document.getElementById(modalId);

    // 如果模态框已存在，先移除
    if (modal) {
        document.body.removeChild(modal);
    }

    // 创建新的模态框
    modal = document.createElement('div');
    modal.id = modalId;
    modal.className = 'modal fade';
    modal.tabIndex = -1;
    modal.setAttribute('aria-labelledby', `${modalId}Label`);
    modal.setAttribute('aria-hidden', 'true');

    // 构建模态框内容
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="${modalId}Label">令牌创建成功</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <strong>重要提示：</strong> 这是您唯一能够看到完整令牌的机会。请立即复制并安全地存储它。
                    </div>

                    <h6>令牌信息</h6>
                    <div class="mb-3">
                        <label class="form-label">名称</label>
                        <input type="text" class="form-control" value="${token.name}" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">完整令牌</label>
                        <div class="input-group">
                            <input type="text" class="form-control font-monospace" value="${token.fullToken}" id="fullTokenValue" readonly>
                            <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('fullTokenValue')">
                                <i class="bi bi-clipboard"></i> 复制
                            </button>
                        </div>
                        <div class="form-text">请妥善保管此令牌，它将不再显示。</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">权限级别</label>
                            <input type="text" class="form-control" value="${getPermissionName(token.permission)}" readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">创建日期</label>
                            <input type="text" class="form-control" value="${token.created}" readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">过期时间</label>
                            <input type="text" class="form-control" value="${token.expires === '0' ? '永不过期' : token.expires}" readonly>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">访问范围</label>
                        <div>
                            ${token.scopes.map(scope => `<span class="badge bg-secondary me-1">${getScopeName(scope)}</span>`).join('')}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" rows="2" readonly>${token.description || '无描述'}</textarea>
                    </div>

                    <h6 class="mt-4">使用示例</h6>
                    <div class="mb-3">
                        <label class="form-label">cURL</label>
                        <div class="input-group">
                            <input type="text" class="form-control font-monospace" value="curl -H 'Authorization: Bearer ${token.fullToken}' https://api.example.com/v1/data" id="curlExample" readonly>
                            <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('curlExample')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Python</label>
                        <div class="input-group">
                            <textarea class="form-control font-monospace" rows="4" id="pythonExample" readonly>import requests

headers = {
    'Authorization': 'Bearer ${token.fullToken}'
}
response = requests.get('https://api.example.com/v1/data', headers=headers)</textarea>
                            <button class="btn btn-outline-primary" type="button" onclick="copyToClipboard('pythonExample')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    // 添加到文档中
    document.body.appendChild(modal);

    // 初始化Bootstrap模态框
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

// 显示令牌详情
function showTokenDetail(tokenId) {
    // 获取令牌信息
    const token = apiTokens.find(t => t.id === tokenId);
    if (!token) return;

    // 获取模态框元素
    const modalContent = document.getElementById('tokenDetailContent');

    // 构建模态框内容
    modalContent.innerHTML = `
        <div class="mb-3">
            <label class="form-label">名称</label>
            <input type="text" class="form-control" value="${token.name}" readonly>
        </div>

        <div class="mb-3">
            <label class="form-label">令牌前缀</label>
            <input type="text" class="form-control" value="${token.token}" readonly>
        </div>

        <div class="row mb-3">
            <div class="col-md-4">
                <label class="form-label">权限级别</label>
                <input type="text" class="form-control" value="${getPermissionName(token.permission)}" readonly>
            </div>
            <div class="col-md-4">
                <label class="form-label">状态</label>
                <input type="text" class="form-control" value="${getStatusName(token.status)}" readonly>
            </div>
            <div class="col-md-4">
                <label class="form-label">最后使用时间</label>
                <input type="text" class="form-control" value="${token.lastUsed || '从未使用'}" readonly>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-6">
                <label class="form-label">创建日期</label>
                <input type="text" class="form-control" value="${token.created}" readonly>
            </div>
            <div class="col-md-6">
                <label class="form-label">过期时间</label>
                <input type="text" class="form-control" value="${token.expires === '0' ? '永不过期' : token.expires}" readonly>
            </div>
        </div>

        <div class="mb-3">
            <label class="form-label">访问范围</label>
            <div>
                ${token.scopes.map(scope => `<span class="badge bg-secondary me-1">${getScopeName(scope)}</span>`).join('')}
            </div>
        </div>

        <div class="mb-3">
            <label class="form-label">描述</label>
            <textarea class="form-control" rows="2" readonly>${token.description || '无描述'}</textarea>
        </div>

        <div class="d-flex justify-content-between mt-4">
            ${token.status === 'active' ?
                `<button class="btn btn-danger" onclick="revokeToken('${token.id}')">撤销令牌</button>` :
                `<button class="btn btn-secondary" onclick="deleteToken('${token.id}')">删除令牌</button>`
            }
        </div>
    `;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('tokenDetailModal'));
    modal.show();
}

// 撤销令牌
function revokeToken(tokenId) {
    if (confirm('确定要撤销此令牌吗？撤销后将无法使用该令牌访问API。')) {
        // 显示加载提示
        const loadingToast = showToast('正在撤销令牌...', 'info');

        // 模拟API调用
        setTimeout(() => {
            // 隐藏加载提示
            hideToast(loadingToast);

            // 更新令牌状态
            const tokenIndex = apiTokens.findIndex(t => t.id === tokenId);
            if (tokenIndex !== -1) {
                apiTokens[tokenIndex].status = 'revoked';
            }

            // 更新UI
            updateTokensUI();

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('tokenDetailModal'));
            if (modal) modal.hide();

            // 显示成功消息
            showToast('令牌已成功撤销！', 'success');
        }, 1000);
    }
}

// 删除令牌
function deleteToken(tokenId) {
    if (confirm('确定要删除此令牌吗？删除后将无法恢复。')) {
        // 显示加载提示
        const loadingToast = showToast('正在删除令牌...', 'info');

        // 模拟API调用
        setTimeout(() => {
            // 隐藏加载提示
            hideToast(loadingToast);

            // 从列表中移除令牌
            apiTokens = apiTokens.filter(t => t.id !== tokenId);

            // 更新UI
            updateTokensUI();

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('tokenDetailModal'));
            if (modal) modal.hide();

            // 显示成功消息
            showToast('令牌已成功删除！', 'success');
        }, 1000);
    }
}

// 复制到剪贴板
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    document.execCommand('copy');

    // 显示成功提示
    showToast('已复制到剪贴板', 'success');
}

// 获取权限级别名称
function getPermissionName(permission) {
    switch (permission) {
        case 'read': return '只读权限';
        case 'write': return '读写权限';
        case 'admin': return '管理员权限';
        default: return permission;
    }
}

// 获取状态名称
function getStatusName(status) {
    switch (status) {
        case 'active': return '有效';
        case 'revoked': return '已撤销';
        case 'expired': return '已过期';
        default: return status;
    }
}

// 获取范围名称
function getScopeName(scope) {
    switch (scope) {
        case 'market_data': return '市场数据';
        case 'portfolio': return '投资组合';
        case 'model': return '模型预测';
        case 'user': return '用户信息';
        default: return scope;
    }
}

// 从收藏中移除股票
function removeFromFavorites(ticker) {
    if (confirm(`确定要从收藏中移除 ${ticker} 吗？`)) {
        // 模拟API调用
        setTimeout(() => {
            // 从列表中移除股票
            favoriteStocks = favoriteStocks.filter(s => s.ticker !== ticker);

            // 更新UI
            updateFavoritesUI();

            // 显示成功消息
            alert(`${ticker} 已从收藏中移除！`);
        }, 1000);
    }
}

// 退出登录
function logout() {
    // 清除登录信息
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');

    // 重定向到首页
    window.location.href = '/';
}

// 获取订阅计划
function fetchSubscriptionPlans() {
    // 显示加载动画
    const plansContainer = document.getElementById('subscriptionPlans');
    plansContainer.innerHTML = '<div class="text-center py-3 col-12"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    // 模拟 API 调用
    setTimeout(() => {
        // 模拟数据
        subscriptionPlans = [
            {
                tier: 'basic',
                name: '入门版',
                description: '适合初学者和小额投资者的基础功能',
                price_monthly: 0.0,
                price_yearly: 0.0,
                features: [
                    { name: '基础行情数据', description: '获取基础股票和期货行情数据', included: true },
                    { name: '单一投资组合', description: '创建和管理一个投资组合', included: true },
                    { name: '基础技术指标', description: '使用常见技术指标如MA、MACD等', included: true },
                    { name: '基础AI预测', description: '基础AI模型预测功能', included: true },
                    { name: '高级技术指标', description: '使用高级技术指标', included: false },
                    { name: '实时行情数据', description: '获取实时行情数据', included: false },
                ],
                max_portfolios: 1,
                max_strategies: 1,
                real_time_data: false,
                advanced_indicators: false,
                ai_predictions: true,
                priority_support: false
            },
            {
                tier: 'advanced',
                name: '进阶版',
                description: '适合有一定经验的中等规模投资者',
                price_monthly: 199.0,
                price_yearly: 1990.0,
                features: [
                    { name: '全面行情数据', description: '获取全面股票和期货行情数据', included: true },
                    { name: '多投资组合', description: '创建和管理多个投资组合', included: true },
                    { name: '高级技术指标', description: '使用高级技术指标', included: true },
                    { name: '进阶AI预测', description: '进阶AI模型预测功能', included: true },
                    { name: '多策略支持', description: '使用多种交易策略', included: true },
                    { name: '优先客户支持', description: '获得优先客户支持', included: false },
                ],
                max_portfolios: 5,
                max_strategies: 3,
                real_time_data: true,
                advanced_indicators: true,
                ai_predictions: true,
                priority_support: false
            },
            {
                tier: 'professional',
                name: '专业版',
                description: '适合专业投资者和机构投资者',
                price_monthly: 599.0,
                price_yearly: 5990.0,
                features: [
                    { name: '全面行情数据', description: '获取全面股票和期货行情数据', included: true },
                    { name: '无限投资组合', description: '创建和管理无限数量的投资组合', included: true },
                    { name: '全部技术指标', description: '使用所有技术指标', included: true },
                    { name: '专业AI预测', description: '专业AI模型预测功能', included: true },
                    { name: '无限策略支持', description: '使用无限数量的交易策略', included: true },
                    { name: '优先客户支持', description: '获得优先客户支持', included: true },
                    { name: 'API访问', description: '通过API访问数据和功能', included: true },
                    { name: '定制化分析', description: '定制化数据分析和报告', included: true },
                ],
                max_portfolios: 999,
                max_strategies: 999,
                real_time_data: true,
                advanced_indicators: true,
                ai_predictions: true,
                priority_support: true
            }
        ];

        // 更新UI
        updateSubscriptionPlansUI();
    }, 1000);
}

// 获取当前订阅
function fetchCurrentSubscription() {
    // 显示加载动画
    const subscriptionContainer = document.getElementById('currentSubscription');
    subscriptionContainer.innerHTML = '<div class="text-center py-3"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    // 模拟 API 调用
    setTimeout(() => {
        // 模拟数据
        currentSubscription = {
            tier: 'basic',
            start_date: '2023-05-01',
            end_date: '2024-05-01',
            is_active: true,
            auto_renew: false,
            payment_method: 'credit_card'
        };

        // 更新UI
        updateCurrentSubscriptionUI();
    }, 1500);
}

// 更新订阅计划UI
function updateSubscriptionPlansUI() {
    const plansContainer = document.getElementById('subscriptionPlans');

    if (subscriptionPlans.length > 0) {
        plansContainer.innerHTML = '';

        // 创建每个计划的卡片
        subscriptionPlans.forEach(plan => {
            const planCard = document.createElement('div');
            planCard.className = 'col-md-4 mb-4';

            // 确定卡片样式
            let cardClass = 'card h-100';
            let headerClass = 'card-header text-center';
            let buttonClass = 'btn btn-outline-primary w-100';

            // 如果是当前订阅的计划，高亮显示
            if (currentSubscription && plan.tier === currentSubscription.tier) {
                cardClass = 'card h-100 border-primary';
                headerClass = 'card-header text-center bg-primary text-white';
                buttonClass = 'btn btn-primary w-100';
            }

            // 构建功能列表HTML
            const featuresHtml = plan.features.map(feature => {
                const iconClass = feature.included ? 'text-success' : 'text-muted';
                const icon = feature.included ? '✓' : '✗';
                return `<li class="list-group-item d-flex justify-content-between align-items-center">
                    <span>${feature.name}</span>
                    <span class="${iconClass}">${icon}</span>
                </li>`;
            }).join('');

            // 计算每月价格
            let priceDisplay = '';
            let yearlyPriceDisplay = '';

            if (plan.price_monthly <= 0) {
                // 免费版特殊显示
                priceDisplay = `<span class="text-success">免费</span>`;
                yearlyPriceDisplay = `<span class="badge bg-success">永久免费</span>`;
            } else {
                // 计算年付折扣
                const yearlyDiscount = ((plan.price_monthly * 12 - plan.price_yearly) / (plan.price_monthly * 12) * 100).toFixed(0);
                priceDisplay = `¥${plan.price_monthly.toFixed(0)}<small class="text-muted">/月</small>`;
                yearlyPriceDisplay = `年付: ¥${plan.price_yearly.toFixed(0)} (省${yearlyDiscount}%)`;
            }

            planCard.innerHTML = `
                <div class="${cardClass}">
                    <div class="${headerClass}">
                        <h4 class="my-0">${plan.name}</h4>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h1 class="card-title pricing-card-title text-center">
                            ${priceDisplay}
                        </h1>
                        <p class="text-center text-muted">${yearlyPriceDisplay}</p>
                        <p class="card-text">${plan.description}</p>
                        <ul class="list-group list-group-flush mb-4">
                            ${featuresHtml}
                        </ul>
                        <div class="mt-auto">
                            <button class="${buttonClass} subscribe-btn" data-tier="${plan.tier}">
                                ${currentSubscription && plan.tier === currentSubscription.tier ? '当前订阅' : '订阅此计划'}
                            </button>
                        </div>
                    </div>
                </div>
            `;

            plansContainer.appendChild(planCard);
        });
    } else {
        plansContainer.innerHTML = '<div class="col-12 text-center py-3">暂无订阅计划</div>';
    }
}

// 更新当前订阅UI
function updateCurrentSubscriptionUI() {
    const subscriptionContainer = document.getElementById('currentSubscription');

    if (currentSubscription) {
        // 获取对应的计划详情
        const plan = subscriptionPlans.find(p => p.tier === currentSubscription.tier) || {
            name: '未知计划',
            price_monthly: 0,
            price_yearly: 0
        };

        // 格式化日期
        const startDate = new Date(currentSubscription.start_date).toLocaleDateString('zh-CN');
        const endDate = new Date(currentSubscription.end_date).toLocaleDateString('zh-CN');

        // 计算剩余天数
        const today = new Date();
        const end = new Date(currentSubscription.end_date);
        const daysLeft = Math.ceil((end - today) / (1000 * 60 * 60 * 24));

        // 构建状态标签
        let statusBadge = '';
        if (currentSubscription.is_active) {
            statusBadge = '<span class="badge bg-success">有效</span>';
        } else {
            statusBadge = '<span class="badge bg-danger">已过期</span>';
        }

        // 构建自动续费标签
        let renewBadge = '';
        if (currentSubscription.auto_renew) {
            renewBadge = '<span class="badge bg-info">自动续费</span>';
        }

        // 检查是否是免费计划
        const isFreePlan = plan.price_monthly <= 0;

        // 构建价格显示
        let priceDisplay = isFreePlan ?
            '<span class="text-success">免费</span>' :
            `<span class="text-primary">¥${plan.price_monthly.toFixed(0)}/月</span>`;

        // 构建到期时间显示
        let expiryDisplay = isFreePlan ?
            '<span>永久有效</span>' :
            `<span>到期: ${endDate}</span>`;

        // 构建剩余时间显示
        let remainingDisplay = isFreePlan ?
            '<p class="mb-3">订阅状态: <strong class="text-success">永久有效</strong></p>' :
            `<p class="mb-3">订阅剩余: <strong>${daysLeft}</strong> 天</p>`;

        // 构建自动续费按钮
        let renewButtonDisplay = isFreePlan ?
            '' :
            `<div class="d-grid gap-2">
                ${currentSubscription.auto_renew ?
                    `<button id="cancelAutoRenewBtn" class="btn btn-outline-danger">取消自动续费</button>` :
                    `<button id="enableAutoRenewBtn" class="btn btn-outline-primary">开启自动续费</button>`
                }
            </div>`;

        subscriptionContainer.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">${plan.name} ${statusBadge} ${renewBadge}</h5>
                ${priceDisplay}
            </div>
            ${!isFreePlan ? `
            <div class="progress mb-3" style="height: 10px;">
                <div class="progress-bar bg-primary" role="progressbar" style="width: ${Math.max(0, Math.min(100, 100 - (daysLeft / 365 * 100)))}%"></div>
            </div>
            ` : ''}
            <div class="d-flex justify-content-between small text-muted mb-3">
                <span>开始: ${startDate}</span>
                ${expiryDisplay}
            </div>
            ${remainingDisplay}
            ${renewButtonDisplay}
        `;
    } else {
        subscriptionContainer.innerHTML = `
            <div class="text-center py-3">
                <p>您当前没有有效的订阅。</p>
                <p>请选择下方的订阅计划开始使用高级功能。</p>
            </div>
        `;
    }
}

// 显示订阅模态框
function showSubscriptionModal(tier) {
    // 获取计划详情
    const plan = subscriptionPlans.find(p => p.tier === tier);
    if (!plan) return;

    // 创建模态框
    const modalId = 'subscriptionModal';
    let modal = document.getElementById(modalId);

    // 如果模态框已存在，先移除
    if (modal) {
        document.body.removeChild(modal);
    }

    // 创建新的模态框
    modal = document.createElement('div');
    modal.id = modalId;
    modal.className = 'modal fade';
    modal.tabIndex = -1;
    modal.setAttribute('aria-labelledby', `${modalId}Label`);
    modal.setAttribute('aria-hidden', 'true');

    // 构建模态框内容
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="${modalId}Label">订阅 ${plan.name}</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>${plan.description}</p>
                    ${plan.price_monthly <= 0 ? `
                    <div class="alert alert-success mb-4">
                        <i class="bi bi-gift me-2"></i>
                        <strong>免费计划</strong> - 此计划完全免费，无需支付。
                    </div>
                    ` : `
                    <div class="mb-4">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="billingCycle" id="monthlyBilling" value="monthly" checked>
                            <label class="form-check-label" for="monthlyBilling">
                                月付 ¥${plan.price_monthly.toFixed(0)}/月
                            </label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="billingCycle" id="yearlyBilling" value="yearly">
                            <label class="form-check-label" for="yearlyBilling">
                                年付 ¥${plan.price_yearly.toFixed(0)}/年 (省${((plan.price_monthly * 12 - plan.price_yearly) / (plan.price_monthly * 12) * 100).toFixed(0)}%)
                            </label>
                        </div>
                    </div>
                    `}
                    ${plan.price_monthly <= 0 ? `
                    <div class="alert alert-info">
                        <small>注意：订阅成功后，您将立即获得该计划的所有功能。免费计划无需支付和续费。</small>
                    </div>
                    ` : `
                    <div class="mb-3">
                        <label for="paymentMethod" class="form-label">支付方式</label>
                        <select class="form-select" id="paymentMethod">
                            <option value="alipay">支付宝</option>
                            <option value="wechat">微信支付</option>
                            <option value="credit_card">信用卡</option>
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="autoRenew" checked>
                        <label class="form-check-label" for="autoRenew">
                            开启自动续费
                        </label>
                    </div>
                    <div class="alert alert-info">
                        <small>注意：订阅成功后，您将立即获得该计划的所有功能。如果开启自动续费，将在到期前自动续费。</small>
                    </div>
                    `}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmSubscription" data-tier="${plan.tier}">确认订阅</button>
                </div>
            </div>
        </div>
    `;

    // 添加到文档中
    document.body.appendChild(modal);

    // 初始化Bootstrap模态框
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    // 添加确认订阅事件
    document.getElementById('confirmSubscription').addEventListener('click', function() {
        const tier = this.getAttribute('data-tier');
        const billingCycle = document.querySelector('input[name="billingCycle"]:checked').value;
        const paymentMethod = document.getElementById('paymentMethod').value;
        const autoRenew = document.getElementById('autoRenew').checked;

        // 关闭模态框
        modalInstance.hide();

        // 处理订阅
        processSubscription(tier, billingCycle, paymentMethod, autoRenew);
    });
}

// 处理订阅
function processSubscription(tier, billingCycle, paymentMethod, autoRenew) {
    // 获取计划详情
    const plan = subscriptionPlans.find(p => p.tier === tier);
    if (!plan) return;

    // 显示加载提示
    const loadingToast = showToast('正在处理您的订阅请求...', 'info');

    // 模拟 API 调用
    setTimeout(() => {
        // 隐藏加载提示
        hideToast(loadingToast);

        // 如果是免费计划，设置特殊属性
        const isFreePlan = plan.price_monthly <= 0;

        // 更新当前订阅
        currentSubscription = {
            tier: tier,
            start_date: new Date().toISOString().split('T')[0],
            // 免费计划设置为永久有效
            end_date: isFreePlan ? '2099-12-31' : new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
            is_active: true,
            // 免费计划不需要自动续费
            auto_renew: isFreePlan ? false : autoRenew,
            // 免费计划无需支付方式
            payment_method: isFreePlan ? 'free' : paymentMethod
        };

        // 更新UI
        updateCurrentSubscriptionUI();
        updateSubscriptionPlansUI();

        // 显示成功提示
        if (isFreePlan) {
            showToast('恭喜！您已成功订阅免费版，可以使用基础功能。', 'success');
        } else {
            showToast('订阅成功！您现在可以使用所有高级功能。', 'success');
        }
    }, 2000);
}

// 取消自动续费
function cancelAutoRenew() {
    if (confirm('确定要取消自动续费吗？到期后将不再自动扣费。')) {
        // 显示加载提示
        const loadingToast = showToast('正在处理您的请求...', 'info');

        // 模拟 API 调用
        setTimeout(() => {
            // 隐藏加载提示
            hideToast(loadingToast);

            // 更新自动续费状态
            currentSubscription.auto_renew = false;

            // 更新UI
            updateCurrentSubscriptionUI();

            // 显示成功提示
            showToast('已成功取消自动续费。', 'success');
        }, 1000);
    }
}

// 开启自动续费
function enableAutoRenew() {
    if (confirm('确定要开启自动续费吗？到期后将自动扣费续订。')) {
        // 显示加载提示
        const loadingToast = showToast('正在处理您的请求...', 'info');

        // 模拟 API 调用
        setTimeout(() => {
            // 隐藏加载提示
            hideToast(loadingToast);

            // 更新自动续费状态
            currentSubscription.auto_renew = true;

            // 更新UI
            updateCurrentSubscriptionUI();

            // 显示成功提示
            showToast('已成功开启自动续费。', 'success');
        }, 1000);
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建提示容器
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();

    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="关闭"></button>
        </div>
    `;

    // 添加到容器
    toastContainer.appendChild(toast);

    // 初始化Bootstrap提示
    const toastInstance = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });

    // 显示提示
    toastInstance.show();

    // 返回提示实例，便于后续操作
    return { element: toast, instance: toastInstance };
}

// 隐藏提示消息
function hideToast(toast) {
    if (toast && toast.instance) {
        toast.instance.hide();
    }
}

// 创建提示容器
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1050';
    document.body.appendChild(container);
    return container;
}

// 更新第三方账号绑定状态
function updateThirdPartyBindStatus() {
    // 如果用户对象中没有第三方账号绑定信息，初始化为空对象
    if (!currentUser.thirdPartyAccounts) {
        currentUser.thirdPartyAccounts = {};
    }

    // 更新微信绑定状态
    const wechatBindStatus = document.getElementById('wechatBindStatus');
    const bindWechatBtn = document.getElementById('bindWechatBtn');

    if (currentUser.thirdPartyAccounts.wechat) {
        wechatBindStatus.textContent = '已绑定微信';
        bindWechatBtn.classList.remove('btn-outline-success');
        bindWechatBtn.classList.add('btn-success');
    } else {
        wechatBindStatus.textContent = '绑定微信';
        bindWechatBtn.classList.remove('btn-success');
        bindWechatBtn.classList.add('btn-outline-success');
    }

    // 更新支付宝绑定状态
    const alipayBindStatus = document.getElementById('alipayBindStatus');
    const bindAlipayBtn = document.getElementById('bindAlipayBtn');

    if (currentUser.thirdPartyAccounts.alipay) {
        alipayBindStatus.textContent = '已绑定支付宝';
        bindAlipayBtn.classList.remove('btn-outline-primary');
        bindAlipayBtn.classList.add('btn-primary');
    } else {
        alipayBindStatus.textContent = '绑定支付宝';
        bindAlipayBtn.classList.remove('btn-primary');
        bindAlipayBtn.classList.add('btn-outline-primary');
    }
}

// 绑定第三方账号
function bindThirdPartyAccount(type) {
    // 如果已绑定，则解绑
    if (currentUser.thirdPartyAccounts && currentUser.thirdPartyAccounts[type]) {
        if (confirm(`确定要解除${type === 'wechat' ? '微信' : '支付宝'}绑定吗？`)) {
            // 显示加载提示
            const loadingToast = showToast('正在解除绑定...', 'info');

            // 模拟 API 调用
            setTimeout(() => {
                // 隐藏加载提示
                hideToast(loadingToast);

                // 解除绑定
                delete currentUser.thirdPartyAccounts[type];

                // 更新本地存储
                localStorage.setItem('user', JSON.stringify(currentUser));

                // 更新UI
                updateThirdPartyBindStatus();

                // 显示成功提示
                showToast(`已成功解除${type === 'wechat' ? '微信' : '支付宝'}绑定`, 'success');
            }, 1000);
        }
    } else {
        // 如果未绑定，则绑定
        // 模拟第三方授权过程
        // 在真实应用中，这里应该跳转到第三方授权页面

        // 显示加载提示
        const loadingToast = showToast(`正在跳转到${type === 'wechat' ? '微信' : '支付宝'}授权页面...`, 'info');

        // 模拟授权过程
        setTimeout(() => {
            // 隐藏加载提示
            hideToast(loadingToast);

            // 模拟授权成功
            if (!currentUser.thirdPartyAccounts) {
                currentUser.thirdPartyAccounts = {};
            }

            // 设置绑定信息
            currentUser.thirdPartyAccounts[type] = {
                bindTime: new Date().toISOString(),
                accountId: type === 'wechat' ? 'wx_' + Math.random().toString(36).substring(2, 10) : 'alipay_' + Math.random().toString(36).substring(2, 10)
            };

            // 更新本地存储
            localStorage.setItem('user', JSON.stringify(currentUser));

            // 更新UI
            updateThirdPartyBindStatus();

            // 显示成功提示
            showToast(`已成功绑定${type === 'wechat' ? '微信' : '支付宝'}账号`, 'success');
        }, 2000);
    }
}
