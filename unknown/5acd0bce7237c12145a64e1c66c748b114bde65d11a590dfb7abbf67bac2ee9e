"""
Performance attribution functions for the AI Hedge Fund module.

This file provides functions for analyzing the sources of portfolio performance.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import matplotlib.pyplot as plt
import io
import base64

from ..tools.api import get_price_data, get_sector_data, get_market_cap


def calculate_attribution(
    portfolio: Dict[str, Any],
    benchmark: Optional[str] = "SPY",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Calculate performance attribution for a portfolio.

    Args:
        portfolio: Portfolio dictionary with positions and weights
        benchmark: Benchmark ticker symbol (default: SPY)
        start_date: Start date for analysis (YYYY-MM-DD)
        end_date: End date for analysis (YYYY-MM-DD)

    Returns:
        Dictionary with attribution analysis results
    """
    # Initialize results
    results = {
        "status": "success",
        "message": "Attribution analysis completed successfully",
        "portfolio_return": 0.0,
        "benchmark_return": 0.0,
        "active_return": 0.0,
        "attribution": {},
    }

    # Extract portfolio positions
    positions = portfolio.get("positions", {})

    if not positions:
        return {
            "status": "error",
            "message": "Portfolio has no positions",
        }

    # Calculate portfolio weights
    total_value = portfolio.get("total_value", 0.0)

    if total_value <= 0:
        return {
            "status": "error",
            "message": "Invalid portfolio value",
        }

    weights = {}
    tickers = []

    for ticker, position in positions.items():
        # Skip cash positions
        if ticker == "cash":
            continue

        # Calculate position value
        long_value = position.get("long_value", 0.0)
        short_value = position.get("short_value", 0.0)
        position_value = long_value - short_value

        # Calculate weight
        weight = position_value / total_value

        weights[ticker] = weight
        tickers.append(ticker)

    # Get price data for portfolio and benchmark
    if benchmark:
        tickers.append(benchmark)

    # Get returns for each ticker
    returns = {}
    for ticker in tickers:
        price_data = get_price_data(ticker, start_date, end_date)

        if price_data.empty:
            continue

        # Calculate return
        start_price = price_data["close"].iloc[0]
        end_price = price_data["close"].iloc[-1]
        ticker_return = (end_price / start_price - 1) * 100

        returns[ticker] = ticker_return

    # Calculate portfolio return
    portfolio_return = sum(weights.get(ticker, 0) * returns.get(ticker, 0) for ticker in weights)

    # Calculate benchmark return
    benchmark_return = returns.get(benchmark, 0) if benchmark else 0

    # Calculate active return
    active_return = portfolio_return - benchmark_return

    # Calculate attribution
    attribution = {}
    for ticker in weights:
        if ticker in returns:
            # Calculate contribution to return
            contribution = weights[ticker] * returns[ticker]

            # Calculate active contribution
            active_contribution = weights[ticker] * (returns[ticker] - benchmark_return)

            attribution[ticker] = {
                "weight": weights[ticker],
                "return": returns[ticker],
                "contribution": contribution,
                "active_contribution": active_contribution,
            }

    # Store results
    results["portfolio_return"] = portfolio_return
    results["benchmark_return"] = benchmark_return
    results["active_return"] = active_return
    results["attribution"] = attribution

    return results


def calculate_sector_attribution(
    portfolio: Dict[str, Any],
    benchmark: Optional[str] = "SPY",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Calculate sector-based performance attribution for a portfolio.

    Args:
        portfolio: Portfolio dictionary with positions and weights
        benchmark: Benchmark ticker symbol (default: SPY)
        start_date: Start date for analysis (YYYY-MM-DD)
        end_date: End date for analysis (YYYY-MM-DD)

    Returns:
        Dictionary with sector attribution analysis results
    """
    # Initialize results
    results = {
        "status": "success",
        "message": "Sector attribution analysis completed successfully",
        "portfolio_return": 0.0,
        "benchmark_return": 0.0,
        "active_return": 0.0,
        "sector_attribution": {},
    }

    # Extract portfolio positions
    positions = portfolio.get("positions", {})

    if not positions:
        return {
            "status": "error",
            "message": "Portfolio has no positions",
        }

    # Calculate portfolio weights
    total_value = portfolio.get("total_value", 0.0)

    if total_value <= 0:
        return {
            "status": "error",
            "message": "Invalid portfolio value",
        }

    weights = {}
    tickers = []

    for ticker, position in positions.items():
        # Skip cash positions
        if ticker == "cash":
            continue

        # Calculate position value
        long_value = position.get("long_value", 0.0)
        short_value = position.get("short_value", 0.0)
        position_value = long_value - short_value

        # Calculate weight
        weight = position_value / total_value

        weights[ticker] = weight
        tickers.append(ticker)

    # Get sector data for portfolio tickers
    sector_data = {}
    for ticker in tickers:
        sector = get_sector_data(ticker)
        if sector:
            sector_data[ticker] = sector

    # Get benchmark sector weights
    benchmark_sector_weights = {}
    if benchmark:
        # For simplicity, we'll use a predefined sector breakdown for common benchmarks
        # In a real implementation, this would be fetched from an API or database
        if benchmark == "SPY":
            benchmark_sector_weights = {
                "Technology": 0.28,
                "Healthcare": 0.14,
                "Financials": 0.13,
                "Consumer Discretionary": 0.10,
                "Communication Services": 0.08,
                "Industrials": 0.08,
                "Consumer Staples": 0.07,
                "Energy": 0.05,
                "Utilities": 0.03,
                "Real Estate": 0.03,
                "Materials": 0.03,
            }
        elif benchmark == "QQQ":
            benchmark_sector_weights = {
                "Technology": 0.50,
                "Communication Services": 0.18,
                "Consumer Discretionary": 0.17,
                "Healthcare": 0.07,
                "Industrials": 0.04,
                "Consumer Staples": 0.04,
                "Utilities": 0.0,
                "Energy": 0.0,
                "Financials": 0.0,
                "Materials": 0.0,
                "Real Estate": 0.0,
            }

    # Calculate portfolio sector weights
    portfolio_sector_weights = {}
    for ticker, weight in weights.items():
        sector = sector_data.get(ticker, "Unknown")
        if sector in portfolio_sector_weights:
            portfolio_sector_weights[sector] += weight
        else:
            portfolio_sector_weights[sector] = weight

    # Get returns for each ticker
    returns = {}
    for ticker in tickers:
        price_data = get_price_data(ticker, start_date, end_date)

        if price_data.empty:
            continue

        # Calculate return
        start_price = price_data["close"].iloc[0]
        end_price = price_data["close"].iloc[-1]
        ticker_return = (end_price / start_price - 1) * 100

        returns[ticker] = ticker_return

    # Calculate sector returns
    sector_returns = {}
    for ticker, ticker_return in returns.items():
        sector = sector_data.get(ticker, "Unknown")
        if sector not in sector_returns:
            sector_returns[sector] = []

        sector_returns[sector].append(ticker_return)

    # Calculate average sector returns
    avg_sector_returns = {}
    for sector, sector_return_list in sector_returns.items():
        avg_sector_returns[sector] = sum(sector_return_list) / len(sector_return_list)

    # Calculate benchmark return
    benchmark_return = 0.0
    if benchmark:
        benchmark_price_data = get_price_data(benchmark, start_date, end_date)

        if not benchmark_price_data.empty:
            start_price = benchmark_price_data["close"].iloc[0]
            end_price = benchmark_price_data["close"].iloc[-1]
            benchmark_return = (end_price / start_price - 1) * 100

    # Calculate portfolio return
    portfolio_return = sum(weights.get(ticker, 0) * returns.get(ticker, 0) for ticker in weights)

    # Calculate active return
    active_return = portfolio_return - benchmark_return

    # Calculate sector attribution
    sector_attribution = {}
    for sector, weight in portfolio_sector_weights.items():
        benchmark_weight = benchmark_sector_weights.get(sector, 0.0)
        sector_return = avg_sector_returns.get(sector, 0.0)

        # Calculate allocation effect (weight difference * benchmark return)
        allocation_effect = (weight - benchmark_weight) * benchmark_return

        # Calculate selection effect (sector return difference * benchmark weight)
        selection_effect = (sector_return - benchmark_return) * benchmark_weight

        # Calculate interaction effect (weight difference * return difference)
        interaction_effect = (weight - benchmark_weight) * (sector_return - benchmark_return)

        # Calculate total effect
        total_effect = allocation_effect + selection_effect + interaction_effect

        sector_attribution[sector] = {
            "portfolio_weight": weight,
            "benchmark_weight": benchmark_weight,
            "sector_return": sector_return,
            "allocation_effect": allocation_effect,
            "selection_effect": selection_effect,
            "interaction_effect": interaction_effect,
            "total_effect": total_effect,
        }

    # Store results
    results["portfolio_return"] = portfolio_return
    results["benchmark_return"] = benchmark_return
    results["active_return"] = active_return
    results["sector_attribution"] = sector_attribution

    return results


def calculate_style_attribution(
    portfolio: Dict[str, Any],
    benchmark: Optional[str] = "SPY",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Calculate style-based performance attribution for a portfolio.

    Args:
        portfolio: Portfolio dictionary with positions and weights
        benchmark: Benchmark ticker symbol (default: SPY)
        start_date: Start date for analysis (YYYY-MM-DD)
        end_date: End date for analysis (YYYY-MM-DD)

    Returns:
        Dictionary with style attribution analysis results
    """
    # Initialize results
    results = {
        "status": "success",
        "message": "Style attribution analysis completed successfully",
        "portfolio_return": 0.0,
        "benchmark_return": 0.0,
        "active_return": 0.0,
        "style_attribution": {},
    }

    # Extract portfolio positions
    positions = portfolio.get("positions", {})

    if not positions:
        return {
            "status": "error",
            "message": "Portfolio has no positions",
        }

    # Calculate portfolio weights
    total_value = portfolio.get("total_value", 0.0)

    if total_value <= 0:
        return {
            "status": "error",
            "message": "Invalid portfolio value",
        }

    weights = {}
    tickers = []

    for ticker, position in positions.items():
        # Skip cash positions
        if ticker == "cash":
            continue

        # Calculate position value
        long_value = position.get("long_value", 0.0)
        short_value = position.get("short_value", 0.0)
        position_value = long_value - short_value

        # Calculate weight
        weight = position_value / total_value

        weights[ticker] = weight
        tickers.append(ticker)

    # Get market cap data for portfolio tickers
    market_caps = {}
    for ticker in tickers:
        market_cap = get_market_cap(ticker, end_date)
        if market_cap:
            market_caps[ticker] = market_cap

    # Classify stocks by market cap
    size_classifications = {}
    for ticker, market_cap in market_caps.items():
        if market_cap > 10e9:  # Large cap (>$10B)
            size_classifications[ticker] = "Large Cap"
        elif market_cap > 2e9:  # Mid cap ($2B-$10B)
            size_classifications[ticker] = "Mid Cap"
        else:  # Small cap (<$2B)
            size_classifications[ticker] = "Small Cap"

    # Get financial metrics for portfolio tickers to determine value/growth
    # For simplicity, we'll use a mock classification
    # In a real implementation, this would be based on P/E, P/B, etc.
    style_classifications = {}
    for ticker in tickers:
        # Mock classification - in reality, this would be based on financial metrics
        if ticker in ["AAPL", "MSFT", "AMZN", "GOOGL", "META", "TSLA"]:
            style_classifications[ticker] = "Growth"
        elif ticker in ["JPM", "BAC", "WFC", "XOM", "CVX", "PG", "JNJ"]:
            style_classifications[ticker] = "Value"
        else:
            style_classifications[ticker] = "Blend"

    # Combine size and style classifications
    combined_classifications = {}
    for ticker in tickers:
        size = size_classifications.get(ticker, "Unknown")
        style = style_classifications.get(ticker, "Unknown")
        combined_classifications[ticker] = f"{size} {style}"

    # Get returns for each ticker
    returns = {}
    for ticker in tickers:
        price_data = get_price_data(ticker, start_date, end_date)

        if price_data.empty:
            continue

        # Calculate return
        start_price = price_data["close"].iloc[0]
        end_price = price_data["close"].iloc[-1]
        ticker_return = (end_price / start_price - 1) * 100

        returns[ticker] = ticker_return

    # Calculate style returns
    style_returns = {}
    for ticker, ticker_return in returns.items():
        style = combined_classifications.get(ticker, "Unknown")
        if style not in style_returns:
            style_returns[style] = []

        style_returns[style].append(ticker_return)

    # Calculate average style returns
    avg_style_returns = {}
    for style, style_return_list in style_returns.items():
        avg_style_returns[style] = sum(style_return_list) / len(style_return_list)

    # Calculate portfolio style weights
    portfolio_style_weights = {}
    for ticker, weight in weights.items():
        style = combined_classifications.get(ticker, "Unknown")
        if style in portfolio_style_weights:
            portfolio_style_weights[style] += weight
        else:
            portfolio_style_weights[style] = weight

    # Get benchmark style weights
    # For simplicity, we'll use a predefined style breakdown for common benchmarks
    benchmark_style_weights = {}
    if benchmark:
        if benchmark == "SPY":
            benchmark_style_weights = {
                "Large Cap Growth": 0.30,
                "Large Cap Value": 0.25,
                "Large Cap Blend": 0.20,
                "Mid Cap Growth": 0.08,
                "Mid Cap Value": 0.07,
                "Mid Cap Blend": 0.05,
                "Small Cap Growth": 0.02,
                "Small Cap Value": 0.02,
                "Small Cap Blend": 0.01,
            }
        elif benchmark == "QQQ":
            benchmark_style_weights = {
                "Large Cap Growth": 0.70,
                "Large Cap Value": 0.05,
                "Large Cap Blend": 0.15,
                "Mid Cap Growth": 0.08,
                "Mid Cap Value": 0.01,
                "Mid Cap Blend": 0.01,
                "Small Cap Growth": 0.0,
                "Small Cap Value": 0.0,
                "Small Cap Blend": 0.0,
            }

    # Calculate benchmark return
    benchmark_return = 0.0
    if benchmark:
        benchmark_price_data = get_price_data(benchmark, start_date, end_date)

        if not benchmark_price_data.empty:
            start_price = benchmark_price_data["close"].iloc[0]
            end_price = benchmark_price_data["close"].iloc[-1]
            benchmark_return = (end_price / start_price - 1) * 100

    # Calculate portfolio return
    portfolio_return = sum(weights.get(ticker, 0) * returns.get(ticker, 0) for ticker in weights)

    # Calculate active return
    active_return = portfolio_return - benchmark_return

    # Calculate style attribution
    style_attribution = {}
    for style, weight in portfolio_style_weights.items():
        benchmark_weight = benchmark_style_weights.get(style, 0.0)
        style_return = avg_style_returns.get(style, 0.0)

        # Calculate allocation effect (weight difference * benchmark return)
        allocation_effect = (weight - benchmark_weight) * benchmark_return

        # Calculate selection effect (style return difference * benchmark weight)
        selection_effect = (style_return - benchmark_return) * benchmark_weight

        # Calculate interaction effect (weight difference * return difference)
        interaction_effect = (weight - benchmark_weight) * (style_return - benchmark_return)

        # Calculate total effect
        total_effect = allocation_effect + selection_effect + interaction_effect

        style_attribution[style] = {
            "portfolio_weight": weight,
            "benchmark_weight": benchmark_weight,
            "style_return": style_return,
            "allocation_effect": allocation_effect,
            "selection_effect": selection_effect,
            "interaction_effect": interaction_effect,
            "total_effect": total_effect,
        }

    # Store results
    results["portfolio_return"] = portfolio_return
    results["benchmark_return"] = benchmark_return
    results["active_return"] = active_return
    results["style_attribution"] = style_attribution

    return results


def calculate_factor_attribution(
    portfolio: Dict[str, Any],
    benchmark: Optional[str] = "SPY",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Calculate factor-based performance attribution for a portfolio.

    Args:
        portfolio: Portfolio dictionary with positions and weights
        benchmark: Benchmark ticker symbol (default: SPY)
        start_date: Start date for analysis (YYYY-MM-DD)
        end_date: End date for analysis (YYYY-MM-DD)

    Returns:
        Dictionary with factor attribution analysis results
    """
    # Initialize results
    results = {
        "status": "success",
        "message": "Factor attribution analysis completed successfully",
        "portfolio_return": 0.0,
        "benchmark_return": 0.0,
        "active_return": 0.0,
        "factor_attribution": {},
    }

    # Extract portfolio positions
    positions = portfolio.get("positions", {})

    if not positions:
        return {
            "status": "error",
            "message": "Portfolio has no positions",
        }

    # Calculate portfolio weights
    total_value = portfolio.get("total_value", 0.0)

    if total_value <= 0:
        return {
            "status": "error",
            "message": "Invalid portfolio value",
        }

    weights = {}
    tickers = []

    for ticker, position in positions.items():
        # Skip cash positions
        if ticker == "cash":
            continue

        # Calculate position value
        long_value = position.get("long_value", 0.0)
        short_value = position.get("short_value", 0.0)
        position_value = long_value - short_value

        # Calculate weight
        weight = position_value / total_value

        weights[ticker] = weight
        tickers.append(ticker)

    # Define factors
    factors = [
        "Market",
        "Size",
        "Value",
        "Momentum",
        "Quality",
        "Volatility",
    ]

    # Define factor returns (mock data)
    # In a real implementation, this would be fetched from a factor database
    factor_returns = {
        "Market": 5.0,  # Market factor return
        "Size": -1.0,  # Size factor return (small minus big)
        "Value": 2.0,  # Value factor return (value minus growth)
        "Momentum": 3.0,  # Momentum factor return (winners minus losers)
        "Quality": 1.5,  # Quality factor return (high quality minus low quality)
        "Volatility": -2.0,  # Volatility factor return (low vol minus high vol)
    }

    # Define factor exposures for each ticker (mock data)
    # In a real implementation, this would be calculated from financial data
    factor_exposures = {}
    for ticker in tickers:
        # Mock factor exposures
        if ticker in ["AAPL", "MSFT", "AMZN", "GOOGL", "META"]:
            factor_exposures[ticker] = {
                "Market": 1.1,  # High market beta
                "Size": -0.8,  # Large cap (negative size exposure)
                "Value": -0.5,  # Growth (negative value exposure)
                "Momentum": 0.7,  # Positive momentum
                "Quality": 0.6,  # High quality
                "Volatility": -0.3,  # Medium volatility
            }
        elif ticker in ["JPM", "BAC", "WFC", "GS"]:
            factor_exposures[ticker] = {
                "Market": 1.2,  # High market beta
                "Size": -0.7,  # Large cap (negative size exposure)
                "Value": 0.8,  # Value (positive value exposure)
                "Momentum": -0.2,  # Negative momentum
                "Quality": 0.3,  # Medium quality
                "Volatility": -0.1,  # Medium volatility
            }
        elif ticker in ["XOM", "CVX", "COP"]:
            factor_exposures[ticker] = {
                "Market": 0.9,  # Medium market beta
                "Size": -0.6,  # Large cap (negative size exposure)
                "Value": 0.9,  # Value (positive value exposure)
                "Momentum": 0.5,  # Positive momentum
                "Quality": 0.2,  # Medium quality
                "Volatility": -0.4,  # Medium volatility
            }
        else:
            # Default exposures for other stocks
            factor_exposures[ticker] = {
                "Market": 1.0,  # Market beta
                "Size": 0.0,  # Neutral size
                "Value": 0.0,  # Neutral value/growth
                "Momentum": 0.0,  # Neutral momentum
                "Quality": 0.0,  # Neutral quality
                "Volatility": 0.0,  # Neutral volatility
            }

    # Define benchmark factor exposures
    benchmark_factor_exposures = {
        "Market": 1.0,  # Market beta
        "Size": -0.2,  # Slight large cap bias
        "Value": 0.1,  # Slight value bias
        "Momentum": 0.1,  # Slight momentum bias
        "Quality": 0.2,  # Slight quality bias
        "Volatility": -0.1,  # Slight volatility bias
    }

    # Calculate portfolio factor exposures
    portfolio_factor_exposures = {}
    for factor in factors:
        portfolio_factor_exposures[factor] = sum(
            weights.get(ticker, 0) * factor_exposures.get(ticker, {}).get(factor, 0)
            for ticker in weights
        )

    # Get returns for each ticker
    returns = {}
    for ticker in tickers:
        price_data = get_price_data(ticker, start_date, end_date)

        if price_data.empty:
            continue

        # Calculate return
        start_price = price_data["close"].iloc[0]
        end_price = price_data["close"].iloc[-1]
        ticker_return = (end_price / start_price - 1) * 100

        returns[ticker] = ticker_return

    # Calculate benchmark return
    benchmark_return = 0.0
    if benchmark:
        benchmark_price_data = get_price_data(benchmark, start_date, end_date)

        if not benchmark_price_data.empty:
            start_price = benchmark_price_data["close"].iloc[0]
            end_price = benchmark_price_data["close"].iloc[-1]
            benchmark_return = (end_price / start_price - 1) * 100

    # Calculate portfolio return
    portfolio_return = sum(weights.get(ticker, 0) * returns.get(ticker, 0) for ticker in weights)

    # Calculate active return
    active_return = portfolio_return - benchmark_return

    # Calculate factor attribution
    factor_attribution = {}
    total_factor_contribution = 0.0

    for factor in factors:
        # Calculate active factor exposure
        active_exposure = portfolio_factor_exposures.get(factor, 0) - benchmark_factor_exposures.get(factor, 0)

        # Calculate factor contribution to active return
        factor_contribution = active_exposure * factor_returns.get(factor, 0)
        total_factor_contribution += factor_contribution

        factor_attribution[factor] = {
            "portfolio_exposure": portfolio_factor_exposures.get(factor, 0),
            "benchmark_exposure": benchmark_factor_exposures.get(factor, 0),
            "active_exposure": active_exposure,
            "factor_return": factor_returns.get(factor, 0),
            "contribution": factor_contribution,
        }

    # Calculate specific (non-factor) return
    specific_return = active_return - total_factor_contribution

    # Store results
    results["portfolio_return"] = portfolio_return
    results["benchmark_return"] = benchmark_return
    results["active_return"] = active_return
    results["factor_attribution"] = factor_attribution
    results["total_factor_contribution"] = total_factor_contribution
    results["specific_return"] = specific_return

    return results


def plot_attribution_chart(attribution_data: Dict[str, Any], chart_type: str = "factor") -> str:
    """
    Plot attribution analysis chart.

    Args:
        attribution_data: Attribution analysis data
        chart_type: Type of attribution chart ("factor", "sector", or "style")

    Returns:
        Base64-encoded image of the chart
    """
    # Create the plot
    fig, ax = plt.subplots(figsize=(12, 8))

    # Extract data based on chart type
    if chart_type == "factor":
        if "factor_attribution" not in attribution_data:
            # Create a simple error plot
            ax.text(0.5, 0.5, "No factor attribution data available", ha="center", va="center", fontsize=14)
            ax.set_title("Factor Attribution Analysis")
            ax.axis("off")

            # Convert plot to base64-encoded image
            buffer = io.BytesIO()
            plt.savefig(buffer, format="png")
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
            plt.close()

            return image_base64

        # Extract factor data
        factors = []
        contributions = []
        colors = []

        for factor, data in attribution_data["factor_attribution"].items():
            factors.append(factor)
            contribution = data.get("contribution", 0.0)
            contributions.append(contribution)

            # Set color based on contribution
            if contribution > 0:
                colors.append("green")
            else:
                colors.append("red")

        # Add specific return
        factors.append("Specific")
        specific_return = attribution_data.get("specific_return", 0.0)
        contributions.append(specific_return)
        if specific_return > 0:
            colors.append("blue")
        else:
            colors.append("purple")

        # Create waterfall chart
        cumulative = 0
        for i, contribution in enumerate(contributions):
            if i == 0:  # First bar starts at 0
                ax.bar(factors[i], contribution, bottom=0, color=colors[i], alpha=0.7)
                cumulative += contribution
            elif i == len(contributions) - 1:  # Last bar (specific return)
                ax.bar(factors[i], contribution, bottom=cumulative, color=colors[i], alpha=0.7)
                cumulative += contribution
            else:  # Middle bars
                ax.bar(factors[i], contribution, bottom=cumulative, color=colors[i], alpha=0.7)
                cumulative += contribution

        # Add total active return bar
        active_return = attribution_data.get("active_return", 0.0)
        ax.bar("Total Active", active_return, color="black", alpha=0.7)

        # Add horizontal line at y=0
        ax.axhline(y=0, color="gray", linestyle="-", alpha=0.5)

        # Format the plot
        ax.set_ylabel("Contribution to Active Return (%)")
        ax.set_title("Factor Attribution Analysis")
        ax.grid(True, axis="y", alpha=0.3)

        # Add value labels on top of bars
        for i, contribution in enumerate(contributions):
            if abs(contribution) > 0.1:  # Only label significant contributions
                ax.text(
                    i,
                    contribution / 2 if contribution > 0 else contribution / 2,
                    f"{contribution:.2f}%",
                    ha="center",
                    va="center",
                    fontweight="bold",
                    color="white" if abs(contribution) > 1 else "black",
                )

        # Label total active return
        ax.text(
            len(contributions),
            active_return / 2 if active_return > 0 else active_return / 2,
            f"{active_return:.2f}%",
            ha="center",
            va="center",
            fontweight="bold",
            color="white" if abs(active_return) > 1 else "black",
        )

    elif chart_type == "sector":
        if "sector_attribution" not in attribution_data:
            # Create a simple error plot
            ax.text(0.5, 0.5, "No sector attribution data available", ha="center", va="center", fontsize=14)
            ax.set_title("Sector Attribution Analysis")
            ax.axis("off")

            # Convert plot to base64-encoded image
            buffer = io.BytesIO()
            plt.savefig(buffer, format="png")
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
            plt.close()

            return image_base64

        # Extract sector data
        sectors = []
        allocation_effects = []
        selection_effects = []
        interaction_effects = []
        total_effects = []

        for sector, data in attribution_data["sector_attribution"].items():
            sectors.append(sector)
            allocation_effects.append(data.get("allocation_effect", 0.0))
            selection_effects.append(data.get("selection_effect", 0.0))
            interaction_effects.append(data.get("interaction_effect", 0.0))
            total_effects.append(data.get("total_effect", 0.0))

        # Create stacked bar chart
        width = 0.8
        ax.bar(sectors, allocation_effects, width, label="Allocation Effect", alpha=0.7, color="blue")
        ax.bar(sectors, selection_effects, width, bottom=allocation_effects, label="Selection Effect", alpha=0.7, color="green")
        ax.bar(
            sectors,
            interaction_effects,
            width,
            bottom=[a + s for a, s in zip(allocation_effects, selection_effects)],
            label="Interaction Effect",
            alpha=0.7,
            color="orange",
        )

        # Add horizontal line at y=0
        ax.axhline(y=0, color="gray", linestyle="-", alpha=0.5)

        # Format the plot
        ax.set_ylabel("Contribution to Active Return (%)")
        ax.set_title("Sector Attribution Analysis")
        ax.grid(True, axis="y", alpha=0.3)
        ax.legend()

        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45, ha="right")

    elif chart_type == "style":
        if "style_attribution" not in attribution_data:
            # Create a simple error plot
            ax.text(0.5, 0.5, "No style attribution data available", ha="center", va="center", fontsize=14)
            ax.set_title("Style Attribution Analysis")
            ax.axis("off")

            # Convert plot to base64-encoded image
            buffer = io.BytesIO()
            plt.savefig(buffer, format="png")
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
            plt.close()

            return image_base64

        # Extract style data
        styles = []
        allocation_effects = []
        selection_effects = []
        interaction_effects = []
        total_effects = []

        for style, data in attribution_data["style_attribution"].items():
            styles.append(style)
            allocation_effects.append(data.get("allocation_effect", 0.0))
            selection_effects.append(data.get("selection_effect", 0.0))
            interaction_effects.append(data.get("interaction_effect", 0.0))
            total_effects.append(data.get("total_effect", 0.0))

        # Create stacked bar chart
        width = 0.8
        ax.bar(styles, allocation_effects, width, label="Allocation Effect", alpha=0.7, color="blue")
        ax.bar(styles, selection_effects, width, bottom=allocation_effects, label="Selection Effect", alpha=0.7, color="green")
        ax.bar(
            styles,
            interaction_effects,
            width,
            bottom=[a + s for a, s in zip(allocation_effects, selection_effects)],
            label="Interaction Effect",
            alpha=0.7,
            color="orange",
        )

        # Add horizontal line at y=0
        ax.axhline(y=0, color="gray", linestyle="-", alpha=0.5)

        # Format the plot
        ax.set_ylabel("Contribution to Active Return (%)")
        ax.set_title("Style Attribution Analysis")
        ax.grid(True, axis="y", alpha=0.3)
        ax.legend()

        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45, ha="right")

    else:  # Default case
        ax.text(0.5, 0.5, f"Unknown chart type: {chart_type}", ha="center", va="center", fontsize=14)
        ax.set_title("Attribution Analysis")
        ax.axis("off")

    # Adjust layout
    plt.tight_layout()

    # Convert plot to base64-encoded image
    buffer = io.BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
    plt.close()

    return image_base64