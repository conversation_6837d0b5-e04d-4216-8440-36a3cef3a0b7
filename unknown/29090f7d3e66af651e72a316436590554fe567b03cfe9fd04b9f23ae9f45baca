// 全局变量
let currentTicker = '';
let stockChart = null;
let isLoggedIn = false;
let currentUser = null;
let accessToken = null;

// API基础URL和版本在config.js中定义

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化事件监听器
    initEventListeners();

    // 检查本地存储中是否有登录信息
    checkLoginStatus();
});

// 初始化事件监听器
function initEventListeners() {
    // 搜索按钮点击事件
    document.getElementById('searchBtn').addEventListener('click', function() {
        const ticker = document.getElementById('searchTicker').value.trim().toUpperCase();
        if (ticker) {
            searchStock(ticker);
        }
    });

    // 搜索输入框回车事件
    document.getElementById('searchTicker').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const ticker = this.value.trim().toUpperCase();
            if (ticker) {
                searchStock(ticker);
            }
        }
    });

    // 时间周期按钮点击事件
    document.querySelectorAll('[data-period]').forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有按钮的active类
            document.querySelectorAll('[data-period]').forEach(btn => {
                btn.classList.remove('active');
            });

            // 添加当前按钮的active类
            this.classList.add('active');

            // 获取选中的时间周期
            const period = this.getAttribute('data-period');

            // 如果有当前股票代码，则更新图表
            if (currentTicker) {
                updateStockChart(currentTicker, period);
            }
        });
    });

    // 新闻主题下拉菜单点击事件
    document.querySelectorAll('[data-topic]').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            // 获取选中的主题
            const topic = this.getAttribute('data-topic');
            console.log('选择了主题:', topic);

            // 更新下拉菜单按钮文本
            const dropdownBtn = document.getElementById('newsTopicDropdown');
            if (dropdownBtn) {
                dropdownBtn.textContent = topic || '全部主题';
            }

            // 如果有当前股票代码，则更新新闻
            if (currentTicker) {
                fetchNews(currentTicker, topic);
            }
        });
    });

    // 登录按钮点击事件
    document.getElementById('loginBtn').addEventListener('click', function() {
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    });

    // 注册按钮点击事件
    document.getElementById('registerBtn').addEventListener('click', function() {
        const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
        registerModal.show();
    });

    // 生成用户名按钮点击事件
    document.getElementById('generateUsernameBtn').addEventListener('click', function() {
        generateRandomUsername();
    });

    // 登录表单提交事件
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;

        login(username, password);
    });

    // 忘记密码链接点击事件
    document.getElementById('forgotPasswordLink').addEventListener('click', function(e) {
        e.preventDefault();

        // 关闭登录模态框
        const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
        loginModal.hide();

        // 打开重置密码模态框
        const resetPasswordModal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
        resetPasswordModal.show();

        // 重置表单状态
        document.getElementById('resetPasswordStep1').style.display = 'block';
        document.getElementById('resetPasswordStep2').style.display = 'none';
        document.getElementById('resetPasswordError').style.display = 'none';
        document.getElementById('resetPasswordEmail').value = '';
    });

    // 发送重置链接按钮点击事件
    document.getElementById('sendResetLinkBtn').addEventListener('click', function() {
        const emailOrUsername = document.getElementById('resetPasswordEmail').value.trim();

        if (!emailOrUsername) {
            const resetPasswordError = document.getElementById('resetPasswordError');
            resetPasswordError.style.display = 'block';
            document.getElementById('resetPasswordErrorMessage').textContent = '请输入用户名或邮箱';
            return;
        }

        sendPasswordResetLink(emailOrUsername);
    });

    // 重新发送重置链接按钮点击事件
    document.getElementById('resendResetLinkBtn').addEventListener('click', function() {
        document.getElementById('resetPasswordStep1').style.display = 'block';
        document.getElementById('resetPasswordStep2').style.display = 'none';
    });

    // 注册表单提交事件
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('registerUsername').value;
        const email = document.getElementById('registerEmail').value;
        const fullName = document.getElementById('registerFullName').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('registerConfirmPassword').value;

        if (password !== confirmPassword) {
            alert('两次输入的密码不一致');
            return;
        }

        register(username, email, fullName, password);
    });
}

// 检查登录状态
function checkLoginStatus() {
    const token = localStorage.getItem('accessToken');
    const user = localStorage.getItem('user');

    if (token && user) {
        accessToken = token;
        currentUser = JSON.parse(user);
        isLoggedIn = true;

        // 更新UI显示登录状态
        updateLoginUI();
    }
}

// 更新登录UI
function updateLoginUI() {
    const loginBtn = document.getElementById('loginBtn');
    const registerBtn = document.getElementById('registerBtn');

    if (isLoggedIn) {
        // 创建用户下拉菜单
        const userDropdownContainer = document.createElement('div');
        userDropdownContainer.className = 'dropdown';

        // 创建下拉菜单按钮
        userDropdownContainer.innerHTML = `
            <div class="d-flex align-items-center user-dropdown" data-bs-toggle="dropdown" aria-expanded="false" role="button">
                <div class="user-avatar">${currentUser.username.charAt(0).toUpperCase()}</div>
                <span class="ms-2">${currentUser.username}</span>
                <i class="bi bi-chevron-down ms-1"></i>
            </div>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i>个人资料</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>账号设置</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
            </ul>
        `;

        // 替换登录和注册按钮
        loginBtn.parentNode.replaceChild(userDropdownContainer, loginBtn);
        registerBtn.style.display = 'none';

        // 添加退出登录按钮事件
        setTimeout(() => {
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('确定要退出登录吗？')) {
                        logout();
                    }
                });
            }
        }, 100);
    } else {
        // 恢复登录和注册按钮
        loginBtn.style.display = '';
        registerBtn.style.display = '';
    }
}

// 搜索股票
function searchStock(ticker) {
    console.log('搜索股票:', ticker); // 添加日志，帮助调试
    currentTicker = ticker;

    // 显示股票数据和新闻区域
    const stockDataSection = document.getElementById('stockDataSection');
    const newsSection = document.getElementById('newsSection');

    if (stockDataSection) {
        stockDataSection.style.display = 'block';
    } else {
        console.error('找不到股票数据区域元素');
    }

    if (newsSection) {
        newsSection.style.display = 'block';
    } else {
        console.error('找不到新闻区域元素');
    }

    // 更新股票标题
    const stockTitle = document.getElementById('stockTitle');
    if (stockTitle) {
        stockTitle.textContent = `${ticker} 股票数据`;
    }

    // 获取当前选中的时间周期
    const activePeriodBtn = document.querySelector('[data-period].active');
    const period = activePeriodBtn ? activePeriodBtn.getAttribute('data-period') : '1d';

    // 确保元素已经存在后再更新图表
    setTimeout(() => {
        // 更新股票图表
        updateStockChart(ticker, period);

        // 获取股票详情
        fetchStockDetails(ticker);
    }, 100); // 小延时确保 DOM 已更新

    // 获取新闻
    const topicElement = document.getElementById('newsTopicDropdown');
    const topic = topicElement ? topicElement.textContent.trim() : '';
    console.log('选中的主题:', topic);
    fetchNews(ticker, topic !== '全部主题' ? topic : '');

    // 自动加载高级图表
    if (typeof loadAdvancedCharts === 'function') {
        loadAdvancedCharts(ticker);
    }
}

// 将searchStock函数添加到全局作用域，使其在其他JS文件中可用
window.searchStock = searchStock;

// 更新股票图表
async function updateStockChart(ticker, period) {
    // 获取图表元素
    const stockChartElement = document.getElementById('stockChart');

    // 如果图表元素不存在，可能是因为股票数据区域尚未显示
    if (!stockChartElement) {
        console.warn('图表元素不存在，先显示股票数据区域');
        // 显示股票数据区域
        document.getElementById('stockDataSection').style.display = 'block';
        document.getElementById('newsSection').style.display = 'block';

        // 重新获取图表元素
        const stockChartContainer = document.querySelector('#stockChart');
        if (!stockChartContainer) {
            console.error('无法找到图表容器元素');
            return; // 如果仍然找不到，则退出函数
        }
    }

    // 重新获取图表元素（可能已经显示了）
    const chartContainer = document.getElementById('stockChart')?.parentNode || document.querySelector('.card-body');
    if (!chartContainer) {
        console.error('无法找到图表容器');
        return;
    }

    // 显示加载动画
    chartContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 将period转换为duration参数
        let duration = '30';
        switch(period) {
            case '1d': duration = '1'; break;
            case '1w': duration = '7'; break;
            case '1m': duration = '30'; break;
            case '3m': duration = '90'; break;
            case '1y': duration = '365'; break;
        }

        // 调用真实API
        const response = await fetchWithAuth(`${API_BASE_URL}/stock/${ticker}/chart?price_type=收盘&duration=${duration}`);

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const chartData = await response.json();

        // 创建新的canvas元素
        chartContainer.innerHTML = '<canvas id="stockChart" height="300"></canvas>';

        // 获取新的canvas上下文
        const ctx = document.getElementById('stockChart').getContext('2d');

        // 准备数据
        const labels = chartData.dates || [];
        const prices = chartData.values || [];

        // 销毁旧图表
        if (stockChart) {
            stockChart.destroy();
        }

        // 创建新图表
        stockChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: `${ticker} 价格`,
                    data: prices,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: '#0d6efd',
                    pointHoverBorderColor: '#fff',
                    pointHoverBorderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
    } catch (error) {
        console.error('获取股票图表数据失败:', error);
        chartContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">获取数据失败: ${error.message}</p></div>`;

        // 如果API调用失败，使用模拟数据作为备选
        setTimeout(() => {
            // 创建新的canvas元素
            chartContainer.innerHTML = '<canvas id="stockChart" height="300"></canvas>';

            // 获取新的canvas上下文
            const ctx = document.getElementById('stockChart').getContext('2d');

            // 模拟数据
            const data = generateMockStockData(period);

            // 销毁旧图表
            if (stockChart) {
                stockChart.destroy();
            }

            // 创建新图表
            stockChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: `${ticker} 价格 (模拟数据)`,
                        data: data.prices,
                        borderColor: '#0d6efd',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHoverBackgroundColor: '#0d6efd',
                        pointHoverBorderColor: '#fff',
                        pointHoverBorderWidth: 2,
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        }, 1000);
    }
}

// 获取股票详情
async function fetchStockDetails(ticker) {
    // 显示加载动画
    const detailsContainer = document.getElementById('stockDetails');
    detailsContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 调用真实API获取股票价格统计数据
        const priceResponse = await fetchWithAuth(`${API_BASE_URL}/stock/${ticker}/price-stats?operation=最新&price_type=收盘&duration=1`);

        if (!priceResponse.ok) {
            throw new Error(`API错误: ${priceResponse.status}`);
        }

        const priceData = await priceResponse.json();

        // 调用其他API获取更多详情
        const highResponse = await fetchWithAuth(`${API_BASE_URL}/stock/${ticker}/price-stats?operation=最高&price_type=高&duration=1`);

        const lowResponse = await fetchWithAuth(`${API_BASE_URL}/stock/${ticker}/price-stats?operation=最低&price_type=低&duration=1`);

        const openResponse = await fetchWithAuth(`${API_BASE_URL}/stock/${ticker}/price-stats?operation=最新&price_type=开盘&duration=1`);

        const highData = await highResponse.json();
        const lowData = await lowResponse.json();
        const openData = await openResponse.json();

        // 整合数据
        const details = {
            price: priceData.value || 0,
            change: priceData.change || 0,
            changePercent: priceData.change_percent || 0,
            open: openData.value || 0,
            high: highData.value || 0,
            low: lowData.value || 0,
            volume: priceData.volume || 0,
            marketCap: priceData.market_cap || 0,
            pe: priceData.pe_ratio || 0,
            dividend: priceData.dividend_yield || 0
        };

        // 更新UI
        detailsContainer.innerHTML = `
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">当前价格</div>
                    <div class="stock-detail-value">$${details.price.toFixed(2)}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">涨跌幅</div>
                    <div class="stock-detail-value ${details.change > 0 ? 'positive' : 'negative'}">
                        ${details.change > 0 ? '+' : ''}${details.change.toFixed(2)} (${details.changePercent.toFixed(2)}%)
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">开盘价</div>
                    <div class="stock-detail-value">$${details.open.toFixed(2)}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">最高价</div>
                    <div class="stock-detail-value">$${details.high.toFixed(2)}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">最低价</div>
                    <div class="stock-detail-value">$${details.low.toFixed(2)}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">成交量</div>
                    <div class="stock-detail-value">${details.volume.toLocaleString()}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">市值</div>
                    <div class="stock-detail-value">$${(details.marketCap / 1000000000).toFixed(2)}B</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">市盈率</div>
                    <div class="stock-detail-value">${details.pe.toFixed(2)}</div>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('获取股票详情失败:', error);
        detailsContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">获取数据失败: ${error.message}</p></div>`;

        // 如果API调用失败，使用模拟数据作为备选
        setTimeout(() => {
            // 模拟数据
            const details = {
                price: (Math.random() * 1000 + 100).toFixed(2),
                change: (Math.random() * 20 - 10).toFixed(2),
                changePercent: (Math.random() * 5 - 2.5).toFixed(2),
                open: (Math.random() * 1000 + 100).toFixed(2),
                high: (Math.random() * 1000 + 150).toFixed(2),
                low: (Math.random() * 1000 + 50).toFixed(2),
                volume: Math.floor(Math.random() * 10000000),
                marketCap: Math.floor(Math.random() * 1000000000000),
                pe: (Math.random() * 30 + 5).toFixed(2),
                dividend: (Math.random() * 5).toFixed(2)
            };

            // 更新UI
            detailsContainer.innerHTML = `
                <div class="alert alert-warning mb-3">使用模拟数据显示</div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">当前价格</div>
                        <div class="stock-detail-value">$${details.price}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">涨跌幅</div>
                        <div class="stock-detail-value ${details.change > 0 ? 'positive' : 'negative'}">
                            ${details.change > 0 ? '+' : ''}${details.change} (${details.changePercent}%)
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">开盘价</div>
                        <div class="stock-detail-value">$${details.open}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">最高价</div>
                        <div class="stock-detail-value">$${details.high}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">最低价</div>
                        <div class="stock-detail-value">$${details.low}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">成交量</div>
                        <div class="stock-detail-value">${details.volume.toLocaleString()}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">市值</div>
                        <div class="stock-detail-value">$${(details.marketCap / 1000000000).toFixed(2)}B</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">市盈率</div>
                        <div class="stock-detail-value">${details.pe}</div>
                    </div>
                </div>
            `;
        }, 1000);
    }
}

// 获取新闻
async function fetchNews(ticker, topic = '') {
    // 显示加载动画
    const newsContainer = document.getElementById('newsList');
    newsContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    // 构建API URL
    let url = `${API_BASE_URL}/${API_VERSION}/news/${ticker}`;
    if (topic) {
        url += `?topic=${encodeURIComponent(topic)}`;
    }
    console.log('请求新闻的URL:', url);

    // 使用模拟数据的函数
    const showMockNews = () => {
        // 模拟数据
        const news = generateMockNews(ticker, topic);

        // 更新UI
        if (news.length > 0) {
            newsContainer.innerHTML = '<div class="alert alert-warning mb-3">使用模拟数据显示</div>';
            news.forEach(item => {
                const newsItem = document.createElement('div');
                newsItem.className = 'news-item';
                newsItem.innerHTML = `
                    <div class="news-date">${item.date}</div>
                    <h5 class="news-title">${item.title}</h5>
                    <p>${item.summary}</p>
                    <div class="news-source">来源: ${item.source}</div>
                `;
                newsContainer.appendChild(newsItem);
            });
        } else {
            newsContainer.innerHTML = '<div class="text-center py-5"><p class="text-muted">没有找到相关新闻</p></div>';
        }
    };

    try {
        // 调用真实API
        const response = await fetchWithAuth(url);

        // 如果是500错误，直接使用模拟数据
        if (response.status === 500) {
            console.warn('服务器内部错误，使用模拟数据');
            showMockNews();
            return;
        }

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const data = await response.json();

        // 检查是否是模拟数据
        if (data && data.is_mock) {
            console.log('后端返回了模拟数据');
            // 显示模拟数据提示
            newsContainer.innerHTML = '<div class="alert alert-warning mb-3">使用模拟数据显示</div>';
        }

        // 更新标题，如果有证券名称
        if (data && data.name) {
            const newsTitle = document.getElementById('newsTitle');
            if (newsTitle) {
                newsTitle.textContent = `${data.name} 相关新闻`;
            }
        }

        // 处理API响应
        if (data && data.result) {
            // 将API响应转换为新闻项目列表
            // 这里需要根据API的实际响应格式进行调整
            const newsText = data.result;

            // 将文本分割成段落，每个段落作为一条新闻
            const paragraphs = newsText.split('\n\n').filter(p => p.trim().length > 0);

            if (paragraphs.length > 0) {
                // 如果是模拟数据，保留警告提示，否则清空容器
                if (!data.is_mock) {
                    newsContainer.innerHTML = '';
                }

                paragraphs.forEach((paragraph, index) => {
                    // 尝试提取标题和内容
                    const lines = paragraph.split('\n');
                    const title = lines[0] || `${ticker}相关新闻 ${index + 1}`;
                    const content = lines.slice(1).join('\n') || paragraph;

                    // 尝试提取日期，如果标题以日期格式开头 [YYYY-MM-DD]
                    let date = new Date().toISOString().split('T')[0];
                    let displayTitle = title;
                    const dateMatch = title.match(/\[(\d{4}-\d{2}-\d{2})\]\s*(.+)/);
                    if (dateMatch) {
                        date = dateMatch[1];
                        displayTitle = dateMatch[2];
                    }

                    const newsItem = document.createElement('div');
                    newsItem.className = 'news-item';
                    newsItem.innerHTML = `
                        <div class="news-date">${date}</div>
                        <h5 class="news-title">${displayTitle}</h5>
                        <p>${content}</p>
                        <div class="news-source">来源: 智能体投顾助手</div>
                    `;
                    newsContainer.appendChild(newsItem);
                });
            } else {
                newsContainer.innerHTML = '<div class="text-center py-5"><p class="text-muted">没有找到相关新闻</p></div>';
            }
        } else {
            newsContainer.innerHTML = '<div class="text-center py-5"><p class="text-muted">没有找到相关新闻</p></div>';
        }
    } catch (error) {
        console.error('获取新闻失败:', error);

        // 显示错误信息，但不会阻止我们使用模拟数据
        newsContainer.innerHTML = `<div class="alert alert-danger mb-3">获取新闻失败: ${error.message}</div>`;

        // 使用模拟数据作为备选
        setTimeout(() => {
            showMockNews();
        }, 500);
    }
}

// 登录
async function login(username, password) {
    // 显示加载状态
    const submitBtn = document.querySelector('#loginForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 登录中...';
    submitBtn.disabled = true;

    // 隐藏错误信息
    const loginError = document.getElementById('loginError');
    loginError.style.display = 'none';

    try {
        // 调用真实API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                'username': username,
                'password': password
            })
        });

        // 尝试解析响应数据，即使响应不成功
        let responseData;
        try {
            responseData = await response.json();
        } catch (e) {
            responseData = { detail: `登录失败: ${response.status}` };
        }

        if (!response.ok) {
            let errorMessage = responseData.detail || `登录失败: ${response.status}`;

            // 处理各种特定错误
            if (response.status === 404) {
                // 用户未注册
                if (confirm('该用户尚未注册，是否立即注册？')) {
                    // 关闭登录模态框
                    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
                    loginModal.hide();

                    // 打开注册模态框
                    const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
                    registerModal.show();

                    // 预填写用户名
                    document.getElementById('registerUsername').value = username;
                    return;
                }
            } else if (response.status === 401) {
                errorMessage = '用户名或密码错误';
            } else if (response.status === 403) {
                errorMessage = '账户已被禁用';
            }

            // 显示错误信息
            loginError.style.display = 'block';
            document.getElementById('loginErrorMessage').textContent = errorMessage;

            // 恢复按钮状态
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
            return;
        }

        if (responseData && responseData.access_token) {
            // 尝试获取用户信息
            let userData = null;
            try {
                const userResponse = await fetchWithAuth(`${API_BASE_URL}/${API_VERSION}/user/me`);

                if (userResponse.ok) {
                    userData = await userResponse.json();
                } else {
                    console.warn(`获取用户信息失败: ${userResponse.status}`);
                    // 即使获取用户信息失败，也不中断登录过程
                    // 使用登录响应中的用户信息
                }
            } catch (error) {
                console.error('获取用户信息时发生错误:', error);
                // 即使获取用户信息失败，也不中断登录过程
            }

            // 从响应中提取用户数据
            const userInfo = (userData && userData.data) ? userData.data : {
                username: responseData.username || username,
                email: responseData.email || '',
                full_name: responseData.full_name || '',
                is_admin: responseData.is_admin || false
            };

            // 保存登录信息
            accessToken = responseData.access_token;
            currentUser = userInfo;
            isLoggedIn = true;

            // 保存到本地存储
            localStorage.setItem('accessToken', accessToken);
            localStorage.setItem('user', JSON.stringify(currentUser));

            // 更新UI
            updateLoginUI();

            // 关闭模态框
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            loginModal.hide();

            // 重置表单
            document.getElementById('loginForm').reset();

            // 显示成功消息
            alert('登录成功！');
        } else {
            loginError.style.display = 'block';
            document.getElementById('loginErrorMessage').textContent = '登录响应中缺少访问令牌';
        }
    } catch (error) {
        console.error('登录失败:', error);
        loginError.style.display = 'block';
        document.getElementById('loginErrorMessage').textContent = error.message;
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 注册
async function register(username, email, fullName, password) {
    // 显示加载状态
    const submitBtn = document.querySelector('#registerForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 注册中...';
    submitBtn.disabled = true;

    // 隐藏错误信息
    const registerError = document.getElementById('registerError');
    registerError.style.display = 'none';

    try {
        // 调用真实API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                email: email,
                full_name: fullName,
                password: password  // 密码将在服务器端进行哈希
            })
        });

        // 尝试解析响应数据，即使响应不成功
        let responseData;
        try {
            responseData = await response.json();
        } catch (e) {
            // 如果无法解析JSON，使用默认错误消息
            responseData = { detail: `注册失败: ${response.status}` };
        }

        if (!response.ok) {
            // 如果响应不成功，显示错误信息
            let errorMessage = `注册失败: ${response.status}`;

            // 如果响应中包含详细错误信息，使用详细信息
            if (responseData && responseData.detail) {
                errorMessage = responseData.detail;
            }

            // 处理各种特定错误
            if (errorMessage.includes('用户名已存在')) {
                // 如果是用户名已存在错误，提供生成新用户名的选项
                if (confirm('用户名已存在，是否生成新的用户名？')) {
                    generateRandomUsername();
                    // 显示错误信息，但不抛出异常，允许用户重试
                    registerError.style.display = 'block';
                    document.getElementById('registerErrorMessage').textContent = '已生成新用户名，请重新提交';
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                    return;
                }
            } else if (errorMessage.includes('密码长度不能少于')) {
                // 如果是密码长度错误，直接在表单中显示错误
                registerError.style.display = 'block';
                document.getElementById('registerErrorMessage').textContent = errorMessage;
                // 聚焦到密码输入框
                document.getElementById('registerPassword').focus();
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
                return;
            }

            throw new Error(errorMessage);
        }

        // 关闭模态框
        const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
        registerModal.hide();

        // 重置表单
        document.getElementById('registerForm').reset();

        // 显示成功消息
        alert('注册成功！请登录您的账号。');

        // 打开登录模态框
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    } catch (error) {
        console.error('注册失败:', error);

        // 显示错误信息
        registerError.style.display = 'block';
        document.getElementById('registerErrorMessage').textContent = error.message;
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 生成随机用户名
function generateRandomUsername() {
    // 生成一个随机数字字符串
    const randomDigits = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

    // 生成一个随机字母字符串
    const randomChars = Array(4).fill(0).map(() =>
        String.fromCharCode(97 + Math.floor(Math.random() * 26))
    ).join('');

    // 组合成带有指定前缀的用户名
    const username = `HUA-${randomChars}${randomDigits}`;

    // 设置到输入框
    document.getElementById('registerUsername').value = username;

    return username;
}

// 退出登录
function logout() {
    // 清除登录信息
    accessToken = null;
    currentUser = null;
    isLoggedIn = false;

    // 清除本地存储
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');

    // 刷新页面
    location.reload();
}

// 发送密码重置链接
async function sendPasswordResetLink(emailOrUsername) {
    // 显示加载状态
    const submitBtn = document.getElementById('sendResetLinkBtn');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';
    submitBtn.disabled = true;

    // 隐藏错误信息
    const resetPasswordError = document.getElementById('resetPasswordError');
    resetPasswordError.style.display = 'none';

    try {
        // 模拟发送重置链接
        // 在实际应用中，这里应该调用后端 API
        await new Promise(resolve => setTimeout(resolve, 1500)); // 模拟网络请求延迟

        // 显示成功消息
        document.getElementById('resetPasswordStep1').style.display = 'none';
        document.getElementById('resetPasswordStep2').style.display = 'block';
    } catch (error) {
        console.error('发送重置链接失败:', error);
        resetPasswordError.style.display = 'block';
        document.getElementById('resetPasswordErrorMessage').textContent = error.message || '发送重置链接失败，请稍后重试';
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 生成模拟股票数据
function generateMockStockData(period) {
    const labels = [];
    const prices = [];
    let dataPoints = 0;
    let startPrice = Math.random() * 500 + 100;

    switch (period) {
        case '1d':
            dataPoints = 24;
            for (let i = 0; i < dataPoints; i++) {
                const hour = i % 12 + 1;
                const ampm = i < 12 ? 'AM' : 'PM';
                labels.push(`${hour}:00 ${ampm}`);
                startPrice += (Math.random() - 0.5) * 5;
                prices.push(startPrice);
            }
            break;
        case '1w':
            dataPoints = 7;
            const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            for (let i = 0; i < dataPoints; i++) {
                labels.push(days[i]);
                startPrice += (Math.random() - 0.5) * 20;
                prices.push(startPrice);
            }
            break;
        case '1m':
            dataPoints = 30;
            for (let i = 0; i < dataPoints; i++) {
                labels.push(`${i + 1}日`);
                startPrice += (Math.random() - 0.5) * 30;
                prices.push(startPrice);
            }
            break;
        case '3m':
            dataPoints = 12;
            for (let i = 0; i < dataPoints; i++) {
                labels.push(`第${i + 1}周`);
                startPrice += (Math.random() - 0.5) * 50;
                prices.push(startPrice);
            }
            break;
        case '1y':
            dataPoints = 12;
            const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
            for (let i = 0; i < dataPoints; i++) {
                labels.push(months[i]);
                startPrice += (Math.random() - 0.5) * 100;
                prices.push(startPrice);
            }
            break;
    }

    return { labels, prices };
}

// 生成模拟新闻数据
function generateMockNews(ticker, topic = '') {
    const news = [];
    const sources = ['财经网', '证券时报', '华尔街日报', '彭博社', '路透社'];
    const today = new Date();

    // 生成5-10条新闻
    const count = Math.floor(Math.random() * 6) + 5;

    for (let i = 0; i < count; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);

        let title = '';
        let summary = '';

        if (topic) {
            // 根据主题生成新闻
            switch (topic) {
                case '财报':
                    title = `${ticker} 公布${Math.floor(Math.random() * 4) + 1}季度财报，${Math.random() > 0.5 ? '超出' : '未达'}市场预期`;
                    summary = `${ticker} 公司${Math.floor(Math.random() * 4) + 1}季度营收达到${(Math.random() * 100).toFixed(2)}亿美元，${Math.random() > 0.5 ? '同比增长' : '同比下降'}${(Math.random() * 20).toFixed(1)}%。每股收益${(Math.random() * 5).toFixed(2)}美元，${Math.random() > 0.5 ? '超出' : '低于'}分析师预期。`;
                    break;
                case '产品':
                    title = `${ticker} 宣布推出新${Math.random() > 0.5 ? '产品' : '服务'}，瞄准${Math.random() > 0.5 ? '消费' : '企业'}市场`;
                    summary = `${ticker} 公司今日宣布推出全新${Math.random() > 0.5 ? '产品' : '服务'}，旨在${Math.random() > 0.5 ? '提升用户体验' : '扩大市场份额'}。分析师普遍${Math.random() > 0.5 ? '看好' : '持谨慎态度'}，预计将对公司业绩产生${Math.random() > 0.5 ? '积极' : '一定'}影响。`;
                    break;
                case '市场':
                    title = `分析师${Math.random() > 0.5 ? '上调' : '下调'} ${ticker} 目标价，${Math.random() > 0.5 ? '看好' : '担忧'}其市场前景`;
                    summary = `多家券商${Math.random() > 0.5 ? '上调' : '下调'} ${ticker} 目标价，${Math.random() > 0.5 ? '看好' : '担忧'}其在${Math.random() > 0.5 ? '国内' : '国际'}市场的发展前景。目前市场普遍预期该股${Math.random() > 0.5 ? '有上涨空间' : '面临调整压力'}。`;
                    break;
                default:
                    title = `${ticker} ${Math.random() > 0.5 ? '股价' : '市值'}${Math.random() > 0.5 ? '上涨' : '下跌'}，${Math.random() > 0.5 ? '投资者' : '分析师'}${Math.random() > 0.5 ? '看好' : '担忧'}前景`;
                    summary = `受${Math.random() > 0.5 ? '市场情绪' : '行业动态'}影响，${ticker} 今日${Math.random() > 0.5 ? '股价' : '市值'}${Math.random() > 0.5 ? '上涨' : '下跌'}${(Math.random() * 10).toFixed(2)}%。分析师认为，这主要是由于${Math.random() > 0.5 ? '公司基本面' : '外部环境'}变化所致。`;
            }
        } else {
            // 随机生成新闻
            const topics = ['财报', '产品', '市场', '人事', '战略'];
            const randomTopic = topics[Math.floor(Math.random() * topics.length)];

            switch (randomTopic) {
                case '财报':
                    title = `${ticker} 公布${Math.floor(Math.random() * 4) + 1}季度财报，${Math.random() > 0.5 ? '超出' : '未达'}市场预期`;
                    summary = `${ticker} 公司${Math.floor(Math.random() * 4) + 1}季度营收达到${(Math.random() * 100).toFixed(2)}亿美元，${Math.random() > 0.5 ? '同比增长' : '同比下降'}${(Math.random() * 20).toFixed(1)}%。每股收益${(Math.random() * 5).toFixed(2)}美元，${Math.random() > 0.5 ? '超出' : '低于'}分析师预期。`;
                    break;
                case '产品':
                    title = `${ticker} 宣布推出新${Math.random() > 0.5 ? '产品' : '服务'}，瞄准${Math.random() > 0.5 ? '消费' : '企业'}市场`;
                    summary = `${ticker} 公司今日宣布推出全新${Math.random() > 0.5 ? '产品' : '服务'}，旨在${Math.random() > 0.5 ? '提升用户体验' : '扩大市场份额'}。分析师普遍${Math.random() > 0.5 ? '看好' : '持谨慎态度'}，预计将对公司业绩产生${Math.random() > 0.5 ? '积极' : '一定'}影响。`;
                    break;
                case '市场':
                    title = `分析师${Math.random() > 0.5 ? '上调' : '下调'} ${ticker} 目标价，${Math.random() > 0.5 ? '看好' : '担忧'}其市场前景`;
                    summary = `多家券商${Math.random() > 0.5 ? '上调' : '下调'} ${ticker} 目标价，${Math.random() > 0.5 ? '看好' : '担忧'}其在${Math.random() > 0.5 ? '国内' : '国际'}市场的发展前景。目前市场普遍预期该股${Math.random() > 0.5 ? '有上涨空间' : '面临调整压力'}。`;
                    break;
                case '人事':
                    title = `${ticker} 宣布${Math.random() > 0.5 ? 'CEO' : 'CFO'}${Math.random() > 0.5 ? '离职' : '任命'}，${Math.random() > 0.5 ? '市场反应积极' : '引发投资者担忧'}`;
                    summary = `${ticker} 公司今日宣布${Math.random() > 0.5 ? 'CEO' : 'CFO'}${Math.random() > 0.5 ? '离职' : '任命'}。${Math.random() > 0.5 ? '分析师认为这将为公司带来新的发展机遇' : '市场对此反应谨慎，担忧可能影响公司战略连续性'}。`;
                    break;
                case '战略':
                    title = `${ticker} 宣布${Math.random() > 0.5 ? '收购' : '战略合作'}计划，${Math.random() > 0.5 ? '加码' : '布局'} ${Math.random() > 0.5 ? '人工智能' : '云计算'}领域`;
                    summary = `${ticker} 公司宣布将${Math.random() > 0.5 ? '收购' : '与'}${Math.random() > 0.5 ? '一家初创公司' : '行业领导者'}${Math.random() > 0.5 ? '' : '达成战略合作'}，进一步${Math.random() > 0.5 ? '加码' : '布局'} ${Math.random() > 0.5 ? '人工智能' : '云计算'}领域。此举被视为公司${Math.random() > 0.5 ? '转型' : '扩张'}战略的重要一步。`;
                    break;
            }
        }

        news.push({
            title: title,
            summary: summary,
            date: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`,
            source: sources[Math.floor(Math.random() * sources.length)]
        });
    }

    return news;
}

