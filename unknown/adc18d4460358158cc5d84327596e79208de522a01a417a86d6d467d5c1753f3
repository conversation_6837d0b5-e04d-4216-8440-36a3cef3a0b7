// 全局变量
let currentTicker = '';
let stockChart = null;
let isLoggedIn = false;
let currentUser = null;
let accessToken = null;

// API基础URL
const API_BASE_URL = 'http://localhost:8080/api';
const API_VERSION = 'v1';

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化事件监听器
    initEventListeners();

    // 检查本地存储中是否有登录信息
    checkLoginStatus();

    // 设置默认股票代码
    document.getElementById('searchTicker').value = 'IF9999.SF';

    // 自动搜索默认股票
    searchStock('IF9999.SF');
});

// 初始化事件监听器
function initEventListeners() {
    // 搜索按钮点击事件
    document.getElementById('searchBtn').addEventListener('click', function() {
        const ticker = document.getElementById('searchTicker').value.trim().toUpperCase();
        if (ticker) {
            searchStock(ticker);
        }
    });

    // 搜索输入框回车事件
    document.getElementById('searchTicker').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const ticker = this.value.trim().toUpperCase();
            if (ticker) {
                searchStock(ticker);
            }
        }
    });

    // 注意: runModelBtn的点击事件已经移到model-prediction.js中处理

    // 图表/表格视图切换按钮点击事件
    document.querySelectorAll('[data-view]').forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有按钮的active类
            document.querySelectorAll('[data-view]').forEach(btn => {
                btn.classList.remove('active');
            });

            // 添加当前按钮的active类
            this.classList.add('active');

            // 获取选中的视图类型
            const viewType = this.getAttribute('data-view');

            // 切换视图
            if (viewType === 'chart') {
                document.getElementById('predictionChartView').style.display = 'block';
                document.getElementById('predictionTableView').style.display = 'none';
            } else if (viewType === 'table') {
                document.getElementById('predictionChartView').style.display = 'none';
                document.getElementById('predictionTableView').style.display = 'block';
            }
        });
    });

    // 登录按钮点击事件
    document.getElementById('loginBtn').addEventListener('click', function() {
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    });

    // 注册按钮点击事件
    document.getElementById('registerBtn').addEventListener('click', function() {
        const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
        registerModal.show();
    });

    // 登录表单提交事件
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;

        login(username, password);
    });

    // 注册表单提交事件
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('registerUsername').value;
        const email = document.getElementById('registerEmail').value;
        const fullName = document.getElementById('registerFullName').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('registerConfirmPassword').value;

        if (password !== confirmPassword) {
            alert('两次输入的密码不一致');
            return;
        }

        register(username, email, fullName, password);
    });
}

// 检查登录状态
function checkLoginStatus() {
    const token = localStorage.getItem('accessToken');
    const user = localStorage.getItem('user');

    if (token && user) {
        accessToken = token;
        currentUser = JSON.parse(user);
        isLoggedIn = true;

        // 更新UI显示登录状态
        updateLoginUI();
    }
}

// 更新登录UI
function updateLoginUI() {
    const loginBtn = document.getElementById('loginBtn');
    const registerBtn = document.getElementById('registerBtn');

    if (isLoggedIn) {
        // 创建用户下拉菜单
        const userDropdownContainer = document.createElement('div');
        userDropdownContainer.className = 'dropdown';

        // 创建下拉菜单按钮
        userDropdownContainer.innerHTML = `
            <div class="d-flex align-items-center user-dropdown" data-bs-toggle="dropdown" aria-expanded="false" role="button">
                <div class="user-avatar">${currentUser.username.charAt(0).toUpperCase()}</div>
                <span class="ms-2">${currentUser.username}</span>
                <i class="bi bi-chevron-down ms-1"></i>
            </div>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i>个人资料</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>账号设置</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
            </ul>
        `;

        // 替换登录和注册按钮
        loginBtn.parentNode.replaceChild(userDropdownContainer, loginBtn);
        registerBtn.style.display = 'none';

        // 添加退出登录按钮事件
        setTimeout(() => {
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('确定要退出登录吗？')) {
                        logout();
                    }
                });
            }
        }, 100);
    } else {
        // 恢复登录和注册按钮
        loginBtn.style.display = '';
        registerBtn.style.display = '';
    }
}

// 搜索股票
function searchStock(ticker) {
    currentTicker = ticker;

    // 更新股票信息标题和代码
    document.getElementById('stockInfoTitle').textContent = `${ticker} 股票信息`;
    document.getElementById('stockCode').textContent = ticker;

    // 获取股票基本信息
    fetchStockInfo(ticker).then(stockInfo => {
        // 更新股票基本信息
        if (stockInfo) {
            // 更新股票信息
            document.getElementById('currentPrice').textContent = stockInfo.price ? stockInfo.price.toFixed(2) : '--';
            document.getElementById('priceChange').textContent = stockInfo.changePercent ? `${stockInfo.changePercent > 0 ? '+' : ''}${stockInfo.changePercent.toFixed(2)}%` : '--';
            document.getElementById('priceChange').className = stockInfo.changePercent >= 0 ? 'text-success' : 'text-danger';
            document.getElementById('volume').textContent = stockInfo.volume ? stockInfo.volume.toLocaleString() : '--';
            document.getElementById('updateTime').textContent = new Date().toLocaleString();
        }
    }).catch(error => {
        console.error('获取股票基本信息失败:', error);
    });

    // 获取历史K线数据并绘制图表
    fetchHistoricalCandlestickData(ticker).then(data => {
        // 更新股票信息
        if (data && data.length > 0) {
            // 计算ATR
            const atr = calculateATR(data);
            document.getElementById('atrValue').textContent = atr.toFixed(2);

            // 绘制历史K线图
            renderHistoricalChart(data);
        }
    });
}

// 获取股票基本信息
async function fetchStockInfo(ticker) {
    try {
        // 构建API URL - 使用datahub的stock_info接口
        const url = `${API_BASE_URL}/v1/datahub/stock_info?label=${ticker}`;

        console.log(`正在获取股票基本信息，URL: ${url}`);

        // 调用API
        const response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const data = await response.json();
        console.log(`成功获取股票基本信息:`, data);

        return data;
    } catch (error) {
        console.error(`获取${ticker}基本信息失败:`, error);
        console.log('尝试使用旧的API端点...');

        try {
            // 尝试使用旧的API端点作为备选
            const fallbackUrl = `${API_BASE_URL}/stock/${ticker}/info`;
            const fallbackResponse = await fetch(fallbackUrl, {
                headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
            });

            if (fallbackResponse.ok) {
                const fallbackData = await fallbackResponse.json();
                console.log(`成功使用旧API获取数据:`, fallbackData);
                return fallbackData;
            }
        } catch (fallbackError) {
            console.error('备选API也失败:', fallbackError);
        }

        // 如果两个API都失败，返回模拟数据
        return {
            name: ticker,
            price: 100 + Math.random() * 100,
            change: (Math.random() - 0.5) * 10,
            changePercent: (Math.random() - 0.5) * 5,
            open: 100 + Math.random() * 100,
            high: 100 + Math.random() * 110,
            low: 100 + Math.random() * 90,
            volume: Math.floor(Math.random() * 1000000)
        };
    }
}

// 初始化预测图表
function initPredictionChart() {
    const chartContainer = document.getElementById('predictionChart');
    chartContainer.innerHTML = '<div class="text-center py-5"><p class="text-muted">请先运行模型</p></div>';
}

// 运行预测模型
async function runPredictionModel() {
    if (!currentTicker) {
        alert('请先输入股票代码');
        return;
    }

    // 获取模型参数
    const modelType = document.getElementById('modelSelect').value;
    const predictionPeriod = document.getElementById('predictionPeriod').value;
    const historyPeriod = document.getElementById('historyPeriod').value;
    const confidenceInterval = document.getElementById('confidenceInterval').value;
    const includeVolume = document.getElementById('includeVolume').checked;
    const includeSentiment = document.getElementById('includeSentiment').checked;

    // 显示加载动画
    document.getElementById('predictionChart').innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">模型运行中...</p></div>';
    document.getElementById('predictionTableBody').innerHTML = '';
    document.getElementById('modelEvaluation').innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">评估中...</p></div>';
    document.getElementById('modelExplanation').innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">分析中...</p></div>';
    document.getElementById('featureImportance').innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">计算中...</p></div>';

    try {
        // 构建 API URL
        let url = `${API_BASE_URL}/prediction/${currentTicker}?model=${modelType}&prediction_period=${predictionPeriod}&history_period=${historyPeriod}&confidence_interval=${confidenceInterval}`;
        if (includeVolume) url += '&include_volume=true';
        if (includeSentiment) url += '&include_sentiment=true';

        // 调用真实API
        const response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const data = await response.json();

        // 处理API响应
        if (data) {
            // 更新预测图表
            updatePredictionChart(data);

            // 更新预测表格 - 使用model-prediction.js中的函数
            // 注意: 这里不需要调用updatePredictionTable，因为它已经在model-prediction.js中实现

            // 更新模型评估
            updateModelEvaluation(data);

            // 更新模型解释
            updateModelExplanation(data);

            // 更新特征重要性
            updateFeatureImportance(data);
        } else {
            throw new Error('预测数据为空');
        }
    } catch (error) {
        console.error('运行预测模型失败:', error);

        // 显示错误信息
        document.getElementById('predictionChart').innerHTML = `<div class="text-center py-5"><p class="text-danger">预测失败: ${error.message}</p></div>`;
        document.getElementById('modelEvaluation').innerHTML = `<div class="text-center py-5"><p class="text-danger">评估失败: ${error.message}</p></div>`;
        document.getElementById('modelExplanation').innerHTML = `<div class="text-center py-5"><p class="text-danger">解释失败: ${error.message}</p></div>`;
        document.getElementById('featureImportance').innerHTML = `<div class="text-center py-5"><p class="text-danger">特征重要性计算失败: ${error.message}</p></div>`;

        // 如果API调用失败，使用模拟数据作为备选
        setTimeout(() => {
            // 生成模拟预测数据
            const mockData = generateMockPredictionData(currentTicker, parseInt(predictionPeriod), parseFloat(confidenceInterval));

            // 更新UI
            updatePredictionChart(mockData);
            // 注意: 这里不需要调用updatePredictionTable，因为它已经在model-prediction.js中实现
            updateModelEvaluation(mockData);
            updateModelExplanation(mockData);
            updateFeatureImportance(mockData);

            // 显示模拟数据提示
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning mb-3';
            alertDiv.textContent = '使用模拟数据显示';
            document.getElementById('predictionChart').parentNode.insertBefore(alertDiv, document.getElementById('predictionChart'));
        }, 1000);
    }
}

// 更新预测图表
function updatePredictionChart(data) {
    const chartContainer = document.getElementById('predictionChart');
    chartContainer.innerHTML = '';

    // 使用lightweight-charts创建图表
    const chart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: 400,
        layout: {
            backgroundColor: '#ffffff',
            textColor: '#333',
        },
        grid: {
            vertLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
            horzLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
        },
        timeScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
        },
        // 禁用鼠标滚轮缩放，避免影响页面滚动
        handleScroll: {
            mouseWheel: false,
        },
        // 禁用鼠标滚轮缩放
        handleScale: {
            mouseWheel: false,
        },
    });

    // 添加历史数据线
    const historySeries = chart.addLineSeries({
        color: '#2962FF',
        lineWidth: 2,
        title: '历史数据',
    });

    // 添加预测数据线
    const predictionSeries = chart.addLineSeries({
        color: '#FF6D00',
        lineWidth: 2,
        lineStyle: LightweightCharts.LineStyle.Dotted,
        title: '预测数据',
    });

    // 添加置信区间区域
    const areaSeries = chart.addAreaSeries({
        topColor: 'rgba(255, 109, 0, 0.4)',
        bottomColor: 'rgba(255, 109, 0, 0.1)',
        lineColor: 'rgba(255, 109, 0, 0.3)',
        lineWidth: 1,
        title: '置信区间',
    });

    // 准备数据
    const historyData = data.history_data.map(item => ({
        time: new Date(item.date).getTime() / 1000,
        value: item.price
    }));

    const predictionData = data.prediction_data.map(item => ({
        time: new Date(item.date).getTime() / 1000,
        value: item.price
    }));

    const areaData = data.prediction_data.map(item => ({
        time: new Date(item.date).getTime() / 1000,
        value: item.price,
        high: item.upper_bound,
        low: item.lower_bound
    }));

    // 设置数据
    historySeries.setData(historyData);
    predictionSeries.setData(predictionData);
    areaSeries.setData(areaData);

    // 调整可见范围
    chart.timeScale().fitContent();

    // 添加图例
    const legend = document.createElement('div');
    legend.className = 'd-flex justify-content-center mt-3';
    legend.innerHTML = `
        <div class="me-4"><span class="legend-marker" style="background-color: #2962FF;"></span> 历史数据</div>
        <div class="me-4"><span class="legend-marker" style="background-color: #FF6D00;"></span> 预测数据</div>
        <div><span class="legend-marker" style="background-color: rgba(255, 109, 0, 0.4);"></span> 置信区间</div>
    `;
    chartContainer.parentNode.appendChild(legend);

    // 添加响应式调整
    window.addEventListener('resize', () => {
        chart.applyOptions({ width: chartContainer.clientWidth });
    });
}

// 更新模型评估
function updateModelEvaluation(data) {
    const container = document.getElementById('modelEvaluation');

    if (data.evaluation) {
        container.innerHTML = `
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="evaluation-item">
                        <div class="evaluation-label">均方误差 (RMSE)</div>
                        <div class="evaluation-value">${data.evaluation.rmse.toFixed(4)}</div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="evaluation-item">
                        <div class="evaluation-label">平均绝对误差 (MAE)</div>
                        <div class="evaluation-value">${data.evaluation.mae.toFixed(4)}</div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="evaluation-item">
                        <div class="evaluation-label">决定系数 (R²)</div>
                        <div class="evaluation-value">${data.evaluation.r2.toFixed(4)}</div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="evaluation-item">
                        <div class="evaluation-label">平均绝对百分比误差 (MAPE)</div>
                        <div class="evaluation-value">${data.evaluation.mape.toFixed(2)}%</div>
                    </div>
                </div>
            </div>
        `;
    } else {
        container.innerHTML = '<div class="text-center py-5"><p class="text-muted">模型评估数据不可用</p></div>';
    }
}

// 更新模型解释
function updateModelExplanation(data) {
    const container = document.getElementById('modelExplanation');

    if (data.explanation) {
        container.innerHTML = `
            <div class="model-explanation">
                <p>${data.explanation}</p>
            </div>
        `;
    } else {
        container.innerHTML = '<div class="text-center py-5"><p class="text-muted">模型解释不可用</p></div>';
    }
}

// 更新特征重要性
function updateFeatureImportance(data) {
    const container = document.getElementById('featureImportance');

    if (data.feature_importance && data.feature_importance.length > 0) {
        // 创建特征重要性图表
        container.innerHTML = '<canvas id="featureImportanceChart" height="300"></canvas>';

        const ctx = document.getElementById('featureImportanceChart').getContext('2d');

        // 准备数据
        const features = data.feature_importance.map(item => item.feature);
        const importance = data.feature_importance.map(item => item.importance);

        // 创建图表
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: features,
                datasets: [{
                    label: '特征重要性',
                    data: importance,
                    backgroundColor: 'rgba(75, 192, 192, 0.6)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '重要性分数'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '特征'
                        }
                    }
                }
            }
        });
    } else {
        container.innerHTML = '<div class="text-center py-5"><p class="text-muted">特征重要性数据不可用</p></div>';
    }
}

// 生成模拟预测数据
function generateMockPredictionData(ticker, predictionPeriod, confidenceInterval) {
    const today = new Date();
    const historyData = [];
    const predictionData = [];

    // 生成历史数据
    let price = Math.random() * 500 + 100;
    for (let i = 30; i >= 1; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);

        price += (Math.random() - 0.5) * 10;
        historyData.push({
            date: date.toISOString().split('T')[0],
            price: price
        });
    }

    // 生成预测数据
    const trend = Math.random() > 0.5 ? 1 : -1;
    const volatility = Math.random() * 5 + 2;

    for (let i = 1; i <= predictionPeriod; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);

        price += trend * (Math.random() * volatility);
        const ciWidth = price * (1 - confidenceInterval) * 2;

        predictionData.push({
            date: date.toISOString().split('T')[0],
            price: price,
            lower_bound: price - ciWidth / 2,
            upper_bound: price + ciWidth / 2
        });
    }

    // 生成模型评估数据
    const evaluation = {
        rmse: Math.random() * 5 + 1,
        mae: Math.random() * 3 + 0.5,
        r2: Math.random() * 0.5 + 0.5,
        mape: Math.random() * 10 + 2
    };

    // 生成模型解释
    const explanations = [
        `基于对${ticker}的历史数据分析，模型预测未来${predictionPeriod}天内股价将呈${trend > 0 ? '上升' : '下降'}趋势。这主要是由于最近的交易量和价格模式显示出的市场${trend > 0 ? '看涨' : '看跌'}情绪。`,
        `模型预测${ticker}在未来${predictionPeriod}天内将经历一定程度的波动，但总体趋势为${trend > 0 ? '上升' : '下降'}。这一预测考虑了历史价格模式、技术指标和市场情绪等因素。`,
        `根据对${ticker}的分析，模型预测在${predictionPeriod}天的时间范围内，股价将总体${trend > 0 ? '上升约' : '下降约'}${(Math.random() * 10 + 2).toFixed(2)}%。这一预测基于历史数据模式和当前市场条件。`
    ];

    const explanation = explanations[Math.floor(Math.random() * explanations.length)];

    // 生成特征重要性数据
    const features = [
        '历史价格',
        '交易量',
        '移动平均线',
        '相对强弱指标',
        '市场情绪',
        '市场波动率'
    ];

    const featureImportance = features.map(feature => ({
        feature: feature,
        importance: Math.random() * 0.8 + 0.2
    }));

    // 按重要性降序排序
    featureImportance.sort((a, b) => b.importance - a.importance);

    return {
        history_data: historyData,
        prediction_data: predictionData,
        evaluation: evaluation,
        explanation: explanation,
        feature_importance: featureImportance
    };
}

// 更新股票图表
async function updateStockChart(ticker, period) {
    // 显示加载动画
    const chartContainer = document.getElementById('stockChart').parentNode;
    chartContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 将period转换为duration参数
        let duration = '30';
        switch(period) {
            case '1d': duration = '1'; break;
            case '1w': duration = '7'; break;
            case '1m': duration = '30'; break;
            case '3m': duration = '90'; break;
            case '1y': duration = '365'; break;
        }

        // 调用真实API
        const response = await fetch(`${API_BASE_URL}/stock/${ticker}/chart?price_type=收盘&duration=${duration}`, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const chartData = await response.json();

        // 创建新的canvas元素
        chartContainer.innerHTML = '<canvas id="stockChart" height="300"></canvas>';

        // 获取新的canvas上下文
        const ctx = document.getElementById('stockChart').getContext('2d');

        // 准备数据
        const labels = chartData.dates || [];
        const prices = chartData.values || [];

        // 销毁旧图表
        if (stockChart) {
            stockChart.destroy();
        }

        // 创建新图表
        stockChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: `${ticker} 价格`,
                    data: prices,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: '#0d6efd',
                    pointHoverBorderColor: '#fff',
                    pointHoverBorderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
    } catch (error) {
        console.error('获取股票图表数据失败:', error);
        chartContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">获取数据失败: ${error.message}</p></div>`;

        // 如果API调用失败，使用模拟数据作为备选
        setTimeout(() => {
            // 创建新的canvas元素
            chartContainer.innerHTML = '<canvas id="stockChart" height="300"></canvas>';

            // 获取新的canvas上下文
            const ctx = document.getElementById('stockChart').getContext('2d');

            // 模拟数据
            const data = generateMockStockData(period);

            // 销毁旧图表
            if (stockChart) {
                stockChart.destroy();
            }

            // 创建新图表
            stockChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: `${ticker} 价格 (模拟数据)`,
                        data: data.prices,
                        borderColor: '#0d6efd',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHoverBackgroundColor: '#0d6efd',
                        pointHoverBorderColor: '#fff',
                        pointHoverBorderWidth: 2,
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        }, 1000);
    }
}

// 获取股票详情
async function fetchStockDetails(ticker) {
    // 显示加载动画
    const detailsContainer = document.getElementById('stockDetails');
    detailsContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 调用真实API获取股票价格统计数据
        const priceResponse = await fetch(`${API_BASE_URL}/stock/${ticker}/price-stats?operation=最新&price_type=收盘&duration=1`, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!priceResponse.ok) {
            throw new Error(`API错误: ${priceResponse.status}`);
        }

        const priceData = await priceResponse.json();

        // 调用其他API获取更多详情
        const highResponse = await fetch(`${API_BASE_URL}/stock/${ticker}/price-stats?operation=最高&price_type=高&duration=1`, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        const lowResponse = await fetch(`${API_BASE_URL}/stock/${ticker}/price-stats?operation=最低&price_type=低&duration=1`, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        const openResponse = await fetch(`${API_BASE_URL}/stock/${ticker}/price-stats?operation=最新&price_type=开盘&duration=1`, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        const highData = await highResponse.json();
        const lowData = await lowResponse.json();
        const openData = await openResponse.json();

        // 整合数据
        const details = {
            price: priceData.value || 0,
            change: priceData.change || 0,
            changePercent: priceData.change_percent || 0,
            open: openData.value || 0,
            high: highData.value || 0,
            low: lowData.value || 0,
            volume: priceData.volume || 0,
            marketCap: priceData.market_cap || 0,
            pe: priceData.pe_ratio || 0,
            dividend: priceData.dividend_yield || 0
        };

        // 更新UI
        detailsContainer.innerHTML = `
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">当前价格</div>
                    <div class="stock-detail-value">$${details.price.toFixed(2)}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">涨跌幅</div>
                    <div class="stock-detail-value ${details.change > 0 ? 'positive' : 'negative'}">
                        ${details.change > 0 ? '+' : ''}${details.change.toFixed(2)} (${details.changePercent.toFixed(2)}%)
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">开盘价</div>
                    <div class="stock-detail-value">$${details.open.toFixed(2)}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">最高价</div>
                    <div class="stock-detail-value">$${details.high.toFixed(2)}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">最低价</div>
                    <div class="stock-detail-value">$${details.low.toFixed(2)}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">成交量</div>
                    <div class="stock-detail-value">${details.volume.toLocaleString()}</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">市值</div>
                    <div class="stock-detail-value">$${(details.marketCap / 1000000000).toFixed(2)}B</div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="stock-detail-item">
                    <div class="stock-detail-label">市盈率</div>
                    <div class="stock-detail-value">${details.pe.toFixed(2)}</div>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('获取股票详情失败:', error);
        detailsContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">获取数据失败: ${error.message}</p></div>`;

        // 如果API调用失败，使用模拟数据作为备选
        setTimeout(() => {
            // 模拟数据
            const details = {
                price: (Math.random() * 1000 + 100).toFixed(2),
                change: (Math.random() * 20 - 10).toFixed(2),
                changePercent: (Math.random() * 5 - 2.5).toFixed(2),
                open: (Math.random() * 1000 + 100).toFixed(2),
                high: (Math.random() * 1000 + 150).toFixed(2),
                low: (Math.random() * 1000 + 50).toFixed(2),
                volume: Math.floor(Math.random() * 10000000),
                marketCap: Math.floor(Math.random() * 1000000000000),
                pe: (Math.random() * 30 + 5).toFixed(2),
                dividend: (Math.random() * 5).toFixed(2)
            };

            // 更新UI
            detailsContainer.innerHTML = `
                <div class="alert alert-warning mb-3">使用模拟数据显示</div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">当前价格</div>
                        <div class="stock-detail-value">$${details.price}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">涨跌幅</div>
                        <div class="stock-detail-value ${details.change > 0 ? 'positive' : 'negative'}">
                            ${details.change > 0 ? '+' : ''}${details.change} (${details.changePercent}%)
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">开盘价</div>
                        <div class="stock-detail-value">$${details.open}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">最高价</div>
                        <div class="stock-detail-value">$${details.high}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">最低价</div>
                        <div class="stock-detail-value">$${details.low}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">成交量</div>
                        <div class="stock-detail-value">${details.volume.toLocaleString()}</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">市值</div>
                        <div class="stock-detail-value">$${(details.marketCap / 1000000000).toFixed(2)}B</div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stock-detail-item">
                        <div class="stock-detail-label">市盈率</div>
                        <div class="stock-detail-value">${details.pe}</div>
                    </div>
                </div>
            `;
        }, 1000);
    }
}

// 获取新闻
async function fetchNews(ticker, topic = '') {
    // 显示加载动画
    const newsContainer = document.getElementById('newsList');
    newsContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 构建API URL
        let url = `${API_BASE_URL}/news/${ticker}`;
        if (topic) {
            url += `?topic=${encodeURIComponent(topic)}`;
        }

        // 调用真实API
        const response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const data = await response.json();

        // 处理API响应
        if (data && data.result) {
            // 将API响应转换为新闻项目列表
            // 这里需要根据API的实际响应格式进行调整
            const newsText = data.result;

            // 将文本分割成段落，每个段落作为一条新闻
            const paragraphs = newsText.split('\n\n').filter(p => p.trim().length > 0);

            if (paragraphs.length > 0) {
                newsContainer.innerHTML = '';

                paragraphs.forEach((paragraph, index) => {
                    // 尝试提取标题和内容
                    const lines = paragraph.split('\n');
                    const title = lines[0] || `${ticker}相关新闻 ${index + 1}`;
                    const content = lines.slice(1).join('\n') || paragraph;

                    const newsItem = document.createElement('div');
                    newsItem.className = 'news-item';
                    newsItem.innerHTML = `
                        <div class="news-date">${new Date().toISOString().split('T')[0]}</div>
                        <h5 class="news-title">${title}</h5>
                        <p>${content}</p>
                        <div class="news-source">来源: 智能体投顾助手</div>
                    `;
                    newsContainer.appendChild(newsItem);
                });
            } else {
                newsContainer.innerHTML = '<div class="text-center py-5"><p class="text-muted">没有找到相关新闻</p></div>';
            }
        } else {
            newsContainer.innerHTML = '<div class="text-center py-5"><p class="text-muted">没有找到相关新闻</p></div>';
        }
    } catch (error) {
        console.error('获取新闻失败:', error);
        newsContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">获取新闻失败: ${error.message}</p></div>`;

        // 如果API调用失败，使用模拟数据作为备选
        setTimeout(() => {
            // 模拟数据
            const news = generateMockNews(ticker, topic);

            // 更新UI
            if (news.length > 0) {
                newsContainer.innerHTML = '<div class="alert alert-warning mb-3">使用模拟数据显示</div>';
                news.forEach(item => {
                    const newsItem = document.createElement('div');
                    newsItem.className = 'news-item';
                    newsItem.innerHTML = `
                        <div class="news-date">${item.date}</div>
                        <h5 class="news-title">${item.title}</h5>
                        <p>${item.summary}</p>
                        <div class="news-source">来源: ${item.source}</div>
                    `;
                    newsContainer.appendChild(newsItem);
                });
            } else {
                newsContainer.innerHTML = '<div class="text-center py-5"><p class="text-muted">没有找到相关新闻</p></div>';
            }
        }, 1000);
    }
}

// 登录
async function login(username, password) {
    // 显示加载状态
    const submitBtn = document.querySelector('#loginForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 登录中...';
    submitBtn.disabled = true;

    try {
        // 调用真实API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                'username': username,
                'password': password
            })
        });

        if (!response.ok) {
            throw new Error(`登录失败: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.access_token) {
            // 获取用户信息
            const userResponse = await fetch(`${API_BASE_URL}/${API_VERSION}/user/me`, {
                headers: {
                    'Authorization': `Bearer ${data.access_token}`
                }
            });

            if (!userResponse.ok) {
                throw new Error(`获取用户信息失败: ${userResponse.status}`);
            }

            const userData = await userResponse.json();

            // 从响应中提取用户数据
            const userInfo = userData.data || {
                username: data.username || username,
                email: data.email || '',
                full_name: data.full_name || ''
            };

            // 保存登录信息
            accessToken = data.access_token;
            currentUser = userInfo;
            isLoggedIn = true;

            // 保存到本地存储
            localStorage.setItem('accessToken', accessToken);
            localStorage.setItem('user', JSON.stringify(currentUser));

            // 更新UI
            updateLoginUI();

            // 关闭模态框
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            loginModal.hide();

            // 重置表单
            document.getElementById('loginForm').reset();

            // 显示成功消息
            alert('登录成功！');
        } else {
            throw new Error('登录响应中缺少访问令牌');
        }
    } catch (error) {
        console.error('登录失败:', error);
        alert(`登录失败: ${error.message}`);
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 注册
async function register(username, email, fullName, password) {
    // 显示加载状态
    const submitBtn = document.querySelector('#registerForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 注册中...';
    submitBtn.disabled = true;

    try {
        // 调用真实API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                email: email,
                full_name: fullName,
                password: password  // 密码将在服务器端进行哈希
            })
        });

        if (!response.ok) {
            throw new Error(`注册失败: ${response.status}`);
        }

        const data = await response.json();

        // 关闭模态框
        const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
        registerModal.hide();

        // 重置表单
        document.getElementById('registerForm').reset();

        // 显示成功消息
        alert('注册成功！请登录您的账号。');

        // 打开登录模态框
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    } catch (error) {
        console.error('注册失败:', error);
        alert(`注册失败: ${error.message}`);
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 退出登录
function logout() {
    // 清除登录信息
    accessToken = null;
    currentUser = null;
    isLoggedIn = false;

    // 清除本地存储
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');

    // 刷新页面
    location.reload();
}

// 生成模拟股票数据
function generateMockStockData(period) {
    const labels = [];
    const prices = [];
    let dataPoints = 0;
    let startPrice = Math.random() * 500 + 100;

    switch (period) {
        case '1d':
            dataPoints = 24;
            for (let i = 0; i < dataPoints; i++) {
                const hour = i % 12 + 1;
                const ampm = i < 12 ? 'AM' : 'PM';
                labels.push(`${hour}:00 ${ampm}`);
                startPrice += (Math.random() - 0.5) * 5;
                prices.push(startPrice);
            }
            break;
        case '1w':
            dataPoints = 7;
            const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            for (let i = 0; i < dataPoints; i++) {
                labels.push(days[i]);
                startPrice += (Math.random() - 0.5) * 20;
                prices.push(startPrice);
            }
            break;
        case '1m':
            dataPoints = 30;
            for (let i = 0; i < dataPoints; i++) {
                labels.push(`${i + 1}日`);
                startPrice += (Math.random() - 0.5) * 30;
                prices.push(startPrice);
            }
            break;
        case '3m':
            dataPoints = 12;
            for (let i = 0; i < dataPoints; i++) {
                labels.push(`第${i + 1}周`);
                startPrice += (Math.random() - 0.5) * 50;
                prices.push(startPrice);
            }
            break;
        case '1y':
            dataPoints = 12;
            const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
            for (let i = 0; i < dataPoints; i++) {
                labels.push(months[i]);
                startPrice += (Math.random() - 0.5) * 100;
                prices.push(startPrice);
            }
            break;
    }

    return { labels, prices };
}

// 生成模拟新闻数据
function generateMockNews(ticker, topic = '') {
    const news = [];
    const sources = ['财经网', '证券时报', '华尔街日报', '彭博社', '路透社'];
    const today = new Date();

    // 生成5-10条新闻
    const count = Math.floor(Math.random() * 6) + 5;

    for (let i = 0; i < count; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);

        let title = '';
        let summary = '';

        if (topic) {
            // 根据主题生成新闻
            switch (topic) {
                case '财报':
                    title = `${ticker} 公布${Math.floor(Math.random() * 4) + 1}季度财报，${Math.random() > 0.5 ? '超出' : '未达'}市场预期`;
                    summary = `${ticker} 公司${Math.floor(Math.random() * 4) + 1}季度营收达到${(Math.random() * 100).toFixed(2)}亿美元，${Math.random() > 0.5 ? '同比增长' : '同比下降'}${(Math.random() * 20).toFixed(1)}%。每股收益${(Math.random() * 5).toFixed(2)}美元，${Math.random() > 0.5 ? '超出' : '低于'}分析师预期。`;
                    break;
                case '产品':
                    title = `${ticker} 宣布推出新${Math.random() > 0.5 ? '产品' : '服务'}，瞄准${Math.random() > 0.5 ? '消费' : '企业'}市场`;
                    summary = `${ticker} 公司今日宣布推出全新${Math.random() > 0.5 ? '产品' : '服务'}，旨在${Math.random() > 0.5 ? '提升用户体验' : '扩大市场份额'}。分析师普遍${Math.random() > 0.5 ? '看好' : '持谨慎态度'}，预计将对公司业绩产生${Math.random() > 0.5 ? '积极' : '一定'}影响。`;
                    break;
                case '市场':
                    title = `分析师${Math.random() > 0.5 ? '上调' : '下调'} ${ticker} 目标价，${Math.random() > 0.5 ? '看好' : '担忧'}其市场前景`;
                    summary = `多家券商${Math.random() > 0.5 ? '上调' : '下调'} ${ticker} 目标价，${Math.random() > 0.5 ? '看好' : '担忧'}其在${Math.random() > 0.5 ? '国内' : '国际'}市场的发展前景。目前市场普遍预期该股${Math.random() > 0.5 ? '有上涨空间' : '面临调整压力'}。`;
                    break;
                default:
                    title = `${ticker} ${Math.random() > 0.5 ? '股价' : '市值'}${Math.random() > 0.5 ? '上涨' : '下跌'}，${Math.random() > 0.5 ? '投资者' : '分析师'}${Math.random() > 0.5 ? '看好' : '担忧'}前景`;
                    summary = `受${Math.random() > 0.5 ? '市场情绪' : '行业动态'}影响，${ticker} 今日${Math.random() > 0.5 ? '股价' : '市值'}${Math.random() > 0.5 ? '上涨' : '下跌'}${(Math.random() * 10).toFixed(2)}%。分析师认为，这主要是由于${Math.random() > 0.5 ? '公司基本面' : '外部环境'}变化所致。`;
            }
        } else {
            // 随机生成新闻
            const topics = ['财报', '产品', '市场', '人事', '战略'];
            const randomTopic = topics[Math.floor(Math.random() * topics.length)];

            switch (randomTopic) {
                case '财报':
                    title = `${ticker} 公布${Math.floor(Math.random() * 4) + 1}季度财报，${Math.random() > 0.5 ? '超出' : '未达'}市场预期`;
                    summary = `${ticker} 公司${Math.floor(Math.random() * 4) + 1}季度营收达到${(Math.random() * 100).toFixed(2)}亿美元，${Math.random() > 0.5 ? '同比增长' : '同比下降'}${(Math.random() * 20).toFixed(1)}%。每股收益${(Math.random() * 5).toFixed(2)}美元，${Math.random() > 0.5 ? '超出' : '低于'}分析师预期。`;
                    break;
                case '产品':
                    title = `${ticker} 宣布推出新${Math.random() > 0.5 ? '产品' : '服务'}，瞄准${Math.random() > 0.5 ? '消费' : '企业'}市场`;
                    summary = `${ticker} 公司今日宣布推出全新${Math.random() > 0.5 ? '产品' : '服务'}，旨在${Math.random() > 0.5 ? '提升用户体验' : '扩大市场份额'}。分析师普遍${Math.random() > 0.5 ? '看好' : '持谨慎态度'}，预计将对公司业绩产生${Math.random() > 0.5 ? '积极' : '一定'}影响。`;
                    break;
                case '市场':
                    title = `分析师${Math.random() > 0.5 ? '上调' : '下调'} ${ticker} 目标价，${Math.random() > 0.5 ? '看好' : '担忧'}其市场前景`;
                    summary = `多家券商${Math.random() > 0.5 ? '上调' : '下调'} ${ticker} 目标价，${Math.random() > 0.5 ? '看好' : '担忧'}其在${Math.random() > 0.5 ? '国内' : '国际'}市场的发展前景。目前市场普遍预期该股${Math.random() > 0.5 ? '有上涨空间' : '面临调整压力'}。`;
                    break;
                case '人事':
                    title = `${ticker} 宣布${Math.random() > 0.5 ? 'CEO' : 'CFO'}${Math.random() > 0.5 ? '离职' : '任命'}，${Math.random() > 0.5 ? '市场反应积极' : '引发投资者担忧'}`;
                    summary = `${ticker} 公司今日宣布${Math.random() > 0.5 ? 'CEO' : 'CFO'}${Math.random() > 0.5 ? '离职' : '任命'}。${Math.random() > 0.5 ? '分析师认为这将为公司带来新的发展机遇' : '市场对此反应谨慎，担忧可能影响公司战略连续性'}。`;
                    break;
                case '战略':
                    title = `${ticker} 宣布${Math.random() > 0.5 ? '收购' : '战略合作'}计划，${Math.random() > 0.5 ? '加码' : '布局'} ${Math.random() > 0.5 ? '人工智能' : '云计算'}领域`;
                    summary = `${ticker} 公司宣布将${Math.random() > 0.5 ? '收购' : '与'}${Math.random() > 0.5 ? '一家初创公司' : '行业领导者'}${Math.random() > 0.5 ? '' : '达成战略合作'}，进一步${Math.random() > 0.5 ? '加码' : '布局'} ${Math.random() > 0.5 ? '人工智能' : '云计算'}领域。此举被视为公司${Math.random() > 0.5 ? '转型' : '扩张'}战略的重要一步。`;
                    break;
            }
        }

        news.push({
            title: title,
            summary: summary,
            date: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`,
            source: sources[Math.floor(Math.random() * sources.length)]
        });
    }

    return news;
}
