from fastapi import APIRouter, HTTPException
from rest_api.services import data_fetcher

router = APIRouter()

@router.get("/stock/{ticker}/chart")
async def get_stock_chart(ticker: str, price_type: str = "收盘", duration: str = "30"):
    """
    获取股票图表数据
    """
    # 转换duration为period
    period_map = {
        "1": "1d",
        "7": "1w",
        "30": "1m",
        "90": "3m",
        "365": "1y"
    }
    period = period_map.get(duration, "1m")

    data = data_fetcher.get_stock_data(ticker, period=period)

    if not data['dates']:
        raise HTTPException(status_code=404, detail=f"未找到股票 {ticker} 的数据")

    return data

@router.get("/stock/{ticker}/intraday")
async def get_intraday_data(ticker: str):
    """
    获取日内分钟级别数据
    """
    data = data_fetcher.get_intraday_data(ticker)

    # 即使数据为空，也会返回模拟数据，所以不需要抛出异常
    return data

@router.get("/stock/{ticker}/candlestick")
async def get_candlestick_data(ticker: str, period: str = "1mo", interval: str = "1d"):
    """
    获取K线图数据

    参数:
    ticker: 股票代码
    period: 时间周期，如 '1d', '5d', '1mo', '3mo', '1y'
    interval: 时间间隔，如 '1m', '5m', '15m', '30m', '60m', '1d'
    """
    data = data_fetcher.get_candlestick_data(ticker, period=period, interval=interval)

    # 即使数据为空，也会返回模拟数据，所以不需要抛出异常
    return data

@router.get("/stock/{ticker}/price-stats")
async def get_price_stats(
    ticker: str,
    operation: str = "最新",
    price_type: str = "收盘",
    duration: str = "1"
):
    """
    获取股票价格统计数据
    """
    data = data_fetcher.get_stock_info(ticker)

    if not data:
        raise HTTPException(status_code=404, detail=f"未找到股票 {ticker} 的价格统计数据")

    return data

@router.get("/stock/{ticker}/indicators")
async def get_indicators(ticker: str, type: str = "ma"):
    """
    获取技术指标数据
    """
    if type in ["ma", "rsi", "macd", "bollinger"]:
        data = data_fetcher.get_technical_indicators(ticker, type)
    elif type in ["volume", "obv", "atr", "adx"]:
        data = data_fetcher.get_extended_indicators(ticker, type)
    else:
        raise HTTPException(status_code=400, detail=f"不支持的指标类型: {type}")

    if not data:
        raise HTTPException(status_code=404, detail=f"未找到股票 {ticker} 的指标数据")

    return data
