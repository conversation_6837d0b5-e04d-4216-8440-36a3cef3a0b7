<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员控制台 - 智能体投顾助手</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../static/css/style.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">智能体投顾助手</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/model">模型</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/portfolio">投资组合</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin">管理控制台</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <button id="loginBtn" class="btn btn-light me-2">登录</button>
                    <button id="registerBtn" class="btn btn-outline-light">注册</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <div class="container mt-4">
        <!-- 未登录提示 -->
        <div id="loginAlert" class="alert alert-warning" style="display: none;">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            请先登录以访问管理员功能。
        </div>

        <!-- 非管理员提示 -->
        <div id="adminAlert" class="alert alert-danger" style="display: none;">
            <i class="bi bi-shield-exclamation me-2"></i>
            您没有管理员权限，无法访问此页面。
        </div>

        <!-- 管理员控制台 -->
        <div id="adminConsole" style="display: none;">
            <h2 class="mb-4"><i class="bi bi-shield-lock me-2"></i>管理员控制台</h2>

            <!-- 管理选项卡 -->
            <ul class="nav nav-tabs mb-4" id="adminTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="true">
                        <i class="bi bi-people me-2"></i>用户管理
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab" aria-controls="system" aria-selected="false">
                        <i class="bi bi-gear me-2"></i>系统设置
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="false">
                        <i class="bi bi-journal-text me-2"></i>系统日志
                    </button>
                </li>
            </ul>

            <!-- 选项卡内容 -->
            <div class="tab-content" id="adminTabContent">
                <!-- 用户管理 -->
                <div class="tab-pane fade show active" id="users" role="tabpanel" aria-labelledby="users-tab">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">用户列表</h5>
                            <button id="refreshUserList" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-arrow-clockwise me-1"></i>刷新
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>用户名</th>
                                            <th>邮箱</th>
                                            <th>姓名</th>
                                            <th>注册时间</th>
                                            <th>最后登录</th>
                                            <th>状态</th>
                                            <th>权限</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="userTableBody">
                                        <!-- 用户数据将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div class="tab-pane fade" id="system" role="tabpanel" aria-labelledby="system-tab">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">系统配置</h5>
                        </div>
                        <div class="card-body">
                            <form id="systemSettingsForm">
                                <div class="mb-3">
                                    <label for="siteName" class="form-label">网站名称</label>
                                    <input type="text" class="form-control" id="siteName" value="智能体投顾助手">
                                </div>
                                <div class="mb-3">
                                    <label for="apiTimeout" class="form-label">API超时设置 (秒)</label>
                                    <input type="number" class="form-control" id="apiTimeout" value="30">
                                </div>
                                <div class="mb-3">
                                    <label for="maxLoginAttempts" class="form-label">最大登录尝试次数</label>
                                    <input type="number" class="form-control" id="maxLoginAttempts" value="5">
                                </div>
                                <div class="mb-3">
                                    <label for="sessionTimeout" class="form-label">会话超时 (分钟)</label>
                                    <input type="number" class="form-control" id="sessionTimeout" value="30">
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                    <label class="form-check-label" for="maintenanceMode">维护模式</label>
                                </div>
                                <button type="submit" class="btn btn-primary">保存设置</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 系统日志 -->
                <div class="tab-pane fade" id="logs" role="tabpanel" aria-labelledby="logs-tab">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="mb-0">系统日志</h5>
                                <button id="refreshLogs" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                </button>
                            </div>
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <select id="logLevel" class="form-select form-select-sm">
                                        <option value="all">所有级别</option>
                                        <option value="HIGH">高级别</option>
                                        <option value="MEDIUM">中级别</option>
                                        <option value="LOW">低级别</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select id="activityType" class="form-select form-select-sm">
                                        <option value="all">所有类型</option>
                                        <!-- 活动类型将通过JavaScript动态加载 -->
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <input type="date" id="startDate" class="form-control form-control-sm" placeholder="开始日期">
                                </div>
                                <div class="col-md-3">
                                    <input type="date" id="endDate" class="form-control form-control-sm" placeholder="结束日期">
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="log-container p-3 bg-dark text-light rounded" style="height: 400px; overflow-y: auto; font-family: monospace;">
                                <div id="logContent">
                                    <!-- 日志内容将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="loginModalLabel">登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">登录</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1" aria-labelledby="registerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="registerModalLabel">注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="mb-3">
                            <label for="registerUsername" class="form-label">用户名</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="registerUsername" required>
                                <button class="btn btn-outline-secondary" type="button" id="generateUsernameBtn">生成用户名</button>
                            </div>
                            <div class="form-text" id="usernameHelp">用户名不能重复，点击生成按钮可自动生成用户名</div>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerFullName" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="registerFullName">
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required minlength="6">
                            <div class="form-text">密码至少需要 6 位字符</div>
                        </div>
                        <div class="mb-3">
                            <label for="registerConfirmPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="registerConfirmPassword" required minlength="6">
                        </div>
                        <div class="alert alert-danger" id="registerError" style="display: none;">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span id="registerErrorMessage"></span>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">注册</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户操作模态框 -->
    <div class="modal fade" id="userActionModal" tabindex="-1" aria-labelledby="userActionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userActionModalLabel">用户详细信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="userDetailTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab" aria-controls="basic" aria-selected="true">
                                <i class="bi bi-person me-2"></i>基本信息
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                                <i class="bi bi-shield-lock me-2"></i>安全设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab" aria-controls="activity" aria-selected="false">
                                <i class="bi bi-activity me-2"></i>活动记录
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="portfolio-tab" data-bs-toggle="tab" data-bs-target="#portfolio" type="button" role="tab" aria-controls="portfolio" aria-selected="false">
                                <i class="bi bi-briefcase me-2"></i>投资组合
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content pt-3" id="userDetailTabContent">
                        <!-- 基本信息 -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                            <form id="userActionForm">
                                <input type="hidden" id="actionUsername">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">用户名</label>
                                        <p id="displayUsername" class="form-control-plaintext"></p>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">邮箱</label>
                                        <p id="displayEmail" class="form-control-plaintext"></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">姓名</label>
                                        <p id="displayFullName" class="form-control-plaintext"></p>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">注册时间</label>
                                        <p id="displayRegisterTime" class="form-control-plaintext"></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">最后登录时间</label>
                                        <p id="displayLastLogin" class="form-control-plaintext"></p>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">最后登录IP</label>
                                        <p id="displayLastIP" class="form-control-plaintext"></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="userAdminStatus">
                                            <label class="form-check-label" for="userAdminStatus">管理员权限</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="userDisabledStatus">
                                            <label class="form-check-label" for="userDisabledStatus">禁用账户</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">保存更改</button>
                                </div>
                            </form>
                        </div>

                        <!-- 安全设置 -->
                        <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0">密码管理</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">上次密码修改时间</label>
                                        <p id="displayPasswordChangeTime" class="form-control-plaintext">未知</p>
                                    </div>
                                    <button id="resetPasswordBtn" class="btn btn-warning">重置密码</button>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0">API令牌</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">活跃令牌数量</label>
                                        <p id="displayTokenCount" class="form-control-plaintext">0</p>
                                    </div>
                                    <button id="manageTokensBtn" class="btn btn-outline-primary">管理API令牌</button>
                                </div>
                            </div>
                        </div>

                        <!-- 活动记录 -->
                        <div class="tab-pane fade" id="activity" role="tabpanel" aria-labelledby="activity-tab">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>活动类型</th>
                                            <th>IP地址</th>
                                            <th>详情</th>
                                        </tr>
                                    </thead>
                                    <tbody id="userActivityTableBody">
                                        <!-- 活动记录将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 投资组合 -->
                        <div class="tab-pane fade" id="portfolio" role="tabpanel" aria-labelledby="portfolio-tab">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>组合名称</th>
                                            <th>创建时间</th>
                                            <th>总价值</th>
                                            <th>盈亏率</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="userPortfolioTableBody">
                                        <!-- 投资组合将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>智能体投顾助手</h5>
                    <p>基于人工智能的投资顾问系统</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2025 智能体投顾助手. 保留所有权利.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../static/js/admin.js"></script>
</body>
</html>
