"""
Social media sentiment analysis for the AI Hedge Fund module.

This file provides functions for analyzing social media sentiment data.
"""

import os
import re
import json
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import io
import base64
import random

from ..data.cache import get_cache


class SocialSentiment:
    """Class representing social media sentiment data for a company."""
    
    def __init__(
        self,
        ticker: str,
        date: str,
        sentiment_score: float,
        sentiment_magnitude: float,
        bullish_count: int,
        bearish_count: int,
        neutral_count: int,
        total_mentions: int,
        source_breakdown: Dict[str, int],
        top_topics: List[str],
        trending_score: float,
        source: str = "mock",
    ):
        """
        Initialize social sentiment data.
        
        Args:
            ticker: Stock ticker symbol
            date: Date of the sentiment data (YYYY-MM-DD)
            sentiment_score: Overall sentiment score (-1.0 to 1.0)
            sentiment_magnitude: Magnitude of sentiment (0.0 to +inf)
            bullish_count: Number of bullish mentions
            bearish_count: Number of bearish mentions
            neutral_count: Number of neutral mentions
            total_mentions: Total number of mentions
            source_breakdown: Dictionary mapping sources to mention counts
            top_topics: List of top topics mentioned
            trending_score: Score indicating how trending the ticker is
            source: Source of the data
        """
        self.ticker = ticker
        self.date = date
        self.sentiment_score = sentiment_score
        self.sentiment_magnitude = sentiment_magnitude
        self.bullish_count = bullish_count
        self.bearish_count = bearish_count
        self.neutral_count = neutral_count
        self.total_mentions = total_mentions
        self.source_breakdown = source_breakdown
        self.top_topics = top_topics
        self.trending_score = trending_score
        self.source = source
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "ticker": self.ticker,
            "date": self.date,
            "sentiment_score": self.sentiment_score,
            "sentiment_magnitude": self.sentiment_magnitude,
            "bullish_count": self.bullish_count,
            "bearish_count": self.bearish_count,
            "neutral_count": self.neutral_count,
            "total_mentions": self.total_mentions,
            "source_breakdown": self.source_breakdown,
            "top_topics": self.top_topics,
            "trending_score": self.trending_score,
            "source": self.source,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SocialSentiment":
        """Create from dictionary."""
        return cls(**data)


def get_social_sentiment(ticker: str, date: str) -> Optional[SocialSentiment]:
    """
    Fetch social media sentiment data for a company.
    
    Args:
        ticker: Stock ticker symbol
        date: Date (YYYY-MM-DD)
        
    Returns:
        SocialSentiment object or None if data not available
    """
    # Get cache instance
    cache = get_cache()
    
    # Check cache first
    if cached_data := cache.get_social_sentiment(ticker):
        # Find the closest date
        closest_date = None
        min_diff = float("inf")
        
        target_date = datetime.strptime(date, "%Y-%m-%d")
        
        for item in cached_data:
            item_date = datetime.strptime(item["date"], "%Y-%m-%d")
            diff = abs((target_date - item_date).days)
            
            if diff < min_diff:
                min_diff = diff
                closest_date = item
        
        # Use cached data if it's within 7 days
        if closest_date and min_diff <= 7:
            return SocialSentiment.from_dict(closest_date)
    
    # If not in cache or no recent data, fetch from API
    headers = {}
    if api_key := os.environ.get("ALTERNATIVE_DATA_API_KEY"):
        headers["X-API-KEY"] = api_key
    
    url = f"https://api.alternativedata.io/social-sentiment/?ticker={ticker}&date={date}"
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        data = response.json()
        
        if "data" in data and data["data"]:
            sentiment_data = SocialSentiment.from_dict(data["data"])
            
            # Cache the results
            if cached_data:
                cached_data.append(sentiment_data.to_dict())
            else:
                cached_data = [sentiment_data.to_dict()]
            
            cache.set_social_sentiment(ticker, cached_data)
            
            return sentiment_data
        else:
            # Fallback to mock data if API returns empty data
            return generate_mock_social_sentiment(ticker, date)
    except Exception as e:
        print(f"Error fetching social sentiment data for {ticker}: {e}")
        
        # Fallback to mock data if API call fails
        return generate_mock_social_sentiment(ticker, date)


def generate_mock_social_sentiment(ticker: str, date: str) -> SocialSentiment:
    """
    Generate mock social media sentiment data for testing.
    
    Args:
        ticker: Stock ticker symbol
        date: Date (YYYY-MM-DD)
        
    Returns:
        SocialSentiment object with mock data
    """
    # Seed random number generator with ticker and date for consistency
    random.seed(sum(ord(c) for c in ticker) + sum(ord(c) for c in date))
    
    # Generate mock sentiment score (-1.0 to 1.0)
    sentiment_score = random.uniform(-1.0, 1.0)
    
    # Generate mock sentiment magnitude (0.0 to 5.0)
    sentiment_magnitude = random.uniform(0.0, 5.0)
    
    # Generate mock mention counts
    total_mentions = random.randint(100, 10000)
    
    # Determine bullish/bearish/neutral counts based on sentiment score
    if sentiment_score > 0.3:
        # Bullish sentiment
        bullish_pct = random.uniform(0.5, 0.8)
        bearish_pct = random.uniform(0.05, 0.2)
    elif sentiment_score < -0.3:
        # Bearish sentiment
        bullish_pct = random.uniform(0.05, 0.2)
        bearish_pct = random.uniform(0.5, 0.8)
    else:
        # Neutral sentiment
        bullish_pct = random.uniform(0.2, 0.4)
        bearish_pct = random.uniform(0.2, 0.4)
    
    neutral_pct = 1.0 - bullish_pct - bearish_pct
    
    bullish_count = int(total_mentions * bullish_pct)
    bearish_count = int(total_mentions * bearish_pct)
    neutral_count = total_mentions - bullish_count - bearish_count
    
    # Generate mock source breakdown
    sources = ["Twitter", "Reddit", "StockTwits", "Yahoo Finance", "Seeking Alpha"]
    source_breakdown = {}
    
    remaining_mentions = total_mentions
    for source in sources[:-1]:
        source_count = random.randint(0, remaining_mentions)
        source_breakdown[source] = source_count
        remaining_mentions -= source_count
    
    source_breakdown[sources[-1]] = remaining_mentions
    
    # Generate mock top topics
    topics = [
        "earnings",
        "growth",
        "valuation",
        "dividend",
        "buyback",
        "CEO",
        "product",
        "competition",
        "regulation",
        "lawsuit",
        "innovation",
        "partnership",
        "acquisition",
        "debt",
        "analyst",
    ]
    
    num_topics = random.randint(3, 7)
    top_topics = random.sample(topics, num_topics)
    
    # Generate mock trending score (0.0 to 100.0)
    trending_score = random.uniform(0.0, 100.0)
    
    return SocialSentiment(
        ticker=ticker,
        date=date,
        sentiment_score=sentiment_score,
        sentiment_magnitude=sentiment_magnitude,
        bullish_count=bullish_count,
        bearish_count=bearish_count,
        neutral_count=neutral_count,
        total_mentions=total_mentions,
        source_breakdown=source_breakdown,
        top_topics=top_topics,
        trending_score=trending_score,
        source="mock",
    )


def get_historical_sentiment(
    ticker: str,
    start_date: str,
    end_date: str,
    interval: str = "daily",
) -> pd.DataFrame:
    """
    Get historical social sentiment data for a company.
    
    Args:
        ticker: Stock ticker symbol
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
        interval: Data interval ("daily" or "weekly")
        
    Returns:
        DataFrame with historical sentiment data
    """
    # Parse dates
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Generate date range
    if interval == "daily":
        dates = [start + timedelta(days=i) for i in range((end - start).days + 1)]
    else:  # weekly
        dates = []
        current = start
        while current <= end:
            dates.append(current)
            current += timedelta(days=7)
    
    # Generate sentiment data for each date
    data = []
    for date in dates:
        date_str = date.strftime("%Y-%m-%d")
        sentiment = get_social_sentiment(ticker, date_str)
        
        if sentiment:
            data.append({
                "date": date_str,
                "sentiment_score": sentiment.sentiment_score,
                "sentiment_magnitude": sentiment.sentiment_magnitude,
                "bullish_count": sentiment.bullish_count,
                "bearish_count": sentiment.bearish_count,
                "neutral_count": sentiment.neutral_count,
                "total_mentions": sentiment.total_mentions,
                "trending_score": sentiment.trending_score,
            })
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Set date as index
    if not df.empty:
        df["date"] = pd.to_datetime(df["date"])
        df.set_index("date", inplace=True)
    
    return df


def analyze_social_sentiment(sentiment_data: SocialSentiment) -> Dict[str, Any]:
    """
    Analyze social media sentiment data and provide insights.
    
    Args:
        sentiment_data: SocialSentiment object
        
    Returns:
        Dictionary with analysis results
    """
    # Initialize results
    results = {
        "ticker": sentiment_data.ticker,
        "date": sentiment_data.date,
        "summary": "",
        "sentiment_signal": "",
        "confidence": 0.0,
        "key_insights": [],
        "source_analysis": "",
        "topic_analysis": "",
    }
    
    # Determine sentiment signal
    if sentiment_data.sentiment_score > 0.3:
        results["sentiment_signal"] = "bullish"
        signal_strength = "strong" if sentiment_data.sentiment_score > 0.6 else "moderate"
    elif sentiment_data.sentiment_score < -0.3:
        results["sentiment_signal"] = "bearish"
        signal_strength = "strong" if sentiment_data.sentiment_score < -0.6 else "moderate"
    else:
        results["sentiment_signal"] = "neutral"
        signal_strength = "weak"
    
    # Calculate confidence based on mention count and sentiment magnitude
    mention_factor = min(1.0, sentiment_data.total_mentions / 5000)  # Cap at 5000 mentions
    magnitude_factor = min(1.0, sentiment_data.sentiment_magnitude / 3.0)  # Cap at 3.0 magnitude
    
    results["confidence"] = (mention_factor * 0.6 + magnitude_factor * 0.4) * 100
    
    # Generate summary
    bullish_pct = sentiment_data.bullish_count / sentiment_data.total_mentions * 100 if sentiment_data.total_mentions > 0 else 0
    bearish_pct = sentiment_data.bearish_count / sentiment_data.total_mentions * 100 if sentiment_data.total_mentions > 0 else 0
    
    results["summary"] = (
        f"Social media sentiment for {sentiment_data.ticker} is {signal_strength} {results['sentiment_signal']} "
        f"with a sentiment score of {sentiment_data.sentiment_score:.2f} and {sentiment_data.total_mentions:,} mentions. "
        f"{bullish_pct:.1f}% of mentions are bullish, while {bearish_pct:.1f}% are bearish."
    )
    
    # Generate key insights
    if sentiment_data.trending_score > 80:
        results["key_insights"].append(f"Extremely high social media activity with trending score of {sentiment_data.trending_score:.1f}/100")
    elif sentiment_data.trending_score > 60:
        results["key_insights"].append(f"High social media activity with trending score of {sentiment_data.trending_score:.1f}/100")
    
    if abs(sentiment_data.sentiment_score) > 0.7:
        results["key_insights"].append(f"Extreme sentiment polarization (score: {sentiment_data.sentiment_score:.2f})")
    
    if sentiment_data.bullish_count > sentiment_data.bearish_count * 3:
        results["key_insights"].append(f"Overwhelmingly bullish sentiment ({bullish_pct:.1f}% bullish vs {bearish_pct:.1f}% bearish)")
    elif sentiment_data.bearish_count > sentiment_data.bullish_count * 3:
        results["key_insights"].append(f"Overwhelmingly bearish sentiment ({bearish_pct:.1f}% bearish vs {bullish_pct:.1f}% bullish)")
    
    # Analyze sources
    top_source = max(sentiment_data.source_breakdown.items(), key=lambda x: x[1]) if sentiment_data.source_breakdown else (None, 0)
    
    if top_source[0]:
        top_source_pct = top_source[1] / sentiment_data.total_mentions * 100 if sentiment_data.total_mentions > 0 else 0
        results["source_analysis"] = f"Most discussion is happening on {top_source[0]} ({top_source_pct:.1f}% of mentions)"
    
    # Analyze topics
    if sentiment_data.top_topics:
        results["topic_analysis"] = f"Top topics: {', '.join(sentiment_data.top_topics)}"
    
    return results


def plot_sentiment_chart(
    sentiment_data: pd.DataFrame,
    ticker: str,
    chart_type: str = "trend",
    title: Optional[str] = None,
) -> str:
    """
    Plot social sentiment data.
    
    Args:
        sentiment_data: DataFrame with sentiment data
        ticker: Stock ticker symbol
        chart_type: Type of chart ("trend", "distribution", or "sources")
        title: Plot title (optional)
        
    Returns:
        Base64-encoded image of the plot
    """
    if sentiment_data.empty:
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, "No sentiment data available", ha="center", va="center", fontsize=14)
        ax.set_title(title or f"Social Sentiment for {ticker}")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    if chart_type == "trend":
        # Create sentiment trend chart
        fig, ax1 = plt.subplots(figsize=(12, 6))
        
        # Plot sentiment score
        ax1.plot(sentiment_data.index, sentiment_data["sentiment_score"], color="blue", linewidth=2, label="Sentiment Score")
        ax1.set_xlabel("Date")
        ax1.set_ylabel("Sentiment Score", color="blue")
        ax1.tick_params(axis="y", labelcolor="blue")
        ax1.axhline(y=0, color="gray", linestyle="--", alpha=0.5)
        
        # Create second y-axis for mention count
        ax2 = ax1.twinx()
        ax2.bar(sentiment_data.index, sentiment_data["total_mentions"], alpha=0.3, color="green", label="Total Mentions")
        ax2.set_ylabel("Total Mentions", color="green")
        ax2.tick_params(axis="y", labelcolor="green")
        
        # Add legend
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc="upper left")
        
        # Format the plot
        plt.title(title or f"Social Sentiment Trend for {ticker}")
        fig.autofmt_xdate()  # Rotate date labels
        plt.grid(True, alpha=0.3)
        
    elif chart_type == "distribution":
        # Create sentiment distribution chart
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Calculate average counts
        avg_bullish = sentiment_data["bullish_count"].mean()
        avg_bearish = sentiment_data["bearish_count"].mean()
        avg_neutral = sentiment_data["neutral_count"].mean()
        
        # Create pie chart
        labels = ["Bullish", "Bearish", "Neutral"]
        sizes = [avg_bullish, avg_bearish, avg_neutral]
        colors = ["green", "red", "gray"]
        explode = (0.1, 0.1, 0)  # Explode bullish and bearish slices
        
        ax.pie(
            sizes,
            explode=explode,
            labels=labels,
            colors=colors,
            autopct="%1.1f%%",
            shadow=True,
            startangle=90,
        )
        ax.axis("equal")  # Equal aspect ratio ensures that pie is drawn as a circle
        
        # Format the plot
        plt.title(title or f"Social Sentiment Distribution for {ticker}")
        
    elif chart_type == "sources":
        # Create sources breakdown chart
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Get the latest data point
        latest_data = sentiment_data.iloc[-1]
        
        # Extract source breakdown (mock data since we don't have it in the DataFrame)
        sources = ["Twitter", "Reddit", "StockTwits", "Yahoo Finance", "Seeking Alpha"]
        source_counts = [
            latest_data["total_mentions"] * 0.4,  # Twitter
            latest_data["total_mentions"] * 0.3,  # Reddit
            latest_data["total_mentions"] * 0.15,  # StockTwits
            latest_data["total_mentions"] * 0.1,  # Yahoo Finance
            latest_data["total_mentions"] * 0.05,  # Seeking Alpha
        ]
        
        # Create horizontal bar chart
        ax.barh(sources, source_counts, color="skyblue")
        
        # Add count labels
        for i, count in enumerate(source_counts):
            ax.text(count + 10, i, f"{int(count):,}", va="center")
        
        # Format the plot
        ax.set_xlabel("Number of Mentions")
        ax.set_title(title or f"Social Media Sources for {ticker}")
        plt.tight_layout()
        
    else:  # Default case
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, f"Unknown chart type: {chart_type}", ha="center", va="center", fontsize=14)
        ax.set_title(title or f"Social Sentiment for {ticker}")
        ax.axis("off")
    
    # Convert plot to base64-encoded image
    buffer = io.BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
    plt.close()
    
    return image_base64
