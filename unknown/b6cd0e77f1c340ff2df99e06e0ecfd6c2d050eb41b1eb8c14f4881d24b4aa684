import unittest
from datetime import datetime
import sys
import os
from unittest.mock import patch, MagicMock
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from web_app.data_fetcher_akshare import (
    format_stock_code,
    get_stock_info,
    get_stock_data,
    get_candlestick_data,
    get_intraday_data,
    generate_mock_stock_info,
    generate_mock_stock_data,
    generate_mock_candlestick_data,
    generate_mock_intraday_data
)

class TestDataFetcherAkshare(unittest.TestCase):
    """测试数据获取模块的测试类"""

    def setUp(self):
        """测试前的设置"""
        self.test_stock_codes = ['600000', 'sh600000', 'sz000001', 'bj430047', 'BABA']

    def test_format_stock_code(self):
        """测试股票代码格式化函数"""
        # 测试上海股票代码
        self.assertEqual(format_stock_code('600000'), 'sh600000')
        self.assertEqual(format_stock_code('sh600000'), 'sh600000')
        
        # 测试深圳股票代码
        self.assertEqual(format_stock_code('000001'), 'sz000001')
        self.assertEqual(format_stock_code('sz000001'), 'sz000001')
        
        # 测试北交所股票代码
        self.assertEqual(format_stock_code('430047'), 'bj430047')
        self.assertEqual(format_stock_code('bj430047'), 'bj430047')
        
        # 测试美股代码转换
        self.assertEqual(format_stock_code('BABA'), 'sh9988')
        
        # 测试无效代码
        self.assertEqual(format_stock_code('999999'), 'sh000001')

    @patch('akshare.stock_zh_a_spot_em')
    def test_get_stock_info(self, mock_spot):
        """测试获取股票信息函数"""
        # 模拟返回数据
        mock_data = pd.DataFrame({
            'code': ['600000'],
            'name': ['测试股票'],
            'current': [10.0],
            'change': [0.5],
            'pct_chg': [5.0],
            'open': [9.5],
            'high': [10.5],
            'low': [9.0],
            'volume': [1000000],
            'amount': [10000000],
            'mkt_cap': [1000000000],
            'pe': [15.0],
            'dividend_yield': [2.5]
        })
        mock_spot.return_value = mock_data

        # 测试获取股票信息
        result = get_stock_info('600000')
        
        self.assertIsInstance(result, dict)
        self.assertEqual(result['code'], 'sh600000')
        self.assertEqual(result['name'], '测试股票')
        self.assertGreater(result['price'], 0)

    def test_generate_mock_stock_info(self):
        """测试生成模拟股票信息函数"""
        for code in self.test_stock_codes:
            result = generate_mock_stock_info(code)
            
            self.assertIsInstance(result, dict)
            self.assertIn('code', result)
            self.assertIn('name', result)
            self.assertIn('price', result)
            self.assertGreater(result['price'], 0)

    def test_generate_mock_stock_data(self):
        """测试生成模拟股票历史数据函数"""
        periods = ['1d', '5d', '1mo', '3mo', '1y']
        intervals = ['1m', '5m', '15m', '30m', '60m', '1d']
        
        for code in self.test_stock_codes:
            for period in periods:
                for interval in intervals:
                    result = generate_mock_stock_data(code, period, interval)
                    
                    self.assertIsInstance(result, dict)
                    self.assertIn('dates', result)
                    self.assertIn('values', result)
                    self.assertGreater(len(result['dates']), 0)
                    self.assertEqual(len(result['dates']), len(result['values']))

    def test_generate_mock_candlestick_data(self):
        """测试生成模拟K线图数据函数"""
        periods = ['1d', '5d', '1mo']
        intervals = ['1m', '5m', '1d']
        
        for code in self.test_stock_codes:
            for period in periods:
                for interval in intervals:
                    result = generate_mock_candlestick_data(code, period, interval)
                    
                    self.assertIsInstance(result, list)
                    self.assertGreater(len(result), 0)
                    
                    # 检查每个数据点的格式
                    for item in result:
                        self.assertIn('time', item)
                        self.assertIn('open', item)
                        self.assertIn('high', item)
                        self.assertIn('low', item)
                        self.assertIn('close', item)
                        self.assertIn('volume', item)
                        
                        # 检查价格逻辑
                        self.assertGreaterEqual(item['high'], item['open'])
                        self.assertGreaterEqual(item['high'], item['close'])
                        self.assertLessEqual(item['low'], item['open'])
                        self.assertLessEqual(item['low'], item['close'])

    def test_generate_mock_intraday_data(self):
        """测试生成模拟日内数据函数"""
        for code in self.test_stock_codes:
            result = generate_mock_intraday_data(code)
            
            self.assertIsInstance(result, list)
            self.assertGreater(len(result), 0)
            
            # 检查每个数据点的格式
            for item in result:
                self.assertIn('time', item)
                self.assertIn('value', item)
                self.assertGreater(item['value'], 0)
                
            # 检查时间序列是否按顺序
            times = [item['time'] for item in result]
            self.assertEqual(times, sorted(times))

    @patch('akshare.stock_zh_a_hist')
    def test_get_stock_data(self, mock_hist):
        """测试获取股票历史数据函数"""
        # 模拟返回数据
        mock_data = pd.DataFrame({
            'date': pd.date_range(start='2023-01-01', periods=30),
            'open': np.random.rand(30) * 100,
            'high': np.random.rand(30) * 100,
            'low': np.random.rand(30) * 100,
            'close': np.random.rand(30) * 100,
            'volume': np.random.rand(30) * 1000000
        })
        mock_hist.return_value = mock_data

        result = get_stock_data('600000', '1mo', '1d')
        
        self.assertIsInstance(result, dict)
        self.assertIn('dates', result)
        self.assertIn('values', result)
        self.assertGreater(len(result['dates']), 0)
        self.assertEqual(len(result['dates']), len(result['values']))

    @patch('akshare.stock_zh_a_minute_em')
    def test_get_intraday_data(self, mock_minute):
        """测试获取日内数据函数"""
        # 模拟返回数据
        mock_data = pd.DataFrame({
            'datetime': pd.date_range(start='2023-01-01 09:30:00', periods=240, freq='1min'),
            'close': np.random.rand(240) * 100
        })
        mock_minute.return_value = mock_data

        result = get_intraday_data('600000')
        
        self.assertIsInstance(result, list)
        self.assertGreater(len(result), 0)
        
        # 检查数据格式
        for item in result:
            self.assertIn('time', item)
            self.assertIn('value', item)
            self.assertGreater(item['value'], 0)

if __name__ == '__main__':
    unittest.main()