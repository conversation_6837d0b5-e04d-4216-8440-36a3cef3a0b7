# 环境配置
# 开发环境使用development，生产环境使用production
ENVIRONMENT=development

# 数据库类型配置
# 开发环境使用sqlite，生产环境使用postgres
DB_TYPE=sqlite

# SQLite配置
# 数据库文件路径，相对于项目根目录
SQLITE_DB_PATH=data/sqlite.db

# PostgreSQL配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=stocks_insights
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 其他配置
APP_SECRET_KEY=your_secret_key
RETRIEVER_PATH=data/tsrag/bar_dataset_retriever

# 注意：BarGPT模型路径配置在config/config.json文件的BARGPT_MODELS部分

# 向量数据库配置
VECTOR_DB_DIRECTORY=data/vector_db
VECTOR_DB_COLLECTION=news_articles

# 模型配置
# 嵌入模型类型，可选值：openai或deepseek，默认为openai
EMBEDDING_MODEL=openai
# LLM模型类型，可选值：openai或deepseek，默认为openai
LLM_MODEL=openai
# DeepSeek API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key_here
# DeepSeek模型名称，默认为deepseek-chat
# 可选值：deepseek-chat, deepseek-v3, deepseek-v3-mini, deepseek-r1, deepseek-r1-mini
DEEPSEEK_MODEL_NAME=deepseek-chat

# MongoDB配置
MONGO_URI=mongodb://************:27017/
DATABASE_NAME=stocks_insights
COLLECTION_NAME=news_articles

# 股票数据表名
STOCK_TABLE=stock_data

# 用户活动日志配置
# 是否启用活动日志记录，默认为true
ACTIVITY_LOG_ENABLED=true
# 最低记录级别，可选值：HIGH（高）、MEDIUM（中）、LOW（低），默认为MEDIUM
ACTIVITY_LOG_MIN_LEVEL=MEDIUM
# 要排除的活动类型，多个类型用逗号分隔，例如：API_CALL,DATA_ACCESS
ACTIVITY_LOG_EXCLUDED_TYPES=

# API配置
# 是否启用所有API，无视配置文件中的设置，默认为false
# 注意：核心API（user、portfolio、favorite）始终启用，不受此设置影响
# 其他API的启用状态在config/config.json的API_SETTINGS.ENABLED_APIS中配置
ENABLE_ALL_APIS=false
