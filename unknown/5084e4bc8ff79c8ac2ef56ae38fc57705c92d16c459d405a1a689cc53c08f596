// 扩展技术指标模块

// 全局变量
let extendedIndicatorCharts = {};

// 初始化扩展技术指标
function initExtendedIndicators() {
    // 添加扩展技术指标按钮点击事件
    document.getElementById('showExtendedIndicatorsBtn').addEventListener('click', function() {
        const ticker = currentTicker;
        if (!ticker) {
            alert('请先搜索股票代码');
            return;
        }

        // 显示扩展技术指标面板
        const extendedIndicatorsPanel = document.getElementById('extendedIndicatorsPanel');
        extendedIndicatorsPanel.style.display = 'block';

        // 加载扩展技术指标
        loadExtendedIndicators(ticker);
    });

    // 关闭按钮点击事件
    document.getElementById('closeExtendedIndicatorsBtn').addEventListener('click', function() {
        document.getElementById('extendedIndicatorsPanel').style.display = 'none';
    });

    // 指标选择事件
    document.querySelectorAll('input[name="extendedIndicator"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked && currentTicker) {
                const indicator = this.value;
                updateExtendedIndicator(currentTicker, indicator);
            }
        });
    });
}

// 加载扩展技术指标
async function loadExtendedIndicators(ticker) {
    // 显示加载动画
    const chartContainer = document.getElementById('extendedIndicatorChart').parentNode;
    chartContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 获取当前选中的指标
        const selectedIndicator = document.querySelector('input[name="extendedIndicator"]:checked').value;

        // 更新指标图表
        await updateExtendedIndicator(ticker, selectedIndicator);
    } catch (error) {
        console.error('加载扩展技术指标失败:', error);
        chartContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">加载扩展技术指标失败: ${error.message}</p></div>`;
    }
}

// 更新扩展指标图表
async function updateExtendedIndicator(ticker, indicator) {
    // 显示加载动画
    const chartContainer = document.getElementById('extendedIndicatorChart').parentNode;
    chartContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 创建图表容器
        chartContainer.innerHTML = '<div id="extendedIndicatorChartContainer" style="width: 100%; height: 400px;"></div>';
        const container = document.getElementById('extendedIndicatorChartContainer');

        // 获取数据
        const data = await fetchExtendedIndicatorData(ticker, indicator);

        // 创建图表
        const chart = LightweightCharts.createChart(container, {
            width: container.clientWidth,
            height: container.clientHeight,
            layout: {
                backgroundColor: '#ffffff',
                textColor: '#333',
            },
            grid: {
                vertLines: {
                    color: 'rgba(197, 203, 206, 0.5)',
                },
                horzLines: {
                    color: 'rgba(197, 203, 206, 0.5)',
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
            },
            timeScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
                timeVisible: true,
                secondsVisible: false,
            },
            // 禁用鼠标滚轮缩放，避免影响页面滚动
            handleScroll: {
                mouseWheel: false,
            },
            // 禁用鼠标滚轮缩放
            handleScale: {
                mouseWheel: false,
            },
        });

        // 根据不同指标类型创建不同的图表
        switch (indicator) {
            case 'volume':
                createVolumeChart(chart, data);
                break;
            case 'obv':
                createOBVChart(chart, data);
                break;
            case 'atr':
                createATRChart(chart, data);
                break;
            case 'adx':
                createADXChart(chart, data);
                break;
            default:
                chartContainer.innerHTML = '<div class="text-center py-5"><p class="text-danger">未知的指标类型</p></div>';
                return;
        }

        // 调整时间刻度以适应所有数据
        chart.timeScale().fitContent();

        // 保存图表引用
        extendedIndicatorCharts[indicator] = chart;

        // 添加窗口大小变化监听器
        window.addEventListener('resize', function() {
            if (extendedIndicatorCharts[indicator]) {
                extendedIndicatorCharts[indicator].applyOptions({
                    width: container.clientWidth,
                    height: container.clientHeight
                });
            }
        });

    } catch (error) {
        console.error('更新扩展指标图表失败:', error);
        chartContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">更新扩展指标图表失败: ${error.message}</p></div>`;
    }
}

// 创建成交量图表
function createVolumeChart(chart, data) {
    // 添加价格线图系列
    const lineSeries = chart.addLineSeries({
        color: '#2962FF',
        lineWidth: 2,
        priceScaleId: 'right',
    });
    lineSeries.setData(data.prices);

    // 添加成交量柱状图系列
    const volumeSeries = chart.addHistogramSeries({
        color: '#26a69a',
        priceFormat: {
            type: 'volume',
        },
        priceScaleId: 'left',
        scaleMargins: {
            top: 0.7,
            bottom: 0,
        },
    });

    // 设置成交量数据
    volumeSeries.setData(data.volumes.map((volume, i) => ({
        time: data.prices[i].time,
        value: volume,
        color: data.prices[i].value > (i > 0 ? data.prices[i-1].value : data.prices[i].value) ? '#26a69a' : '#ef5350'
    })));
}

// 创建OBV图表
function createOBVChart(chart, data) {
    // 添加价格线图系列
    const lineSeries = chart.addLineSeries({
        color: '#2962FF',
        lineWidth: 2,
        priceScaleId: 'right',
    });
    lineSeries.setData(data.prices);

    // 添加OBV线图系列
    const obvSeries = chart.addLineSeries({
        color: '#FF9800',
        lineWidth: 2,
        priceScaleId: 'left',
        scaleMargins: {
            top: 0.7,
            bottom: 0,
        },
    });

    // 设置OBV数据
    obvSeries.setData(data.obv);
}

// 创建ATR图表
function createATRChart(chart, data) {
    // 添加区域图系列代替K线图
    const areaSeries = chart.addAreaSeries({
        lineColor: '#2962FF',
        topColor: 'rgba(41, 98, 255, 0.28)',
        bottomColor: 'rgba(41, 98, 255, 0.05)',
        lineWidth: 2,
        priceScaleId: 'right',
    });

    // 将蜡烛图数据转换为适合区域图的格式
    const areaData = data.candles.map(item => ({
        time: item.time,
        value: item.close,
    }));

    areaSeries.setData(areaData);

    // 添加ATR线图系列
    const atrSeries = chart.addLineSeries({
        color: '#7B1FA2',
        lineWidth: 2,
        priceScaleId: 'left',
        scaleMargins: {
            top: 0.7,
            bottom: 0,
        },
    });

    // 设置ATR数据
    atrSeries.setData(data.atr);
}

// 创建ADX图表
function createADXChart(chart, data) {
    // 添加区域图系列代替K线图
    const areaSeries = chart.addAreaSeries({
        lineColor: '#2962FF',
        topColor: 'rgba(41, 98, 255, 0.28)',
        bottomColor: 'rgba(41, 98, 255, 0.05)',
        lineWidth: 2,
        priceScaleId: 'right',
    });

    // 将蜡烛图数据转换为适合区域图的格式
    const areaData = data.candles.map(item => ({
        time: item.time,
        value: item.close,
    }));

    areaSeries.setData(areaData);

    // 添加ADX线图系列
    const adxSeries = chart.addLineSeries({
        color: '#7B1FA2',
        lineWidth: 2,
        priceScaleId: 'left',
        scaleMargins: {
            top: 0.8,
            bottom: 0.2,
        },
    });

    // 添加+DI线图系列
    const plusDISeries = chart.addLineSeries({
        color: '#26a69a',
        lineWidth: 1,
        priceScaleId: 'left',
        scaleMargins: {
            top: 0.8,
            bottom: 0.2,
        },
    });

    // 添加-DI线图系列
    const minusDISeries = chart.addLineSeries({
        color: '#ef5350',
        lineWidth: 1,
        priceScaleId: 'left',
        scaleMargins: {
            top: 0.8,
            bottom: 0.2,
        },
    });

    // 设置ADX数据
    adxSeries.setData(data.adx);
    plusDISeries.setData(data.plusDI);
    minusDISeries.setData(data.minusDI);
}

// 获取扩展指标数据
async function fetchExtendedIndicatorData(ticker, indicator) {
    try {
        // 构建API URL
        const url = `${API_BASE_URL}/stock/${ticker}/indicators?type=${indicator}`;

        // 调用API
        const response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const data = await response.json();

        // 处理数据
        return data;
    } catch (error) {
        console.error(`获取${ticker}扩展指标数据失败:`, error);

        // 使用模拟数据
        return generateMockExtendedIndicatorData(indicator);
    }
}

// 生成模拟扩展指标数据
function generateMockExtendedIndicatorData(indicator) {
    const dates = [];
    const now = new Date();
    now.setHours(0, 0, 0, 0);

    // 生成日期数据（过去30天）
    for (let i = 29; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(now.getDate() - i);
        dates.push(Math.floor(date.getTime() / 1000 / 86400));
    }

    // 生成基础价格数据
    let basePrice = 100 + Math.random() * 100;
    const prices = [];
    const candles = [];

    for (let i = 0; i < 30; i++) {
        // 模拟价格变动
        const prevPrice = basePrice;
        basePrice += (Math.random() - 0.5) * 5;

        // 添加价格数据点
        prices.push({
            time: dates[i],
            value: basePrice
        });

        // 模拟K线数据
        const open = prevPrice;
        const close = basePrice;
        const high = Math.max(open, close) + Math.random() * 2;
        const low = Math.min(open, close) - Math.random() * 2;

        candles.push({
            time: dates[i],
            open: open,
            high: high,
            low: low,
            close: close
        });
    }

    // 根据不同指标类型生成不同的数据
    switch (indicator) {
        case 'volume':
            return generateVolumeData(prices);
        case 'obv':
            return generateOBVData(prices);
        case 'atr':
            return generateATRData(candles);
        case 'adx':
            return generateADXData(candles);
        default:
            return {
                prices: prices,
                candles: candles
            };
    }
}

// 生成成交量数据
function generateVolumeData(prices) {
    const volumes = [];

    for (let i = 0; i < prices.length; i++) {
        // 模拟成交量
        const volume = Math.floor(Math.random() * 1000000) + 500000;
        volumes.push(volume);
    }

    return {
        prices: prices,
        volumes: volumes
    };
}

// 生成OBV数据
function generateOBVData(prices) {
    const obv = [];
    let obvValue = 10000000; // 初始OBV值

    for (let i = 0; i < prices.length; i++) {
        const time = prices[i].time;

        if (i > 0) {
            // 根据价格变化调整OBV
            const priceChange = prices[i].value - prices[i-1].value;
            const volume = Math.floor(Math.random() * 1000000) + 500000;

            if (priceChange > 0) {
                obvValue += volume;
            } else if (priceChange < 0) {
                obvValue -= volume;
            }
            // 如果价格不变，OBV不变
        }

        obv.push({
            time: time,
            value: obvValue
        });
    }

    return {
        prices: prices,
        obv: obv
    };
}

// 生成ATR数据
function generateATRData(candles) {
    const atr = [];
    const period = 14; // ATR周期

    for (let i = 0; i < candles.length; i++) {
        const time = candles[i].time;

        if (i >= period - 1) {
            // 计算过去14天的真实范围平均值
            let trSum = 0;

            for (let j = i - period + 1; j <= i; j++) {
                // 真实范围是以下三个值中的最大值
                const highLow = candles[j].high - candles[j].low;
                const highClosePrev = j > 0 ? Math.abs(candles[j].high - candles[j-1].close) : 0;
                const lowClosePrev = j > 0 ? Math.abs(candles[j].low - candles[j-1].close) : 0;

                const tr = Math.max(highLow, highClosePrev, lowClosePrev);
                trSum += tr;
            }

            const atrValue = trSum / period;

            atr.push({
                time: time,
                value: atrValue
            });
        } else if (i > 0) {
            // 对于不足14天的数据，使用简单平均
            let trSum = 0;

            for (let j = 0; j <= i; j++) {
                const highLow = candles[j].high - candles[j].low;
                const highClosePrev = j > 0 ? Math.abs(candles[j].high - candles[j-1].close) : 0;
                const lowClosePrev = j > 0 ? Math.abs(candles[j].low - candles[j-1].close) : 0;

                const tr = Math.max(highLow, highClosePrev, lowClosePrev);
                trSum += tr;
            }

            const atrValue = trSum / (i + 1);

            atr.push({
                time: time,
                value: atrValue
            });
        } else {
            // 第一天的ATR就是真实范围
            atr.push({
                time: time,
                value: candles[i].high - candles[i].low
            });
        }
    }

    return {
        candles: candles,
        atr: atr
    };
}

// 生成ADX数据
function generateADXData(candles) {
    const adx = [];
    const plusDI = [];
    const minusDI = [];
    const period = 14; // ADX周期

    for (let i = 0; i < candles.length; i++) {
        const time = candles[i].time;

        // 模拟ADX值（0-100之间）
        let adxValue;
        if (i < 5) {
            // 开始时ADX较低
            adxValue = 10 + Math.random() * 10;
        } else if (i < 15) {
            // 中间ADX上升
            adxValue = 20 + (i - 5) * 3 + Math.random() * 5;
        } else {
            // 后期ADX波动
            adxValue = 40 + Math.sin(i * 0.5) * 15 + Math.random() * 10;
        }

        // 模拟+DI和-DI
        let plusDIValue, minusDIValue;

        if (i < 10) {
            // 开始时+DI > -DI（上升趋势）
            plusDIValue = 25 + Math.random() * 10;
            minusDIValue = 15 + Math.random() * 5;
        } else if (i < 20) {
            // 中间+DI和-DI交叉（趋势转变）
            plusDIValue = 30 - (i - 10) * 1.5 + Math.random() * 5;
            minusDIValue = 15 + (i - 10) * 1.5 + Math.random() * 5;
        } else {
            // 后期-DI > +DI（下降趋势）
            plusDIValue = 15 + Math.random() * 5;
            minusDIValue = 25 + Math.random() * 10;
        }

        adx.push({
            time: time,
            value: adxValue
        });

        plusDI.push({
            time: time,
            value: plusDIValue
        });

        minusDI.push({
            time: time,
            value: minusDIValue
        });
    }

    return {
        candles: candles,
        adx: adx,
        plusDI: plusDI,
        minusDI: minusDI
    };
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否存在扩展技术指标面板
    if (document.getElementById('extendedIndicatorsPanel')) {
        initExtendedIndicators();
    }
});
