"""
Deep Learning Analyst agent for the AI Hedge Fund module.

This agent uses deep learning models to predict stock prices and trends.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json
import os

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_price_data
from ..ml.deep_learning import DeepLearningPredictor
from ..utils.llm import call_llm


class DeepLearningSignal(BaseModel):
    """Signal generated by the Deep Learning Analyst agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    predicted_price: float = Field(description="Predicted price")
    prediction_horizon: int = Field(description="Prediction horizon in days")
    model_type: str = Field(description="Type of deep learning model used")


def deep_learning_analyst_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using deep learning models.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with deep learning analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    dl_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("deep_learning_analyst_agent", ticker, "Fetching price data")
        
        # Get price data
        price_data = get_price_data(ticker, start_date, end_date)
        
        if price_data.empty:
            # Skip this ticker if no price data
            if "progress" in state["metadata"]:
                state["metadata"]["progress"].update_status("deep_learning_analyst_agent", ticker, "No price data available")
            
            dl_analysis[ticker] = {
                "signal": "neutral",
                "confidence": 0.0,
                "reasoning": "No price data available for deep learning analysis",
            }
            continue
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("deep_learning_analyst_agent", ticker, "Training deep learning model")
        
        # Create and train the model
        predictor = DeepLearningPredictor(model_type="lstm")
        
        # Check if a pre-trained model exists
        model_path = f"models/{ticker}_dl_model"
        if os.path.exists(model_path):
            # Load the model
            predictor.load_model(model_path)
        else:
            # Train the model
            training_result = predictor.train(
                df=price_data,
                target_column="close",
                sequence_length=60,  # 60-day lookback
                prediction_horizon=5,  # 5-day prediction
                test_size=0.2,
                epochs=50,
                batch_size=32,
                patience=10,
                model_path=model_path,
            )
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("deep_learning_analyst_agent", ticker, "Making predictions")
        
        # Make predictions
        prediction = predictor.predict(price_data)
        
        # Generate predictions plot
        predictions_plot = predictor.plot_predictions(
            df=price_data,
            title=f"{ticker} Deep Learning Price Predictions"
        )
        
        # Store analysis data
        analysis_data[ticker] = {
            "prediction": prediction,
            "predictions_plot": predictions_plot,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("deep_learning_analyst_agent", ticker, "Generating deep learning analysis")
        
        # Generate detailed analysis using LLM
        dl_output = generate_dl_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        dl_analysis[ticker] = {
            "signal": dl_output.signal,
            "confidence": dl_output.confidence,
            "reasoning": dl_output.reasoning,
            "predicted_price": dl_output.predicted_price,
            "prediction_horizon": dl_output.prediction_horizon,
            "model_type": dl_output.model_type,
            "predictions_plot": predictions_plot,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("deep_learning_analyst_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(dl_analysis, "Deep Learning Analyst Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["deep_learning_analyst_agent"] = dl_analysis
    
    return state


def generate_dl_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> DeepLearningSignal:
    """
    Generate detailed deep learning analysis using LLM.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        DeepLearningSignal object with the deep learning analysis
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Deep Learning Analyst AI agent. Analyze stocks using deep learning predictions.

    Key principles for deep learning analysis:
    - Model Predictions: Focus on the predicted price and direction
    - Confidence Assessment: Consider the model's confidence in its prediction
    - Time Horizon: Be clear about the prediction time horizon
    - Model Limitations: Acknowledge the limitations of deep learning predictions
    - Technical Context: Relate the prediction to technical indicators
    - Balanced View: Present both the prediction and potential risks

    When providing your reasoning, be thorough and specific by:
    1. Summarizing the deep learning model's prediction and confidence
    2. Explaining how the prediction relates to recent price action
    3. Providing specific price targets based on the prediction
    4. Discussing the model's historical accuracy and limitations
    5. Using a professional, analytical tone in your explanation

    For example, if bullish: "The LSTM deep learning model predicts a price increase of 3.2% over the next 5 days, with a target price of $157.43. The model has demonstrated strong directional accuracy in recent tests, correctly predicting 68% of price movements. The prediction is supported by the recent upward momentum and decreasing volatility..."
    For example, if bearish: "The deep learning model forecasts a 2.8% decline over the next 5 days, with a target price of $142.15. This bearish prediction is consistent with the recent price action showing resistance at the 50-day moving average. The model's confidence score of 75% suggests a relatively high probability for this downward movement..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following deep learning prediction data, create a trading signal for {ticker}:

    Analysis Data:
    {json.dumps(analysis_data[ticker]["prediction"], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string",
      "predicted_price": float,
      "prediction_horizon": int,
      "model_type": "string"
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_dl_signal():
        prediction_data = analysis_data.get(ticker, {}).get("prediction", {})
        
        if prediction_data.get("status") != "success":
            return DeepLearningSignal(
                signal="neutral",
                confidence=50.0,
                reasoning="Unable to generate deep learning prediction",
                predicted_price=0.0,
                prediction_horizon=5,
                model_type="lstm",
            )
        
        direction = prediction_data.get("direction", "neutral")
        confidence = prediction_data.get("confidence", 50.0)
        predicted_price = prediction_data.get("predicted_price", 0.0)
        prediction_horizon = prediction_data.get("prediction_horizon", 5)
        predicted_change = prediction_data.get("predicted_change_pct", 0.0)
        
        reasoning = f"Deep learning model predicts a {predicted_change:.2f}% change over the next {prediction_horizon} days."
        
        return DeepLearningSignal(
            signal=direction,
            confidence=confidence,
            reasoning=reasoning,
            predicted_price=predicted_price,
            prediction_horizon=prediction_horizon,
            model_type="lstm",
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=DeepLearningSignal,
        agent_name="deep_learning_analyst_agent",
        default_factory=create_default_dl_signal,
    )
