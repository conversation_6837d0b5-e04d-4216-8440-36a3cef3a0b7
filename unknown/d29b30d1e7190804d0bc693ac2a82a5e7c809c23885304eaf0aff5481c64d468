// 股票代码和名称数据
let stocksData = [];

// API基础URL
// 如果API_BASE_URL已经定义，就使用已有的值
// 否则定义一个新的
if (typeof API_BASE_URL === 'undefined') {
    window.API_BASE_URL = 'http://localhost:8080/api';
}

// 定义datahub API URL
const DATAHUB_API_BASE_URL = `${API_BASE_URL}/v1/datahub`;

// 从服务器获取股票数据
async function fetchStocksData() {
    try {
        console.log('开始获取股票数据...');

        // 首先尝试获取沪深300成分股
        let response = await fetch(`${DATAHUB_API_BASE_URL}/block_data?block_name=沪深300`);

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        let responseData = await response.json();
        console.log('收到沪深300数据响应:', responseData);

        // 处理API响应格式
        let data = [];
        if (responseData && responseData.data && Array.isArray(responseData.data)) {
            data = responseData.data.map(item => ({
                symbol: item.symbol,
                name: item.name
            }));
        } else if (Array.isArray(responseData)) {
            data = responseData.map(item => ({
                symbol: item.symbol,
                name: item.name
            }));
        }

        console.log('处理后的沪深300数据:', data);

        // 如果数据为空，尝试获取中证500成分股
        if (!data || data.length === 0) {
            response = await fetch(`${DATAHUB_API_BASE_URL}/block_data?block_name=中证500`);

            if (!response.ok) {
                throw new Error(`API错误: ${response.status}`);
            }

            responseData = await response.json();
            console.log('收到中证500数据响应:', responseData);

            // 处理API响应格式
            if (responseData && responseData.data && Array.isArray(responseData.data)) {
                data = responseData.data.map(item => ({
                    symbol: item.symbol,
                    name: item.name
                }));
            } else if (Array.isArray(responseData)) {
                data = responseData.map(item => ({
                    symbol: item.symbol,
                    name: item.name
                }));
            }

            console.log('处理后的中证500数据:', data);
        }

        // 如果数据仍然为空，尝试获取期货主力合约
        response = await fetch(`${DATAHUB_API_BASE_URL}/block_data?block_name=ZLQH`);

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        responseData = await response.json();
        console.log('收到期货主力合约数据响应:', responseData);

        // 处理API响应格式
        if (responseData && responseData.data && Array.isArray(responseData.data)) {
            data = responseData.data.map(item => ({
                symbol: item.symbol,
                name: item.name
            }));
        } else if (Array.isArray(responseData)) {
            data = responseData.map(item => ({
                symbol: item.symbol,
                name: item.name
            }));
        }

        console.log('处理后的期货主力合约数据:', data); 

        // 更新stocksData数组
        if (data && data.length > 0) {
            stocksData = data;
            console.log(`成功获取到${data.length}支股票数据`);
        } else {
            // 如果所有API调用都失败，使用默认数据
            console.warn('无法从服务器获取股票数据，使用默认数据');
            stocksData = [
                { symbol: 'IF9999.SF', name: '沪深300指数期货' },
                { symbol: 'IC9999.SF', name: '中证500指数期货' },
                { symbol: 'IH9999.SF', name: '上证50指数期货' },
                { symbol: '600000.SH', name: '浦发银行' },
                { symbol: '000001.SZ', name: '平安银行' }
            ];
        }
    } catch (error) {
        console.error('获取股票数据失败:', error);
        // 使用默认数据
        stocksData = [
            { symbol: 'IF9999.SF', name: '沪深300指数期货' },
            { symbol: 'IC9999.SF', name: '中证500指数期货' },
            { symbol: 'IH9999.SF', name: '上证50指数期货' },
            { symbol: '600000.SH', name: '浦发银行' },
            { symbol: '000001.SZ', name: '平安银行' }
        ];
    }
}

// 初始化自动完成功能
function initAutocomplete() {
    const searchInput = document.getElementById('searchTicker');

    // 如果找不到搜索输入框，则直接返回
    if (!searchInput) {
        console.warn('找不到ID为searchTicker的输入框，自动完成功能将不可用');
        return;
    }

    // 创建自动完成容器
    const autocompleteContainer = document.createElement('div');
    autocompleteContainer.className = 'autocomplete-items';
    autocompleteContainer.style.display = 'none';

    // 设置容器的定位，确保它正确显示在输入框下方
    const inputParent = searchInput.parentNode;
    inputParent.style.position = 'relative';
    inputParent.appendChild(autocompleteContainer);

    // 输入事件监听
    searchInput.addEventListener('input', function() {
        const value = this.value.trim().toUpperCase();

        // 清空自动完成容器
        autocompleteContainer.innerHTML = '';

        if (!value) {
            autocompleteContainer.style.display = 'none';
            return;
        }

        // 过滤匹配的股票
        const matches = stocksData.filter(stock =>
            stock.symbol.includes(value) ||
            stock.name.includes(value)
        ).slice(0, 5); // 最多显示5个结果

        if (matches.length > 0) {
            autocompleteContainer.style.display = 'block';

            // 创建匹配项
            matches.forEach(stock => {
                const item = document.createElement('div');
                item.className = 'autocomplete-item';

                // 高亮匹配文本
                const symbolText = highlightMatch(stock.symbol, value);
                const nameText = highlightMatch(stock.name, value);

                item.innerHTML = `<strong>${symbolText}</strong> - ${nameText}`;

                // 点击事件
                item.addEventListener('click', function() {
                    searchInput.value = stock.symbol;
                    autocompleteContainer.style.display = 'none';

                    // 触发搜索
                    document.getElementById('searchBtn').click();
                });

                autocompleteContainer.appendChild(item);
            });
        } else {
            autocompleteContainer.style.display = 'none';
        }
    });

    // 点击文档其他地方时隐藏自动完成
    document.addEventListener('click', function(e) {
        if (e.target !== searchInput) {
            autocompleteContainer.style.display = 'none';
        }
    });

    // 键盘导航
    searchInput.addEventListener('keydown', function(e) {
        const items = autocompleteContainer.getElementsByClassName('autocomplete-item');
        if (!items.length) return;

        // 找到当前选中项
        let activeIndex = -1;
        for (let i = 0; i < items.length; i++) {
            if (items[i].classList.contains('active')) {
                activeIndex = i;
                break;
            }
        }

        // 向下箭头
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            activeIndex = (activeIndex + 1) % items.length;
            setActiveItem(items, activeIndex);
        }
        // 向上箭头
        else if (e.key === 'ArrowUp') {
            e.preventDefault();
            activeIndex = activeIndex <= 0 ? items.length - 1 : activeIndex - 1;
            setActiveItem(items, activeIndex);
        }
        // 回车键
        else if (e.key === 'Enter' && activeIndex > -1) {
            e.preventDefault();
            items[activeIndex].click();
        }
    });
}

// 设置活动项
function setActiveItem(items, index) {
    for (let i = 0; i < items.length; i++) {
        items[i].classList.remove('active');
    }
    items[index].classList.add('active');
}

// 高亮匹配文本
function highlightMatch(text, query) {
    const index = text.toUpperCase().indexOf(query.toUpperCase());
    if (index >= 0) {
        return text.substring(0, index) +
               '<span class="highlight">' + text.substring(index, index + query.length) + '</span>' +
               text.substring(index + query.length);
    }
    return text;
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 首先从服务器获取股票数据
    await fetchStocksData();
    // 然后初始化自动完成功能
    initAutocomplete();
});
