"""
Alternative Data Analyst agent for the AI Hedge Fund module.

This agent analyzes stocks using alternative data sources like social media sentiment and news sentiment.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json
import pandas as pd

from ..graph.state import AgentState, show_agent_reasoning
from ..alternative_data.social_sentiment import get_social_sentiment, analyze_social_sentiment, get_historical_sentiment
from ..alternative_data.news_analysis import get_news_sentiment, analyze_news_trends
from ..utils.llm import call_llm


class AlternativeDataSignal(BaseModel):
    """Signal generated by the Alternative Data Analyst agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    social_sentiment: Dict[str, Any] = Field(description="Social media sentiment analysis")
    news_sentiment: Dict[str, Any] = Field(description="News sentiment analysis")
    key_insights: List[str] = Field(description="Key insights from alternative data")


def alternative_data_analyst_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using alternative data sources.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with alternative data analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    alt_data_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("alternative_data_analyst_agent", ticker, "Fetching social sentiment")
        
        # Get social sentiment data
        social_sentiment = get_social_sentiment(ticker, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("alternative_data_analyst_agent", ticker, "Analyzing social sentiment")
        
        # Analyze social sentiment
        social_analysis = analyze_social_sentiment(social_sentiment) if social_sentiment else {"sentiment_signal": "neutral", "confidence": 0.0}
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("alternative_data_analyst_agent", ticker, "Fetching historical sentiment")
        
        # Get historical social sentiment
        historical_sentiment = get_historical_sentiment(ticker, start_date, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("alternative_data_analyst_agent", ticker, "Analyzing news trends")
        
        # Analyze news trends
        news_analysis = analyze_news_trends(ticker, start_date, end_date)
        
        # Store analysis data
        analysis_data[ticker] = {
            "social_sentiment": social_analysis,
            "historical_sentiment": historical_sentiment.reset_index().to_dict(orient="records") if not historical_sentiment.empty else [],
            "news_analysis": news_analysis,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("alternative_data_analyst_agent", ticker, "Generating alternative data analysis")
        
        # Generate detailed analysis using LLM
        alt_data_output = generate_alternative_data_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        alt_data_analysis[ticker] = {
            "signal": alt_data_output.signal,
            "confidence": alt_data_output.confidence,
            "reasoning": alt_data_output.reasoning,
            "social_sentiment": alt_data_output.social_sentiment,
            "news_sentiment": alt_data_output.news_sentiment,
            "key_insights": alt_data_output.key_insights,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("alternative_data_analyst_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(alt_data_analysis, "Alternative Data Analyst Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["alternative_data_analyst_agent"] = alt_data_analysis
    
    return state


def generate_alternative_data_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> AlternativeDataSignal:
    """
    Generate detailed alternative data analysis using LLM.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        AlternativeDataSignal object with the alternative data analysis
    """
    # Create a system prompt for the LLM
    system_prompt = """You are an Alternative Data Analyst AI agent. Analyze stocks using alternative data sources like social media sentiment and news sentiment.

    Key principles for alternative data analysis:
    - Social Media Sentiment: Evaluate sentiment and activity on social platforms
    - News Sentiment: Analyze sentiment and volume of news coverage
    - Trend Analysis: Identify changes in sentiment and activity over time
    - Contrarian Signals: Look for potential contrarian signals when sentiment is extreme
    - Volume Analysis: Consider the volume of mentions and news articles
    - Topic Analysis: Identify key topics and themes in social media and news
    - Cross-Validation: Compare signals across different alternative data sources

    When providing your reasoning, be thorough and specific by:
    1. Summarizing the overall alternative data signals and their implications
    2. Highlighting the most significant insights from social media and news
    3. Explaining how alternative data signals might impact stock performance
    4. Identifying any divergence between alternative data and traditional metrics
    5. Using a professional, analytical tone in your explanation

    For example, if bullish: "Social media sentiment is strongly positive with a 72% bullish bias and increasing mention volume, indicating growing retail interest. News sentiment has improved from negative to neutral over the past week, with coverage focusing on the company's new product launches and expansion plans..."
    For example, if bearish: "News sentiment has deteriorated significantly with a -0.65 sentiment score, driven by negative coverage of the company's regulatory challenges. Social media activity shows a concerning 3:1 ratio of bearish to bullish comments, with trending topics focused on competitive threats and management issues..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following alternative data, create a trading signal for {ticker}:

    Analysis Data:
    {json.dumps(analysis_data[ticker], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string",
      "social_sentiment": {{
        "signal": "string",
        "score": float,
        "trend": "string"
      }},
      "news_sentiment": {{
        "signal": "string",
        "score": float,
        "trend": "string"
      }},
      "key_insights": ["string", "string", ...]
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_alternative_data_signal():
        ticker_data = analysis_data.get(ticker, {})
        social_data = ticker_data.get("social_sentiment", {})
        news_data = ticker_data.get("news_analysis", {})
        
        # Determine signal based on social and news sentiment
        social_signal = social_data.get("sentiment_signal", "neutral")
        news_signal = news_data.get("signal", "neutral")
        
        if social_signal == "bullish" and news_signal in ["bullish", "slightly bullish"]:
            signal = "bullish"
            confidence = min(70.0, (social_data.get("confidence", 50.0) + news_data.get("confidence", 50.0)) / 2)
        elif social_signal == "bearish" and news_signal in ["bearish", "slightly bearish"]:
            signal = "bearish"
            confidence = min(70.0, (social_data.get("confidence", 50.0) + news_data.get("confidence", 50.0)) / 2)
        elif social_signal == "bullish" or news_signal in ["bullish", "slightly bullish"]:
            signal = "neutral"
            confidence = 60.0
        elif social_signal == "bearish" or news_signal in ["bearish", "slightly bearish"]:
            signal = "neutral"
            confidence = 60.0
        else:
            signal = "neutral"
            confidence = 50.0
        
        # Generate reasoning
        reasoning = f"Analysis based on social media sentiment ({social_signal}) and news sentiment ({news_signal})."
        
        # Extract key insights
        key_insights = social_data.get("key_insights", [])
        if news_data.get("summary"):
            key_insights.append(news_data.get("summary"))
        
        return AlternativeDataSignal(
            signal=signal,
            confidence=confidence,
            reasoning=reasoning,
            social_sentiment={
                "signal": social_signal,
                "score": social_data.get("sentiment_score", 0.0),
                "trend": "stable",
            },
            news_sentiment={
                "signal": news_signal,
                "score": news_data.get("avg_sentiment", 0.0),
                "trend": news_data.get("trend_description", "stable"),
            },
            key_insights=key_insights[:3] if key_insights else ["No significant insights from alternative data"],
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=AlternativeDataSignal,
        agent_name="alternative_data_analyst_agent",
        default_factory=create_default_alternative_data_signal,
    )
