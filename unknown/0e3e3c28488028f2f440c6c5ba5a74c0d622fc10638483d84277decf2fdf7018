"""
投资代理运行入口点
"""
import sys
import os

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 将当前目录添加到Python路径
sys.path.insert(0, current_dir)
print(current_dir)

# 将项目根目录添加到Python路径
sys.path.append(os.path.abspath(os.path.join(current_dir, '..')))

# 直接导入invest_agent.py
from invest_agent import QuantAlphaSystem

def main():
    """主函数"""
    print("启动量化投资代理系统...")

    # 创建系统实例
    system = QuantAlphaSystem()

    # 执行分析
    ticker = "000001.SH"
    print(f"分析股票: {ticker}")

    try:
        results = system.run_full_analysis(ticker)

        # 输出结果
        print("\n分析结果:")
        for key, value in results.items():
            print(f"{key}: {type(value)}")

        print("\n分析完成!")
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
