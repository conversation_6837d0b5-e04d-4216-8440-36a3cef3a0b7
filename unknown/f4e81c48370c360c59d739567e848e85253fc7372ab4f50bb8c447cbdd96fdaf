/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
}

/* 卡片样式 */
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 500;
}

/* 股票图表样式 */
#stockChart {
    width: 100%;
    height: 300px;
}

/* 股票详情样式 */
.stock-detail-item {
    padding: 10px;
    border-radius: 5px;
    background-color: #f8f9fa;
    margin-bottom: 10px;
}

.stock-detail-label {
    font-weight: bold;
    color: #6c757d;
}

.stock-detail-value {
    font-weight: 500;
}

.stock-detail-value.positive {
    color: #28a745;
}

.stock-detail-value.negative {
    color: #dc3545;
}

/* 新闻列表样式 */
.news-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.news-item:hover {
    background-color: #f8f9fa;
}

.news-item:last-child {
    border-bottom: none;
}

.news-date {
    color: #6c757d;
    font-size: 0.85rem;
}

.news-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.news-source {
    font-size: 0.85rem;
    color: #6c757d;
}

/* 按钮样式 */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header .btn-group,
    .card-header .dropdown {
        margin-top: 10px;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid rgba(0, 0, 0, 0.1);
    border-right-color: #0d6efd;
    border-radius: 50%;
    animation: spinner 0.75s linear infinite;
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

/* 用户登录后的样式 */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #0d6efd;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 8px;
}

.user-dropdown {
    cursor: pointer;
}

/* 自动完成样式 */
.autocomplete-items {
    position: absolute;
    border: 1px solid #ddd;
    border-top: none;
    z-index: 99;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 300px;
    overflow-y: auto;
    background-color: #fff;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.autocomplete-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #f1f1f1;
}

.autocomplete-item:last-child {
    border-bottom: none;
}

.autocomplete-item:hover,
.autocomplete-item.active {
    background-color: #e9f0ff;
}

.highlight {
    background-color: #ffeb3b;
    font-weight: bold;
}

/* 股票比较样式 */
.comparison-stock-item {
    transition: background-color 0.2s;
}

.comparison-stock-item:hover {
    background-color: #f8f9fa;
}

.comparison-color-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

/* 高级图表样式 */
#advancedChartContainer {
    position: relative;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
}

.chart-legend {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-tooltip {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    z-index: 100;
    display: none;
}

/* 预测模型页面样式 */
#predictionChart {
    width: 100%;
    height: 400px;
    background-color: #fff;
    border-radius: 4px;
}

.legend-marker {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.evaluation-item {
    padding: 10px;
    border-radius: 5px;
    background-color: #f8f9fa;
    margin-bottom: 10px;
}

.evaluation-label {
    font-weight: bold;
    color: #6c757d;
    font-size: 0.9rem;
}

.evaluation-value {
    font-weight: 500;
    font-size: 1.1rem;
}

.model-explanation {
    line-height: 1.6;
    text-align: justify;
}

/* 表格样式 */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* 时序检索页面样式 */
.stat-item {
    padding: 10px;
    border-radius: 5px;
    background-color: #f8f9fa;
    margin-bottom: 10px;
}

.stat-label {
    font-weight: bold;
    color: #6c757d;
    font-size: 0.9rem;
}

.stat-value {
    font-weight: 500;
    font-size: 1.1rem;
}

/* 自选股样式 */
.favorites-section {
    margin-bottom: 20px;
}

.favorites-section .nav-tabs {
    border-bottom: 1px solid #dee2e6;
}

.favorites-section .nav-tabs .nav-link {
    margin-bottom: -1px;
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}

.favorites-section .nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
}

.favorites-section .nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.favorites-section .tab-content {
    padding-top: 15px;
}

.favorites-section .table {
    margin-bottom: 0;
}

.favorites-section .table th {
    border-top: none;
}

.favorites-section .stock-link {
    color: #0d6efd;
    text-decoration: none;
}

.favorites-section .stock-link:hover {
    text-decoration: underline;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid rgba(0, 0, 0, 0.1);
    border-right-color: #007bff;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

/* 订阅服务样式 */
.subscription-plan-card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.subscription-plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.subscription-plan-card .card-header {
    font-weight: bold;
    text-align: center;
    padding: 15px;
}

.subscription-plan-card .price {
    font-size: 2rem;
    font-weight: bold;
    text-align: center;
    margin: 15px 0;
}

.subscription-plan-card .price small {
    font-size: 1rem;
    font-weight: normal;
}

.subscription-plan-card .feature-list {
    margin: 20px 0;
}

.subscription-plan-card .feature-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f1f1;
}

.subscription-plan-card .feature-list li:last-child {
    border-bottom: none;
}

.subscription-plan-card .feature-included {
    color: #28a745;
}

.subscription-plan-card .feature-excluded {
    color: #dc3545;
    text-decoration: line-through;
    opacity: 0.7;
}

/* 当前订阅样式 */
.current-subscription {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
}

.current-subscription .progress {
    height: 8px;
    margin: 10px 0;
}

.subscription-badge {
    font-size: 0.8rem;
    padding: 3px 8px;
    border-radius: 10px;
    margin-left: 5px;
}
