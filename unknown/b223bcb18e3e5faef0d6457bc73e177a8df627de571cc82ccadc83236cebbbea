"""
<PERSON> agent for the AI Hedge Fund module.

This agent analyzes stocks using <PERSON>'s quantitative approach,
focusing on statistical patterns, market inefficiencies, and mathematical models.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json
import numpy as np
import pandas as pd
from scipy import stats

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_price_data
from ..utils.llm import call_llm


class SimonsSignal(BaseModel):
    """Signal generated by the <PERSON> agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    statistical_patterns: Dict[str, Any] = Field(description="Identified statistical patterns")
    market_inefficiencies: Dict[str, Any] = Field(description="Identified market inefficiencies")
    time_horizon: str = Field(description="Recommended time horizon for the trade")


def jim_simons_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using <PERSON>'s quantitative approach and LLM reasoning.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with <PERSON>'s analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    simons_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("jim_simons_agent", ticker, "Fetching price data")
        
        # Get price data
        price_data = get_price_data(ticker, start_date, end_date)
        
        if price_data.empty:
            # Skip this ticker if no price data
            if "progress" in state["metadata"]:
                state["metadata"]["progress"].update_status("jim_simons_agent", ticker, "No price data available")
            
            simons_analysis[ticker] = {
                "signal": "neutral",
                "confidence": 0.0,
                "reasoning": "No price data available for analysis",
            }
            continue
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("jim_simons_agent", ticker, "Analyzing statistical patterns")
        
        # Analyze statistical patterns
        statistical_patterns = analyze_statistical_patterns(price_data)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("jim_simons_agent", ticker, "Analyzing market inefficiencies")
        
        # Analyze market inefficiencies
        market_inefficiencies = analyze_market_inefficiencies(price_data)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("jim_simons_agent", ticker, "Analyzing mean reversion")
        
        # Analyze mean reversion
        mean_reversion = analyze_mean_reversion(price_data)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("jim_simons_agent", ticker, "Analyzing momentum factors")
        
        # Analyze momentum factors
        momentum_factors = analyze_momentum_factors(price_data)
        
        # Calculate total score
        total_score = (
            statistical_patterns["score"] + 
            market_inefficiencies["score"] + 
            mean_reversion["score"] + 
            momentum_factors["score"]
        )
        
        max_possible_score = (
            statistical_patterns["max_score"] + 
            market_inefficiencies["max_score"] + 
            mean_reversion["max_score"] + 
            momentum_factors["max_score"]
        )
        
        # Generate trading signal based on score
        if total_score >= 0.6 * max_possible_score:
            signal = "bullish"
        elif total_score <= -0.2 * max_possible_score:
            signal = "bearish"
        else:
            signal = "neutral"
        
        # Combine all analysis results
        analysis_data[ticker] = {
            "signal": signal,
            "score": total_score,
            "max_score": max_possible_score,
            "statistical_patterns": statistical_patterns,
            "market_inefficiencies": market_inefficiencies,
            "mean_reversion": mean_reversion,
            "momentum_factors": momentum_factors,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("jim_simons_agent", ticker, "Generating Simons analysis")
        
        # Generate detailed analysis using LLM
        simons_output = generate_simons_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        simons_analysis[ticker] = {
            "signal": simons_output.signal,
            "confidence": simons_output.confidence,
            "reasoning": simons_output.reasoning,
            "statistical_patterns": simons_output.statistical_patterns,
            "market_inefficiencies": simons_output.market_inefficiencies,
            "time_horizon": simons_output.time_horizon,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("jim_simons_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(simons_analysis, "Jim Simons Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["jim_simons_agent"] = simons_analysis
    
    return state


def analyze_statistical_patterns(price_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze statistical patterns in price data.
    
    Args:
        price_data: DataFrame with price data
        
    Returns:
        Dictionary with statistical pattern analysis results
    """
    score = 0
    max_score = 3
    reasoning = []
    patterns = {}
    
    # Check if we have enough data
    if price_data.empty or len(price_data) < 30:
        return {
            "score": 0,
            "max_score": max_score,
            "details": "Insufficient data for statistical pattern analysis",
            "patterns": {},
        }
    
    # Calculate returns
    price_data["returns"] = price_data["close"].pct_change()
    
    # Remove NaN values
    returns = price_data["returns"].dropna().values
    
    # 1. Check for normality of returns
    if len(returns) >= 30:
        _, p_value = stats.normaltest(returns)
        patterns["normality_p_value"] = float(p_value)
        
        if p_value < 0.05:
            reasoning.append(f"Non-normal return distribution detected (p-value: {p_value:.4f})")
            score += 1  # Non-normal distributions may indicate exploitable patterns
        else:
            reasoning.append(f"Returns appear normally distributed (p-value: {p_value:.4f})")
    
    # 2. Check for autocorrelation in returns
    if len(returns) >= 30:
        # Calculate autocorrelation for lags 1-5
        autocorr = []
        significant_autocorr = False
        
        for lag in range(1, 6):
            if len(returns) > lag:
                ac = float(pd.Series(returns).autocorr(lag=lag))
                autocorr.append(ac)
                
                # Check if autocorrelation is statistically significant
                # Approximate standard error of autocorrelation
                se = 1 / np.sqrt(len(returns))
                if abs(ac) > 2 * se:  # 95% confidence level
                    significant_autocorr = True
                    reasoning.append(f"Significant autocorrelation at lag {lag}: {ac:.4f}")
        
        patterns["autocorrelation"] = autocorr
        
        if significant_autocorr:
            score += 1
            reasoning.append("Exploitable autocorrelation patterns detected")
    
    # 3. Check for volatility clustering (ARCH effects)
    if len(returns) >= 50:
        # Calculate squared returns
        squared_returns = returns ** 2
        
        # Check autocorrelation of squared returns
        sq_autocorr = float(pd.Series(squared_returns).autocorr(lag=1))
        patterns["volatility_clustering"] = sq_autocorr
        
        if abs(sq_autocorr) > 0.1:
            score += 1
            reasoning.append(f"Volatility clustering detected (autocorr of squared returns: {sq_autocorr:.4f})")
    
    # 4. Check for seasonality
    if len(price_data) >= 252:  # At least 1 year of data
        # Extract day of week
        price_data["day_of_week"] = pd.to_datetime(price_data.index).dayofweek
        
        # Calculate average returns by day of week
        day_returns = {}
        for day in range(5):  # 0=Monday, 4=Friday
            day_data = price_data[price_data["day_of_week"] == day]
            if not day_data.empty:
                avg_return = day_data["returns"].mean()
                day_returns[day] = float(avg_return)
        
        patterns["day_of_week_returns"] = day_returns
        
        # Check if any day has significantly different returns
        if day_returns:
            max_day = max(day_returns, key=day_returns.get)
            min_day = min(day_returns, key=day_returns.get)
            
            if day_returns[max_day] > 0 and day_returns[max_day] > 2 * np.mean(list(day_returns.values())):
                reasoning.append(f"Day-of-week effect detected: {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'][max_day]} shows abnormally high returns")
                score += 0.5
            
            if day_returns[min_day] < 0 and day_returns[min_day] < 2 * np.mean(list(day_returns.values())):
                reasoning.append(f"Day-of-week effect detected: {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'][min_day]} shows abnormally low returns")
                score += 0.5
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
        "patterns": patterns,
    }


def analyze_market_inefficiencies(price_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze market inefficiencies in price data.
    
    Args:
        price_data: DataFrame with price data
        
    Returns:
        Dictionary with market inefficiency analysis results
    """
    score = 0
    max_score = 3
    reasoning = []
    inefficiencies = {}
    
    # Check if we have enough data
    if price_data.empty or len(price_data) < 30:
        return {
            "score": 0,
            "max_score": max_score,
            "details": "Insufficient data for market inefficiency analysis",
            "inefficiencies": {},
        }
    
    # Calculate returns
    price_data["returns"] = price_data["close"].pct_change()
    
    # Remove NaN values
    returns = price_data["returns"].dropna().values
    
    # 1. Check for fat tails (excess kurtosis)
    if len(returns) >= 30:
        kurtosis = float(stats.kurtosis(returns))
        inefficiencies["excess_kurtosis"] = kurtosis
        
        if kurtosis > 1:
            score += 1
            reasoning.append(f"Fat tails detected (excess kurtosis: {kurtosis:.4f})")
    
    # 2. Check for skewness
    if len(returns) >= 30:
        skewness = float(stats.skew(returns))
        inefficiencies["skewness"] = skewness
        
        if abs(skewness) > 0.5:
            score += 0.5
            reasoning.append(f"Significant skewness detected ({skewness:.4f})")
    
    # 3. Check for price gaps
    if len(price_data) >= 20:
        # Calculate overnight gaps
        price_data["prev_close"] = price_data["close"].shift(1)
        price_data["gap"] = (price_data["open"] - price_data["prev_close"]) / price_data["prev_close"]
        
        # Identify significant gaps (>1%)
        significant_gaps = price_data[abs(price_data["gap"]) > 0.01].copy()
        
        if not significant_gaps.empty:
            # Calculate gap fill statistics
            gap_count = len(significant_gaps)
            
            # A gap is considered filled if the price returns to the pre-gap level within 5 days
            filled_gaps = 0
            for idx in significant_gaps.index:
                i = price_data.index.get_loc(idx)
                if i + 5 < len(price_data):
                    gap = significant_gaps.loc[idx, "gap"]
                    pre_gap_price = significant_gaps.loc[idx, "prev_close"]
                    
                    # Check next 5 days
                    for j in range(1, 6):
                        if (gap > 0 and price_data.iloc[i + j]["low"] <= pre_gap_price) or \
                           (gap < 0 and price_data.iloc[i + j]["high"] >= pre_gap_price):
                            filled_gaps += 1
                            break
            
            if gap_count > 0:
                fill_rate = filled_gaps / gap_count
                inefficiencies["gap_fill_rate"] = float(fill_rate)
                
                if fill_rate > 0.6:
                    score += 1
                    reasoning.append(f"Exploitable gap fill pattern detected ({fill_rate:.2%} of gaps fill within 5 days)")
    
    # 4. Check for mean reversion in short-term extreme moves
    if len(price_data) >= 60:
        # Identify days with extreme returns (>2 standard deviations)
        std_dev = np.std(returns)
        extreme_up_days = price_data[price_data["returns"] > 2 * std_dev].copy()
        extreme_down_days = price_data[price_data["returns"] < -2 * std_dev].copy()
        
        # Calculate next-day returns after extreme moves
        next_day_after_up = []
        next_day_after_down = []
        
        for idx in extreme_up_days.index:
            i = price_data.index.get_loc(idx)
            if i + 1 < len(price_data):
                next_day_after_up.append(price_data.iloc[i + 1]["returns"])
        
        for idx in extreme_down_days.index:
            i = price_data.index.get_loc(idx)
            if i + 1 < len(price_data):
                next_day_after_down.append(price_data.iloc[i + 1]["returns"])
        
        if next_day_after_up:
            avg_return_after_up = np.mean(next_day_after_up)
            inefficiencies["avg_return_after_extreme_up"] = float(avg_return_after_up)
            
            if avg_return_after_up < -0.001:  # Mean reversion after up days
                score += 0.5
                reasoning.append(f"Mean reversion after extreme up days detected (avg next-day return: {avg_return_after_up:.2%})")
        
        if next_day_after_down:
            avg_return_after_down = np.mean(next_day_after_down)
            inefficiencies["avg_return_after_extreme_down"] = float(avg_return_after_down)
            
            if avg_return_after_down > 0.001:  # Mean reversion after down days
                score += 0.5
                reasoning.append(f"Mean reversion after extreme down days detected (avg next-day return: {avg_return_after_down:.2%})")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
        "inefficiencies": inefficiencies,
    }


def analyze_mean_reversion(price_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze mean reversion patterns in price data.
    
    Args:
        price_data: DataFrame with price data
        
    Returns:
        Dictionary with mean reversion analysis results
    """
    score = 0
    max_score = 2
    reasoning = []
    reversion_metrics = {}
    
    # Check if we have enough data
    if price_data.empty or len(price_data) < 30:
        return {
            "score": 0,
            "max_score": max_score,
            "details": "Insufficient data for mean reversion analysis",
            "reversion_metrics": {},
        }
    
    # Calculate returns
    price_data["returns"] = price_data["close"].pct_change()
    
    # 1. Calculate RSI
    if len(price_data) >= 14:
        # Calculate RSI (14-day)
        delta = price_data["close"].diff()
        gain = delta.where(delta > 0, 0).fillna(0)
        loss = -delta.where(delta < 0, 0).fillna(0)
        
        avg_gain = gain.rolling(window=14).mean()
        avg_loss = loss.rolling(window=14).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        current_rsi = float(rsi.iloc[-1])
        reversion_metrics["rsi"] = current_rsi
        
        # Check for overbought/oversold conditions
        if current_rsi > 70:
            score -= 1
            reasoning.append(f"Overbought condition detected (RSI: {current_rsi:.2f})")
        elif current_rsi < 30:
            score += 1
            reasoning.append(f"Oversold condition detected (RSI: {current_rsi:.2f})")
    
    # 2. Calculate distance from moving averages
    if len(price_data) >= 50:
        # Calculate 20-day and 50-day moving averages
        ma20 = price_data["close"].rolling(window=20).mean()
        ma50 = price_data["close"].rolling(window=50).mean()
        
        # Calculate distance from moving averages
        current_price = price_data["close"].iloc[-1]
        distance_from_ma20 = (current_price / ma20.iloc[-1] - 1) * 100
        distance_from_ma50 = (current_price / ma50.iloc[-1] - 1) * 100
        
        reversion_metrics["distance_from_ma20"] = float(distance_from_ma20)
        reversion_metrics["distance_from_ma50"] = float(distance_from_ma50)
        
        # Check for extreme deviations
        if distance_from_ma20 < -10:
            score += 1
            reasoning.append(f"Price significantly below 20-day MA ({distance_from_ma20:.2f}%)")
        elif distance_from_ma20 > 10:
            score -= 1
            reasoning.append(f"Price significantly above 20-day MA ({distance_from_ma20:.2f}%)")
        
        if distance_from_ma50 < -15:
            score += 1
            reasoning.append(f"Price significantly below 50-day MA ({distance_from_ma50:.2f}%)")
        elif distance_from_ma50 > 15:
            score -= 1
            reasoning.append(f"Price significantly above 50-day MA ({distance_from_ma50:.2f}%)")
    
    # 3. Calculate Bollinger Bands
    if len(price_data) >= 20:
        # Calculate 20-day Bollinger Bands
        ma20 = price_data["close"].rolling(window=20).mean()
        std20 = price_data["close"].rolling(window=20).std()
        
        upper_band = ma20 + 2 * std20
        lower_band = ma20 - 2 * std20
        
        # Calculate %B indicator
        percent_b = (price_data["close"] - lower_band) / (upper_band - lower_band)
        current_percent_b = float(percent_b.iloc[-1])
        
        reversion_metrics["percent_b"] = current_percent_b
        
        # Check for extreme Bollinger Band readings
        if current_percent_b < 0:
            score += 1
            reasoning.append(f"Price below lower Bollinger Band (%B: {current_percent_b:.2f})")
        elif current_percent_b > 1:
            score -= 1
            reasoning.append(f"Price above upper Bollinger Band (%B: {current_percent_b:.2f})")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
        "reversion_metrics": reversion_metrics,
    }


def analyze_momentum_factors(price_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze momentum factors in price data.
    
    Args:
        price_data: DataFrame with price data
        
    Returns:
        Dictionary with momentum factor analysis results
    """
    score = 0
    max_score = 2
    reasoning = []
    momentum_metrics = {}
    
    # Check if we have enough data
    if price_data.empty or len(price_data) < 60:
        return {
            "score": 0,
            "max_score": max_score,
            "details": "Insufficient data for momentum factor analysis",
            "momentum_metrics": {},
        }
    
    # 1. Calculate short-term momentum (20-day)
    if len(price_data) >= 20:
        momentum_20d = (price_data["close"].iloc[-1] / price_data["close"].iloc[-min(20, len(price_data))] - 1) * 100
        momentum_metrics["momentum_20d"] = float(momentum_20d)
        
        if momentum_20d > 10:
            score += 1
            reasoning.append(f"Strong short-term momentum detected ({momentum_20d:.2f}%)")
        elif momentum_20d < -10:
            score -= 1
            reasoning.append(f"Weak short-term momentum detected ({momentum_20d:.2f}%)")
    
    # 2. Calculate medium-term momentum (60-day)
    if len(price_data) >= 60:
        momentum_60d = (price_data["close"].iloc[-1] / price_data["close"].iloc[-min(60, len(price_data))] - 1) * 100
        momentum_metrics["momentum_60d"] = float(momentum_60d)
        
        if momentum_60d > 20:
            score += 1
            reasoning.append(f"Strong medium-term momentum detected ({momentum_60d:.2f}%)")
        elif momentum_60d < -20:
            score -= 1
            reasoning.append(f"Weak medium-term momentum detected ({momentum_60d:.2f}%)")
    
    # 3. Calculate rate of change in volume
    if "volume" in price_data.columns and len(price_data) >= 20:
        volume_roc = (price_data["volume"].iloc[-1] / price_data["volume"].iloc[-min(20, len(price_data)):].mean() - 1) * 100
        momentum_metrics["volume_roc"] = float(volume_roc)
        
        if volume_roc > 50 and momentum_20d > 0:
            score += 0.5
            reasoning.append(f"Increasing volume with positive momentum ({volume_roc:.2f}%)")
        elif volume_roc > 50 and momentum_20d < 0:
            score -= 0.5
            reasoning.append(f"Increasing volume with negative momentum ({volume_roc:.2f}%)")
    
    # 4. Calculate moving average crossovers
    if len(price_data) >= 50:
        # Calculate 10-day and 50-day moving averages
        ma10 = price_data["close"].rolling(window=10).mean()
        ma50 = price_data["close"].rolling(window=50).mean()
        
        # Check for recent crossover
        if len(ma10) > 2 and len(ma50) > 2:
            current_relation = ma10.iloc[-1] > ma50.iloc[-1]
            previous_relation = ma10.iloc[-2] > ma50.iloc[-2]
            
            if current_relation and not previous_relation:
                # Bullish crossover
                score += 1
                reasoning.append("Recent bullish MA crossover (10-day crossed above 50-day)")
                momentum_metrics["ma_crossover"] = "bullish"
            elif not current_relation and previous_relation:
                # Bearish crossover
                score -= 1
                reasoning.append("Recent bearish MA crossover (10-day crossed below 50-day)")
                momentum_metrics["ma_crossover"] = "bearish"
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
        "momentum_metrics": momentum_metrics,
    }


def generate_simons_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> SimonsSignal:
    """
    Generate detailed analysis using LLM with Jim Simons's principles.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        SimonsSignal object with the investment decision
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Jim Simons AI agent. Decide on investment signals based on Jim Simons's quantitative principles:
    - Statistical Patterns: Identify non-random patterns in market data that can be exploited
    - Market Inefficiencies: Focus on structural or behavioral anomalies that create mispricing
    - Mathematical Models: Use rigorous mathematical and statistical approaches rather than fundamental analysis
    - Short-term Trading: Emphasize higher-frequency trading opportunities rather than long-term investments
    - Diversification: Spread risk across many uncorrelated bets rather than concentrated positions
    - Systematic Approach: Follow a disciplined, rules-based approach rather than discretionary decisions

    When providing your reasoning, be thorough and specific by:
    1. Analyzing statistical patterns and anomalies in the price data
    2. Identifying specific market inefficiencies that can be exploited
    3. Explaining the mathematical basis for the trading signal
    4. Specifying the appropriate time horizon for the trade
    5. Using Jim Simons's quantitative, data-driven style in your explanation

    For example, if bullish: "Statistical analysis reveals significant positive autocorrelation in returns with a non-normal distribution (kurtosis: 2.3). The stock is trading 2.1 standard deviations below its 20-day moving average, a condition that has historically led to mean reversion with 68% probability..."
    For example, if bearish: "The data shows a statistically significant pattern of mean reversion following extreme upside moves (p-value: 0.02). Current RSI of 78.3 indicates an overbought condition, and the stock is trading 2.4 standard deviations above its 50-day moving average..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following data, create the investment signal as Jim Simons would:

    Analysis Data for {ticker}:
    {json.dumps(analysis_data[ticker], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string",
      "statistical_patterns": {{
        "key_pattern_1": "description",
        "key_pattern_2": "description"
      }},
      "market_inefficiencies": {{
        "key_inefficiency_1": "description",
        "key_inefficiency_2": "description"
      }},
      "time_horizon": "string describing the recommended time horizon for the trade"
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_simons_signal():
        ticker_data = analysis_data.get(ticker, {})
        signal = ticker_data.get("signal", "neutral")
        score = ticker_data.get("score", 0)
        max_score = ticker_data.get("max_score", 1)
        confidence = (score / max_score * 100) if max_score > 0 else 50.0
        
        statistical_patterns = ticker_data.get("statistical_patterns", {})
        market_inefficiencies = ticker_data.get("market_inefficiencies", {})
        
        # Determine time horizon based on which factors are strongest
        if "momentum_factors" in ticker_data and ticker_data["momentum_factors"]["score"] > 0:
            time_horizon = "Medium-term (1-3 months)"
        elif "mean_reversion" in ticker_data and ticker_data["mean_reversion"]["score"] != 0:
            time_horizon = "Short-term (1-2 weeks)"
        else:
            time_horizon = "Short to medium-term (2-4 weeks)"
        
        return SimonsSignal(
            signal=signal,
            confidence=confidence,
            reasoning="Analysis based on quantitative metrics only. Unable to generate detailed reasoning.",
            statistical_patterns={"pattern": statistical_patterns.get("details", "No patterns detected")},
            market_inefficiencies={"inefficiency": market_inefficiencies.get("details", "No inefficiencies detected")},
            time_horizon=time_horizon,
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=SimonsSignal,
        agent_name="jim_simons_agent",
        default_factory=create_default_simons_signal,
    )
