"""
TdxDataService 手动测试脚本

这个脚本用于手动测试 TdxDataService 的基本功能，包括：
1. 连接到行情服务器
2. 获取股票数据
3. 获取行情数据
4. 测试 WebSocket 客户端

使用方法：
python test_tdx_service_manual.py [选项]

选项：
--test-connect     测试连接功能
--test-stock       测试获取股票数据
--test-quote       测试获取行情数据
--test-index       测试获取指数数据
--test-ws          测试 WebSocket 客户端
--test-all         测试所有功能
--tsdb-path PATH   指定 TimeSeriesDB 路径，默认为 f:/hqdata/tsdb
"""

import os
import sys
import time
import json
import asyncio
import argparse
import websockets
import requests
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入被测试的模块
from rest_api.services.tdx_dc import TdxDataService
from rest_api.services.tdx_market_api import TdxStockMarketAPI, TdxFutureMarketAPI

class TdxServiceTester:
    """TdxDataService 测试类"""
    
    def __init__(self, tsdb_path="f:/hqdata/tsdb"):
        """初始化测试类"""
        self.tsdb_path = tsdb_path
        self.service = None
        self.api_base_url = "http://localhost:8000/api/v1/tdx"
        self.ws_url = "ws://localhost:8000/api/v1/tdx/ws"
        
    def create_service(self):
        """创建 TdxDataService 实例"""
        print(f"创建 TdxDataService 实例，TSDB 路径: {self.tsdb_path}")
        self.service = TdxDataService(self.tsdb_path)
        
    def test_connect(self):
        """测试连接功能"""
        print("\n===== 测试连接功能 =====")
        if not self.service:
            self.create_service()
            
        print("连接到行情服务器...")
        start_time = time.time()
        result = self.service.connect()
        end_time = time.time()
        
        if result:
            print(f"连接成功！耗时: {end_time - start_time:.2f} 秒")
            print(f"标准行情服务器连接状态: {self.service.connected}")
            print(f"扩展行情服务器连接状态: {self.service.ex_connected}")
        else:
            print("连接失败！")
            
        print("断开连接...")
        self.service.disconnect()
        print("已断开连接")
        
    def test_get_stock_data(self):
        """测试获取股票数据"""
        print("\n===== 测试获取股票数据 =====")
        if not self.service:
            self.create_service()
            
        # 确保已连接
        if not self.service.connected and not self.service.stock_api.connected:
            print("连接到行情服务器...")
            self.service.connect()
            
        # 测试获取股票日线数据
        print("\n获取平安银行(000001)日线数据...")
        start_time = time.time()
        data = asyncio.run(self.service.get_stock_data_async(0, "000001", "day", 10))
        end_time = time.time()
        
        if data:
            print(f"获取成功！耗时: {end_time - start_time:.2f} 秒")
            print(f"获取到 {len(data)} 条数据")
            print("数据示例:")
            for i, item in enumerate(data[:3]):
                print(f"  {i+1}. {item}")
        else:
            print("获取失败！")
            
        # 测试获取股票分钟线数据
        print("\n获取平安银行(000001)5分钟线数据...")
        start_time = time.time()
        data = asyncio.run(self.service.get_stock_data_async(0, "000001", "min5", 10))
        end_time = time.time()
        
        if data:
            print(f"获取成功！耗时: {end_time - start_time:.2f} 秒")
            print(f"获取到 {len(data)} 条数据")
            print("数据示例:")
            for i, item in enumerate(data[:3]):
                print(f"  {i+1}. {item}")
        else:
            print("获取失败！")
            
        # 断开连接
        print("\n断开连接...")
        self.service.disconnect()
        print("已断开连接")
        
    def test_get_quote_data(self):
        """测试获取行情数据"""
        print("\n===== 测试获取行情数据 =====")
        if not self.service:
            self.create_service()
            
        # 确保已连接
        if not self.service.connected and not self.service.stock_api.connected:
            print("连接到行情服务器...")
            self.service.connect()
            
        # 测试获取股票行情数据
        print("\n获取平安银行(000001)行情数据...")
        start_time = time.time()
        data = self.service.stock_api.get_security_quotes([(0, "000001")])
        end_time = time.time()
        
        if data:
            print(f"获取成功！耗时: {end_time - start_time:.2f} 秒")
            print("行情数据:")
            quote = dict(data[0])
            for key, value in quote.items():
                print(f"  {key}: {value}")
        else:
            print("获取失败！")
            
        # 断开连接
        print("\n断开连接...")
        self.service.disconnect()
        print("已断开连接")
        
    def test_get_index_data(self):
        """测试获取指数数据"""
        print("\n===== 测试获取指数数据 =====")
        if not self.service:
            self.create_service()
            
        # 确保已连接
        if not self.service.connected and not self.service.stock_api.connected:
            print("连接到行情服务器...")
            self.service.connect()
            
        # 测试获取指数日线数据
        print("\n获取上证指数(000001)日线数据...")
        start_time = time.time()
        data = self.service.stock_api.get_index_bars(9, 1, "000001", 0, 10)
        end_time = time.time()
        
        if data:
            print(f"获取成功！耗时: {end_time - start_time:.2f} 秒")
            print(f"获取到 {len(data)} 条数据")
            print("数据示例:")
            for i, item in enumerate(data[:3]):
                print(f"  {i+1}. {dict(item)}")
        else:
            print("获取失败！")
            
        # 断开连接
        print("\n断开连接...")
        self.service.disconnect()
        print("已断开连接")
        
    def test_get_future_data(self):
        """测试获取期货数据"""
        print("\n===== 测试获取期货数据 =====")
        if not self.service:
            self.create_service()
            
        # 确保已连接
        if not self.service.ex_connected and not self.service.future_api.connected:
            print("连接到行情服务器...")
            self.service.connect()
            
        # 获取期货市场列表
        print("\n获取期货市场列表...")
        start_time = time.time()
        markets = self.service.future_api.get_markets()
        end_time = time.time()
        
        if markets:
            print(f"获取成功！耗时: {end_time - start_time:.2f} 秒")
            print(f"获取到 {len(markets)} 个期货市场")
            print("市场示例:")
            for i, market in enumerate(markets[:3]):
                print(f"  {i+1}. {market}")
                
            # 获取期货合约列表
            if len(markets) > 0:
                market_id = markets[0]['market']
                market_name = markets[0]['name']
                print(f"\n获取期货市场 {market_name} 合约列表...")
                start_time = time.time()
                instruments = self.service.future_api.get_market_instruments(market_id)
                end_time = time.time()
                
                if not instruments.empty:
                    print(f"获取成功！耗时: {end_time - start_time:.2f} 秒")
                    print(f"获取到 {len(instruments)} 个合约")
                    print("合约示例:")
                    for i, (_, inst) in enumerate(instruments.head(3).iterrows()):
                        print(f"  {i+1}. {dict(inst)}")
                        
                    # 获取期货K线数据
                    if len(instruments) > 0:
                        code = instruments.iloc[0]['code']
                        print(f"\n获取期货 {code} K线数据...")
                        start_time = time.time()
                        data = self.service.future_api.get_instrument_bars(4, market_id, code, 0, 10)
                        end_time = time.time()
                        
                        if data:
                            print(f"获取成功！耗时: {end_time - start_time:.2f} 秒")
                            print(f"获取到 {len(data)} 条数据")
                            print("数据示例:")
                            for i, item in enumerate(data[:3]):
                                print(f"  {i+1}. {dict(item)}")
                        else:
                            print("获取失败！")
                else:
                    print("获取失败！")
        else:
            print("获取失败！")
            
        # 断开连接
        print("\n断开连接...")
        self.service.disconnect()
        print("已断开连接")
        
    async def test_websocket_client(self):
        """测试 WebSocket 客户端"""
        print("\n===== 测试 WebSocket 客户端 =====")
        
        try:
            print(f"连接到 WebSocket 服务器: {self.ws_url}")
            async with websockets.connect(self.ws_url) as websocket:
                print("WebSocket 连接成功！")
                
                # 订阅股票行情
                symbols = ["000001.SZ", "600000.SH"]
                print(f"\n订阅股票行情: {symbols}")
                subscribe_msg = {
                    "action": "subscribe",
                    "symbols": symbols
                }
                await websocket.send(json.dumps(subscribe_msg))
                response = await websocket.recv()
                print(f"订阅响应: {response}")
                
                # 接收行情更新
                print("\n等待行情更新 (10秒)...")
                for _ in range(5):
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        print(f"收到行情更新: {response}")
                    except asyncio.TimeoutError:
                        print("等待超时，未收到行情更新")
                
                # 获取股票数据
                print("\n获取股票数据...")
                get_data_msg = {
                    "action": "get_stock_data",
                    "symbol": "000001",
                    "market": 0,
                    "period": "day",
                    "count": 5
                }
                await websocket.send(json.dumps(get_data_msg))
                response = await websocket.recv()
                print(f"获取股票数据响应: {response}")
                
                # 取消订阅
                print(f"\n取消订阅股票行情: {symbols}")
                unsubscribe_msg = {
                    "action": "unsubscribe",
                    "symbols": symbols
                }
                await websocket.send(json.dumps(unsubscribe_msg))
                response = await websocket.recv()
                print(f"取消订阅响应: {response}")
                
                print("\nWebSocket 测试完成")
        except Exception as e:
            print(f"WebSocket 测试出错: {str(e)}")
            
    def test_rest_api(self):
        """测试 REST API"""
        print("\n===== 测试 REST API =====")
        
        # 测试获取股票数据
        print("\n测试获取股票数据 API...")
        url = f"{self.api_base_url}/stock/SZ/000001?period=day&count=5"
        print(f"请求 URL: {url}")
        
        try:
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json()
                print(f"获取成功！获取到 {len(data['data'])} 条数据")
                print("数据示例:")
                for i, item in enumerate(data['data'][:3]):
                    print(f"  {i+1}. {item}")
            else:
                print(f"获取失败！状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
        except Exception as e:
            print(f"请求出错: {str(e)}")
            
        # 测试获取股票行情
        print("\n测试获取股票行情 API...")
        url = f"{self.api_base_url}/quote/SZ/000001"
        print(f"请求 URL: {url}")
        
        try:
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json()
                print("获取成功！行情数据:")
                for key, value in data.items():
                    print(f"  {key}: {value}")
            else:
                print(f"获取失败！状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
        except Exception as e:
            print(f"请求出错: {str(e)}")
            
        # 测试获取指数数据
        print("\n测试获取指数数据 API...")
        url = f"{self.api_base_url}/index/SH/000001?period=day&count=5"
        print(f"请求 URL: {url}")
        
        try:
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json()
                print(f"获取成功！获取到 {len(data['data'])} 条数据")
                print("数据示例:")
                for i, item in enumerate(data['data'][:3]):
                    print(f"  {i+1}. {item}")
            else:
                print(f"获取失败！状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
        except Exception as e:
            print(f"请求出错: {str(e)}")
            
        # 测试获取股票列表
        print("\n测试获取股票列表 API...")
        url = f"{self.api_base_url}/stocks"
        print(f"请求 URL: {url}")
        
        try:
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json()
                print(f"获取成功！获取到 {data['count']} 只股票")
                print("股票示例:")
                for i, item in enumerate(data['stocks'][:3]):
                    print(f"  {i+1}. {item}")
            else:
                print(f"获取失败！状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
        except Exception as e:
            print(f"请求出错: {str(e)}")
            
        # 测试获取期货列表
        print("\n测试获取期货列表 API...")
        url = f"{self.api_base_url}/futures"
        print(f"请求 URL: {url}")
        
        try:
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json()
                print(f"获取成功！获取到 {data['count']} 个期货合约")
                print("期货合约示例:")
                for i, item in enumerate(data['futures'][:3]):
                    print(f"  {i+1}. {item}")
            else:
                print(f"获取失败！状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
        except Exception as e:
            print(f"请求出错: {str(e)}")
            
    def run_all_tests(self):
        """运行所有测试"""
        self.test_connect()
        self.test_get_stock_data()
        self.test_get_quote_data()
        self.test_get_index_data()
        self.test_get_future_data()
        self.test_rest_api()
        asyncio.run(self.test_websocket_client())
        
def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="TdxDataService 手动测试脚本")
    parser.add_argument("--test-connect", action="store_true", help="测试连接功能")
    parser.add_argument("--test-stock", action="store_true", help="测试获取股票数据")
    parser.add_argument("--test-quote", action="store_true", help="测试获取行情数据")
    parser.add_argument("--test-index", action="store_true", help="测试获取指数数据")
    parser.add_argument("--test-future", action="store_true", help="测试获取期货数据")
    parser.add_argument("--test-ws", action="store_true", help="测试 WebSocket 客户端")
    parser.add_argument("--test-api", action="store_true", help="测试 REST API")
    parser.add_argument("--test-all", action="store_true", help="测试所有功能")
    parser.add_argument("--tsdb-path", type=str, default="f:/hqdata/tsdb", help="指定 TimeSeriesDB 路径")
    
    args = parser.parse_args()
    
    # 创建测试类
    tester = TdxServiceTester(args.tsdb_path)
    
    # 运行测试
    if args.test_all:
        tester.run_all_tests()
    else:
        if args.test_connect:
            tester.test_connect()
        if args.test_stock:
            tester.test_get_stock_data()
        if args.test_quote:
            tester.test_get_quote_data()
        if args.test_index:
            tester.test_get_index_data()
        if args.test_future:
            tester.test_get_future_data()
        if args.test_api:
            tester.test_rest_api()
        if args.test_ws:
            asyncio.run(tester.test_websocket_client())
            
    print("\n所有测试完成！")
    
if __name__ == "__main__":
    main()
