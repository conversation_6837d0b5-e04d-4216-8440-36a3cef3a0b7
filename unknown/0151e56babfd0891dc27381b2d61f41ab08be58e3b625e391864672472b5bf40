/**
 * 认证拦截器
 *
 * 用于处理API请求的认证令牌管理，包括：
 * 1. 自动为请求添加认证令牌
 * 2. 检测令牌过期错误并自动刷新令牌
 * 3. 管理令牌存储
 */

// API基础URL和版本在config.js中定义

// 令牌刷新状态
let isRefreshing = false;
let refreshSubscribers = [];

/**
 * 订阅令牌刷新
 * @param {Function} callback 令牌刷新后的回调函数
 */
function subscribeTokenRefresh(callback) {
    refreshSubscribers.push(callback);
}

/**
 * 通知所有订阅者令牌已刷新
 * @param {string} token 新的访问令牌
 */
function onTokenRefreshed(token) {
    refreshSubscribers.forEach(callback => callback(token));
    refreshSubscribers = [];
}

/**
 * 重置刷新状态
 */
function onRefreshError() {
    isRefreshing = false;
    refreshSubscribers = [];
}

/**
 * 刷新访问令牌
 * @returns {Promise<string>} 新的访问令牌
 */
async function refreshAccessToken() {
    try {
        // 从本地存储获取当前令牌
        const currentToken = localStorage.getItem('accessToken');
        if (!currentToken) {
            throw new Error('没有可用的访问令牌');
        }

        // 调用刷新令牌API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/refresh-token`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${currentToken}`
            }
        });

        // 解析响应
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.detail || '刷新令牌失败');
        }

        // 更新本地存储的令牌
        if (data.access_token) {
            localStorage.setItem('accessToken', data.access_token);

            // 如果响应中包含用户信息，也更新用户信息
            if (data.username) {
                const userInfo = {
                    username: data.username,
                    full_name: data.full_name,
                    email: data.email,
                    is_admin: data.is_admin
                };
                localStorage.setItem('user', JSON.stringify(userInfo));
            }

            return data.access_token;
        } else {
            throw new Error('刷新令牌响应中缺少访问令牌');
        }
    } catch (error) {
        console.error('刷新令牌失败:', error);
        // 清除本地存储的令牌和用户信息
        localStorage.removeItem('accessToken');
        localStorage.removeItem('user');
        // 重定向到登录页面或显示登录提示
        showLoginAlert();
        throw error;
    }
}

/**
 * 显示登录提示
 */
function showLoginAlert() {
    // 检查是否有登录提示元素
    const loginAlert = document.getElementById('loginAlert');
    if (loginAlert) {
        loginAlert.style.display = 'block';
    }

    // 更新全局变量
    if (typeof isLoggedIn !== 'undefined') {
        isLoggedIn = false;
    }
    if (typeof currentUser !== 'undefined') {
        currentUser = null;
    }
    if (typeof accessToken !== 'undefined') {
        accessToken = null;
    }

    // 更新UI
    if (typeof updateLoginUI === 'function') {
        updateLoginUI();
    }
}

/**
 * 发送带认证的API请求
 * @param {string} url 请求URL
 * @param {Object} options 请求选项
 * @returns {Promise<Response>} 响应对象
 */
async function fetchWithAuth(url, options = {}) {
    // 从本地存储获取访问令牌
    const token = localStorage.getItem('accessToken');

    // 准备请求头
    const headers = options.headers || {};
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    // 发送请求
    try {
        const response = await fetch(url, {
            ...options,
            headers
        });

        // 检查是否是服务器内部错误
        if (response.status === 500) {
            console.warn('服务器内部错误:', url);
            // 直接返回响应，让调用者处理
            return response;
        }

        // 检查是否是令牌过期错误
        if (response.status === 401) {
            // 尝试解析响应数据
            let responseData;
            try {
                responseData = await response.json();
            } catch (e) {
                // 如果无法解析JSON，假设是令牌错误
                responseData = { detail: '无效的认证凭据' };
            }

            // 如果是令牌过期错误或认证错误，尝试刷新令牌
            if (responseData.detail && (
                responseData.detail.includes('Token已过期') ||
                responseData.detail.includes('无效的认证凭据')
            )) {
                // 如果已经在刷新令牌，等待刷新完成
                if (isRefreshing) {
                    return new Promise((resolve) => {
                        subscribeTokenRefresh(token => {
                            // 使用新令牌重新发送请求
                            headers['Authorization'] = `Bearer ${token}`;
                            resolve(fetch(url, {
                                ...options,
                                headers
                            }));
                        });
                    });
                }

                // 开始刷新令牌
                isRefreshing = true;

                try {
                    // 刷新令牌
                    const newToken = await refreshAccessToken();

                    // 通知所有订阅者
                    onTokenRefreshed(newToken);

                    // 使用新令牌重新发送请求
                    headers['Authorization'] = `Bearer ${newToken}`;
                    return fetch(url, {
                        ...options,
                        headers
                    });
                } catch (error) {
                    // 刷新令牌失败
                    onRefreshError();
                    throw error;
                } finally {
                    isRefreshing = false;
                }
            }
        }

        return response;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// 导出函数
window.fetchWithAuth = fetchWithAuth;
