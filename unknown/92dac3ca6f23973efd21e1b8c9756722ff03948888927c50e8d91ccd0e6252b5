"""
Technical Analyst agent for the AI Hedge Fund module.

This agent analyzes stocks using technical analysis.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_price_data
from ..utils.technical_analysis import calculate_technical_indicators, analyze_technical_signals
from ..utils.llm import call_llm


class TechnicalSignal(BaseModel):
    """Signal generated by the Technical Analyst agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")


def technical_analyst_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using technical analysis.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with technical analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    technical_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("technical_analyst_agent", ticker, "Fetching price data")
        
        # Get price data
        price_data = get_price_data(ticker, start_date, end_date)
        
        if price_data.empty:
            # Skip this ticker if no price data
            if "progress" in state["metadata"]:
                state["metadata"]["progress"].update_status("technical_analyst_agent", ticker, "No price data available")
            
            technical_analysis[ticker] = {
                "signal": "neutral",
                "confidence": 0.0,
                "reasoning": "No price data available for technical analysis",
            }
            continue
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("technical_analyst_agent", ticker, "Calculating technical indicators")
        
        # Calculate technical indicators
        indicators = calculate_technical_indicators(price_data)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("technical_analyst_agent", ticker, "Analyzing technical signals")
        
        # Analyze technical signals
        tech_signals = analyze_technical_signals(indicators)
        
        # Store analysis data
        analysis_data[ticker] = tech_signals
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("technical_analyst_agent", ticker, "Generating detailed analysis")
        
        # Generate detailed analysis using LLM
        technical_output = generate_technical_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        technical_analysis[ticker] = {
            "signal": technical_output.signal,
            "confidence": technical_output.confidence,
            "reasoning": technical_output.reasoning,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("technical_analyst_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(technical_analysis, "Technical Analyst Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["technical_analyst_agent"] = technical_analysis
    
    return state


def generate_technical_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> TechnicalSignal:
    """
    Generate detailed technical analysis using LLM.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        TechnicalSignal object with the technical analysis
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Technical Analyst AI agent. Analyze stocks using technical indicators and chart patterns.

    Key principles for technical analysis:
    - Trend Analysis: Identify the primary trend (uptrend, downtrend, or sideways)
    - Support and Resistance: Identify key price levels
    - Moving Averages: Use crossovers and price relative to MAs
    - Momentum Indicators: Analyze RSI, MACD, and other oscillators
    - Volume Analysis: Consider volume confirmation of price moves
    - Pattern Recognition: Identify chart patterns (head and shoulders, double tops, etc.)
    - Divergence: Look for divergence between price and indicators

    When providing your reasoning, be thorough and specific by:
    1. Summarizing the overall technical picture (bullish, bearish, or neutral)
    2. Highlighting the most significant technical signals that influenced your decision
    3. Explaining how different indicators confirm or contradict each other
    4. Providing specific price levels for support, resistance, or targets
    5. Using a professional, analytical tone in your explanation

    For example, if bullish: "Price is in an uptrend, trading above the 50-day and 200-day moving averages. The MACD shows positive momentum with a recent bullish crossover. RSI at 65 indicates strength without being overbought..."
    For example, if bearish: "Price has broken below the 50-day moving average with increasing volume. The MACD shows negative momentum, and RSI at 35 is trending lower. Key support at $X has been violated..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following technical analysis data, create a trading signal for {ticker}:

    Analysis Data:
    {json.dumps(analysis_data[ticker], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string"
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_technical_signal():
        ticker_data = analysis_data.get(ticker, {})
        overall_signal = ticker_data.get("overall_signal", "neutral")
        signal_strength = ticker_data.get("signal_strength", 50.0)
        summary = ticker_data.get("summary", "")
        
        return TechnicalSignal(
            signal=overall_signal,
            confidence=signal_strength,
            reasoning=summary
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=TechnicalSignal,
        agent_name="technical_analyst_agent",
        default_factory=create_default_technical_signal,
    )
