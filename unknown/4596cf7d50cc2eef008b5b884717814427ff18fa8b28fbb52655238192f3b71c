import sys
import os
import pandas as pd
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入DataHub类
from rest_api.routes.datahub_routes import DataHub
from qtunnel import BarSize, DoRight, RunMode

def test_method():
    """测试单个方法，用于快速调试"""
    # 初始化DataHub实例
    data_hub = DataHub(
        data_path="d:/RoboQuant2",  # 请根据实际路径修改
        market="fut",
        period="day",
        block_name="main"
    )
    
    # 测试用的股票代码和期货代码
    stock_code = "600000.SH"  # 浦发银行
    index_code = "000001.SH"  # 上证指数
    future_code = "IF9999.SF"  # 沪深300指数期货主力合约
    
    # 测试获取历史数据
    print("测试获取历史数据...")
    df = data_hub.get_hist_data(stock_code, period="1mo", interval="1d")
    print(f"获取到 {len(df)} 条记录")
    if not df.empty:
        print(df.head())
    
    # 测试获取K线数据
    print("\n测试获取K线数据...")
    df = data_hub.get_kline_data(stock_code, BarSize.day)
    print(f"获取到 {len(df)} 条记录")
    if not df.empty:
        print(df.head())
    
    # 测试获取股票信息
    print("\n测试获取股票信息...")
    info = data_hub.get_stock_info(stock_code)
    print(info)
    
    # 测试获取指数数据
    print("\n测试获取指数数据...")
    df = data_hub.get_index_data(index_code)
    print(f"获取到 {len(df)} 条记录")
    if not df.empty:
        print(df.head())
    
    # 测试获取板块数据
    print("\n测试获取板块数据...")
    stocks = data_hub.ds.get_block_data("沪深300")
    print(f"获取到 {len(stocks)} 只股票")
    if stocks:
        print(stocks[:5])  # 打印前5只股票
    
    # 测试获取期货信息
    print("\n测试获取期货信息...")
    info = data_hub.get_future_info(future_code)
    print(info)

if __name__ == '__main__':
    test_method()
