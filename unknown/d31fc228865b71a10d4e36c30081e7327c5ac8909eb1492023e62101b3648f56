// 技术指标可视化模块

// 全局变量
let indicatorCharts = {};

// 初始化技术指标
function initTechnicalIndicators() {
    // 添加技术指标按钮点击事件
    document.getElementById('showIndicatorsBtn').addEventListener('click', function() {
        const ticker = currentTicker;
        if (!ticker) {
            alert('请先搜索股票代码');
            return;
        }

        // 显示技术指标面板
        const indicatorsPanel = document.getElementById('technicalIndicatorsPanel');
        indicatorsPanel.style.display = 'block';

        // 加载技术指标
        loadTechnicalIndicators(ticker);
    });

    // 关闭按钮点击事件
    document.getElementById('closeIndicatorsBtn').addEventListener('click', function() {
        document.getElementById('technicalIndicatorsPanel').style.display = 'none';
    });

    // 指标选择事件
    document.querySelectorAll('input[name="indicator"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked && currentTicker) {
                const indicator = this.value;
                updateIndicatorChart(currentTicker, indicator);
            }
        });
    });
}

// 加载技术指标
async function loadTechnicalIndicators(ticker) {
    // 获取图表容器
    const chartContainer = document.getElementById('indicatorChartContainer');
    if (!chartContainer) {
        console.error('找不到指标图表容器');
        return;
    }

    // 显示加载动画
    chartContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 获取当前选中的指标
        const selectedIndicator = document.querySelector('input[name="indicator"]:checked').value;

        // 更新指标图表
        await updateIndicatorChart(ticker, selectedIndicator);
    } catch (error) {
        console.error('加载技术指标失败:', error);
        chartContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">加载技术指标失败: ${error.message}</p></div>`;
    }
}

// 更新指标图表
async function updateIndicatorChart(ticker, indicator) {
    // 获取图表容器
    const chartContainer = document.getElementById('indicatorChartContainer');
    if (!chartContainer) {
        console.error('找不到指标图表容器');
        return;
    }

    // 显示加载动画
    chartContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 构建API URL
        // 注意：这里假设后端有相应的API端点，如果没有，可以使用模拟数据
        let url = `${API_BASE_URL}/stock/${ticker}/indicators?type=${indicator}`;

        // 尝试调用API
        let data;
        try {
            const response = await fetch(url, {
                headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
            });

            if (!response.ok) {
                throw new Error(`API错误: ${response.status}`);
            }

            data = await response.json();
        } catch (error) {
            console.warn('获取指标数据失败，使用模拟数据:', error);
            // 使用模拟数据
            data = generateMockIndicatorData(ticker, indicator);
        }

        // 创建新的canvas元素
        chartContainer.innerHTML = '<canvas id="indicatorChart" height="300"></canvas>';

        // 检查canvas是否创建成功
        const canvas = document.getElementById('indicatorChart');
        if (!canvas) {
            console.error('创建指标图表canvas失败');
            chartContainer.innerHTML = '<div class="text-center py-5"><p class="text-danger">创建图表失败</p></div>';
            return;
        }

        // 获取canvas上下文
        const ctx = canvas.getContext('2d');

        // 销毁旧图表
        if (indicatorCharts[indicator]) {
            indicatorCharts[indicator].destroy();
        }

        // 创建新图表
        indicatorCharts[indicator] = createIndicatorChart(ctx, data, indicator);

    } catch (error) {
        console.error('更新指标图表失败:', error);
        chartContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">更新指标图表失败: ${error.message}</p></div>`;
    }
}

// 创建指标图表
function createIndicatorChart(ctx, data, indicator) {
    // 根据不同指标类型创建不同的图表
    switch (indicator) {
        case 'ma':
            return createMAChart(ctx, data);
        case 'rsi':
            return createRSIChart(ctx, data);
        case 'macd':
            return createMACDChart(ctx, data);
        case 'bollinger':
            return createBollingerChart(ctx, data);
        default:
            return createDefaultChart(ctx, data);
    }
}

// 创建移动平均线图表
function createMAChart(ctx, data) {
    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.dates,
            datasets: [
                {
                    label: '价格',
                    data: data.prices,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'MA5',
                    data: data.ma5,
                    borderColor: '#dc3545',
                    borderWidth: 1.5,
                    pointRadius: 0,
                    fill: false
                },
                {
                    label: 'MA10',
                    data: data.ma10,
                    borderColor: '#fd7e14',
                    borderWidth: 1.5,
                    pointRadius: 0,
                    fill: false
                },
                {
                    label: 'MA20',
                    data: data.ma20,
                    borderColor: '#20c997',
                    borderWidth: 1.5,
                    pointRadius: 0,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '移动平均线 (MA)'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        }
    });
}

// 创建RSI图表
function createRSIChart(ctx, data) {
    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.dates,
            datasets: [
                {
                    label: 'RSI',
                    data: data.rsi,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: '超买线 (70)',
                    data: Array(data.dates.length).fill(70),
                    borderColor: '#dc3545',
                    borderWidth: 1,
                    borderDash: [5, 5],
                    pointRadius: 0,
                    fill: false
                },
                {
                    label: '超卖线 (30)',
                    data: Array(data.dates.length).fill(30),
                    borderColor: '#20c997',
                    borderWidth: 1,
                    borderDash: [5, 5],
                    pointRadius: 0,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '相对强弱指标 (RSI)'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    min: 0,
                    max: 100,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        }
    });
}

// 创建MACD图表
function createMACDChart(ctx, data) {
    // 创建柱状图数据集
    const barColors = data.histogram.map(value => value >= 0 ? 'rgba(40, 167, 69, 0.6)' : 'rgba(220, 53, 69, 0.6)');
    const barBorders = data.histogram.map(value => value >= 0 ? 'rgb(40, 167, 69)' : 'rgb(220, 53, 69)');

    return new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.dates,
            datasets: [
                {
                    label: 'MACD直方图',
                    data: data.histogram,
                    backgroundColor: barColors,
                    borderColor: barBorders,
                    borderWidth: 1,
                    type: 'bar',
                    order: 3
                },
                {
                    label: 'MACD线',
                    data: data.macd,
                    borderColor: '#0d6efd',
                    borderWidth: 2,
                    pointRadius: 0,
                    type: 'line',
                    fill: false,
                    order: 1
                },
                {
                    label: '信号线',
                    data: data.signal,
                    borderColor: '#fd7e14',
                    borderWidth: 2,
                    pointRadius: 0,
                    type: 'line',
                    fill: false,
                    order: 2
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '移动平均线收敛/发散指标 (MACD)'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        }
    });
}

// 创建布林带图表
function createBollingerChart(ctx, data) {
    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.dates,
            datasets: [
                {
                    label: '价格',
                    data: data.prices,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    order: 1
                },
                {
                    label: '上轨',
                    data: data.upper,
                    borderColor: '#dc3545',
                    borderWidth: 1.5,
                    pointRadius: 0,
                    fill: false,
                    order: 2
                },
                {
                    label: '中轨 (MA20)',
                    data: data.middle,
                    borderColor: '#fd7e14',
                    borderWidth: 1.5,
                    pointRadius: 0,
                    fill: false,
                    order: 3
                },
                {
                    label: '下轨',
                    data: data.lower,
                    borderColor: '#20c997',
                    borderWidth: 1.5,
                    pointRadius: 0,
                    fill: false,
                    order: 4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '布林带 (Bollinger Bands)'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        }
    });
}

// 创建默认图表
function createDefaultChart(ctx, data) {
    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.dates,
            datasets: [
                {
                    label: '指标值',
                    data: data.values,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '技术指标'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        }
    });
}

// 生成模拟指标数据
function generateMockIndicatorData(ticker, indicator) {
    const dates = [];
    const today = new Date();

    // 生成日期数据（过去30天）
    for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        dates.push(date.toISOString().split('T')[0]);
    }

    // 生成基础价格数据
    let basePrice = 100 + Math.random() * 100;
    const prices = [];
    for (let i = 0; i < 30; i++) {
        basePrice += (Math.random() - 0.5) * 5;
        prices.push(basePrice);
    }

    // 根据不同指标类型生成不同的数据
    switch (indicator) {
        case 'ma':
            return generateMAData(dates, prices);
        case 'rsi':
            return generateRSIData(dates, prices);
        case 'macd':
            return generateMACDData(dates, prices);
        case 'bollinger':
            return generateBollingerData(dates, prices);
        default:
            return {
                dates: dates,
                values: prices.map(p => p + (Math.random() - 0.5) * 10)
            };
    }
}

// 生成移动平均线数据
function generateMAData(dates, prices) {
    // 计算简单移动平均线
    const ma5 = [];
    const ma10 = [];
    const ma20 = [];

    for (let i = 0; i < prices.length; i++) {
        // MA5
        if (i >= 4) {
            const sum5 = prices.slice(i - 4, i + 1).reduce((a, b) => a + b, 0);
            ma5.push(sum5 / 5);
        } else {
            ma5.push(null);
        }

        // MA10
        if (i >= 9) {
            const sum10 = prices.slice(i - 9, i + 1).reduce((a, b) => a + b, 0);
            ma10.push(sum10 / 10);
        } else {
            ma10.push(null);
        }

        // MA20
        if (i >= 19) {
            const sum20 = prices.slice(i - 19, i + 1).reduce((a, b) => a + b, 0);
            ma20.push(sum20 / 20);
        } else {
            ma20.push(null);
        }
    }

    return {
        dates: dates,
        prices: prices,
        ma5: ma5,
        ma10: ma10,
        ma20: ma20
    };
}

// 生成RSI数据
function generateRSIData(dates, prices) {
    // 简化的RSI计算
    const rsi = [null];

    for (let i = 1; i < prices.length; i++) {
        // 随机生成一个合理的RSI值
        let rsiValue;
        const prevPrice = prices[i - 1];
        const currentPrice = prices[i];

        if (currentPrice > prevPrice) {
            // 上涨趋势，RSI偏高
            rsiValue = 50 + Math.random() * 40;
        } else {
            // 下跌趋势，RSI偏低
            rsiValue = 10 + Math.random() * 40;
        }

        rsi.push(rsiValue);
    }

    return {
        dates: dates,
        rsi: rsi
    };
}

// 生成MACD数据
function generateMACDData(dates, prices) {
    // 简化的MACD计算
    const macd = [];
    const signal = [];
    const histogram = [];

    // 生成随机MACD线
    for (let i = 0; i < prices.length; i++) {
        if (i < 12) {
            macd.push(null);
        } else {
            macd.push((Math.random() - 0.5) * 4);
        }
    }

    // 生成信号线
    for (let i = 0; i < prices.length; i++) {
        if (i < 12 || macd[i] === null) {
            signal.push(null);
        } else {
            signal.push(macd[i] + (Math.random() - 0.5) * 1.5);
        }
    }

    // 计算直方图
    for (let i = 0; i < prices.length; i++) {
        if (macd[i] === null || signal[i] === null) {
            histogram.push(null);
        } else {
            histogram.push(macd[i] - signal[i]);
        }
    }

    return {
        dates: dates,
        macd: macd,
        signal: signal,
        histogram: histogram
    };
}

// 生成布林带数据
function generateBollingerData(dates, prices) {
    // 计算20日移动平均线
    const middle = [];

    for (let i = 0; i < prices.length; i++) {
        if (i >= 19) {
            const sum = prices.slice(i - 19, i + 1).reduce((a, b) => a + b, 0);
            middle.push(sum / 20);
        } else {
            middle.push(null);
        }
    }

    // 计算标准差
    const upper = [];
    const lower = [];

    for (let i = 0; i < prices.length; i++) {
        if (middle[i] === null) {
            upper.push(null);
            lower.push(null);
        } else {
            // 简化的标准差计算
            const stdDev = 10 + Math.random() * 5;
            upper.push(middle[i] + 2 * stdDev);
            lower.push(middle[i] - 2 * stdDev);
        }
    }

    return {
        dates: dates,
        prices: prices,
        upper: upper,
        middle: middle,
        lower: lower
    };
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否存在技术指标面板
    if (document.getElementById('technicalIndicatorsPanel')) {
        initTechnicalIndicators();
    }
});
