# API配置系统

本文档介绍了智能体投顾助手的API配置系统，该系统允许通过配置来决定是否启用或禁用特定的API。

## 概述

智能体投顾助手的API分为两类：

1. **核心API**：始终启用，不可禁用，包括：
   - 用户管理API (`user`)
   - 投资组合API (`portfolio`)
   - 自选股API (`favorite`)

2. **可配置API**：可以通过配置启用或禁用，包括：
   - 模型信息API (`model`)
   - 时序检索API (`tsrag`)
   - 数据中心API (`datahub`)
   - 订阅服务API (`subscription`)
   - 活动日志API (`activity_log`)
   - 股票数据API (`stock`)
   - 新闻文章API (`news`)

## 配置方法

### 配置文件方式

在`config/config.json`文件中，通过`API_SETTINGS`部分配置API的启用状态：

```json
{
  "API_SETTINGS": {
    "CORE_APIS": ["user", "portfolio", "favorite"],
    "ENABLED_APIS": {
      "model": true,
      "tsrag": true,
      "datahub": true,
      "subscription": true,
      "activity_log": true,
      "stock": false,
      "news": false
    }
  }
}
```

- `CORE_APIS`：列出所有核心API，这些API始终启用
- `ENABLED_APIS`：配置可选API的启用状态，`true`表示启用，`false`表示禁用

### 环境变量方式

也可以通过环境变量`ENABLE_ALL_APIS`来覆盖配置文件中的设置：

```
ENABLE_ALL_APIS=true
```

当`ENABLE_ALL_APIS`设置为`true`时，所有API都将被启用，无视配置文件中的设置。

## 按需导入模块

系统会根据配置的启用状态，只导入已启用的API路由模块，这样可以避免不必要的模块初始化，提高应用启动速度和减少内存占用。

在`app.py`中，系统会根据配置有条件地导入路由模块：

```python
# 始终导入核心API路由模块
from rest_api.routes import portfolio_routes, user_routes, favorite_routes

# 按需导入其他API路由模块
if is_api_enabled("model"):
    from rest_api.routes import model_routes
    logger.info("已导入model_routes模块")
else:
    model_routes = None
    logger.info("跳过禁用的model_routes模块")
```

这样，如果某个API被禁用，其相应的路由模块就不会被导入，从而避免了不必要的模块初始化。

## 查看API状态

可以通过访问API根路径`/api`来查看所有API的启用状态：

```json
{
  "message": "欢迎使用智能体投顾助手API",
  "version": "1.0.0",
  "available_versions": ["v1"],
  "current_version": "v1",
  "api_status": {
    "stock": false,
    "news": false,
    "model": true,
    "tsrag": true,
    "datahub": true,
    "portfolio": true,
    "user": true,
    "subscription": true,
    "activity_log": true,
    "favorite": true
  },
  "config_status": {
    "stock": false,
    "news": false,
    "model": true,
    "tsrag": true,
    "datahub": true,
    "portfolio": true,
    "user": true,
    "subscription": true,
    "activity_log": true,
    "favorite": true
  },
  "endpoints": {
    "模型信息": "/api/v1/model",
    "时序检索": "/api/v1/tsrag",
    "数据中心": "/api/v1/datahub",
    "投资组合": "/api/v1/portfolios",
    "用户管理": "/api/v1/user",
    "订阅服务": "/api/v1/subscription",
    "活动日志": "/api/v1/logs",
    "自选股": "/api/v1/favorites"
  }
}
```

响应中的字段说明：
- `api_status`：显示实际已加载的API模块状态
- `config_status`：显示配置文件中的API启用状态
- `endpoints`：只列出了已启用的API端点

## 添加新的API

如果需要添加新的API，请按照以下步骤操作：

1. 创建新的路由模块
2. 在`config/config.json`的`API_SETTINGS.ENABLED_APIS`中添加新API的配置
3. 在`app.py`中添加按需导入的代码
4. 在`app.py`中使用条件注册路由

如果新API是核心API，应该添加到`CORE_APIS`列表中，并直接导入和注册。

## 最佳实践

- 核心API应该尽可能少，只包含系统正常运行所必需的API
- 对于可能消耗大量资源或者只在特定场景下使用的API，建议设置为可配置API
- 在开发环境中可以设置`ENABLE_ALL_APIS=true`以方便测试，但在生产环境中应该根据实际需求配置API的启用状态
- 对于资源密集型的API，如果不需要使用，建议禁用以减少内存占用和提高应用启动速度
