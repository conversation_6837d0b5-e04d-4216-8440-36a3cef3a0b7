{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/stock/{ticker}/price-stats": {"get": {"tags": ["Stock Data"], "summary": "Price Stats", "description": "Get stock price statistics for a specific ticker.\n\nArgs:\n    ticker (str): Stock ticker symbol.\n    operation (str): Operation to perform (e.g., 'highest', 'lowest', 'average').\n    price_type (str): Type of price (e.g., 'open', 'close', 'low', 'high').\n    duration (int): Number of days\n\nReturns:\n    dict: Stock data with the requested statistics.", "operationId": "price_stats_stock__ticker__price_stats_get", "parameters": [{"name": "ticker", "in": "path", "required": true, "schema": {"type": "string", "title": "Ticker"}}, {"name": "operation", "in": "query", "required": true, "schema": {"type": "string", "description": "Operation to perform: 'highest', 'lowest', 'average'", "title": "Operation"}, "description": "Operation to perform: 'highest', 'lowest', 'average'"}, {"name": "price_type", "in": "query", "required": true, "schema": {"type": "string", "description": "Price type: 'open', 'close', 'low', 'high'", "title": "Price Type"}, "description": "Price type: 'open', 'close', 'low', 'high'"}, {"name": "duration", "in": "query", "required": true, "schema": {"type": "string", "description": "Duration (days): '1', '7', '14', '30'", "title": "Duration"}, "description": "Duration (days): '1', '7', '14', '30'"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/stock/{ticker}/chart": {"get": {"tags": ["Stock Data"], "summary": "Chart", "description": "Get stock price statistics and return a histogram/chart for a specific ticker.\n\nArgs:\n    ticker (str): Stock ticker symbol.\n    price_type (str): Type of price (e.g., 'open', 'close', 'low', 'high').\n    duration (int): Number of days\n\nReturns:\n    dict: Stock data with the requested statistics.", "operationId": "chart_stock__ticker__chart_get", "parameters": [{"name": "ticker", "in": "path", "required": true, "schema": {"type": "string", "title": "Ticker"}}, {"name": "price_type", "in": "query", "required": true, "schema": {"type": "string", "description": "Price type: 'open', 'close', 'low', 'high'", "title": "Price Type"}, "description": "Price type: 'open', 'close', 'low', 'high'"}, {"name": "duration", "in": "query", "required": true, "schema": {"type": "string", "description": "Duration (days): '1', '7', '14', '30'", "title": "Duration"}, "description": "Duration (days): '1', '7', '14', '30'"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/news/{ticker}": {"get": {"tags": ["News Articles"], "summary": "News By Topic", "description": "Get news a specific ticker.\n\nArgs:\n    ticker (str): Stock ticker symbol.\n    topic (str): Topic to fetch news for a specific stock.\n\nReturns:\n    dict: Relevant news for a speicific ticker.", "operationId": "news_by_topic_news__ticker__get", "parameters": [{"name": "ticker", "in": "path", "required": true, "schema": {"type": "string", "title": "Ticker"}}, {"name": "topic", "in": "query", "required": false, "schema": {"type": "string", "description": "Topic", "title": "Topic"}, "description": "Topic"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "Root", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}