#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户活动日志模型

该模块定义了用户活动日志相关的数据库模型。
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

# 避免循环导入，使用一个函数来获取Base
from db.models.base import get_base

class UserActivityLog(get_base()):
    """用户活动日志模型"""
    __tablename__ = "user_activity_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # 允许为空，因为有些活动可能不关联用户
    username = Column(String(50))  # 记录用户名，即使用户被删除也能知道是谁
    activity_type = Column(String(50), nullable=False, index=True)  # 活动类型，建立索引以便快速查询
    activity_level = Column(String(20), nullable=False, index=True)  # 活动级别：HIGH, MEDIUM, LOW
    activity_detail = Column(Text)  # 活动详情
    ip_address = Column(String(50))  # IP地址
    user_agent = Column(String(255))  # 用户代理
    timestamp = Column(DateTime, default=datetime.now, index=True)  # 时间戳，建立索引以便按时间查询

    def __repr__(self):
        return f"<UserActivityLog(id={self.id}, user_id={self.user_id}, activity_type='{self.activity_type}')>"
