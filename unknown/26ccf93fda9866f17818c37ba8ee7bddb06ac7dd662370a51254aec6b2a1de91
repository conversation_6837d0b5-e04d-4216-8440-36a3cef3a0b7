// 股票比较模块

// 全局变量
let comparisonChart = null;
let comparedStocks = [];

// 初始化股票比较功能
function initStockComparison() {
    // 添加比较按钮点击事件
    document.getElementById('compareStocksBtn').addEventListener('click', function() {
        const ticker = currentTicker;
        if (!ticker) {
            alert('请先搜索股票代码');
            return;
        }

        // 显示比较面板
        const comparisonPanel = document.getElementById('stockComparisonPanel');
        comparisonPanel.style.display = 'block';

        // 如果当前股票不在比较列表中，添加它
        if (!comparedStocks.includes(ticker)) {
            addStockToComparison(ticker);
        }

        // 更新比较图表
        updateComparisonChart();
    });

    // 关闭按钮点击事件
    document.getElementById('closeComparisonBtn').addEventListener('click', function() {
        document.getElementById('stockComparisonPanel').style.display = 'none';
    });

    // 添加股票按钮点击事件
    document.getElementById('addComparisonStockBtn').addEventListener('click', function() {
        const ticker = document.getElementById('comparisonTickerInput').value.trim().toUpperCase();
        if (!ticker) {
            alert('请输入股票代码');
            return;
        }

        // 检查是否已经在比较列表中
        if (comparedStocks.includes(ticker)) {
            alert('该股票已在比较列表中');
            return;
        }

        // 添加股票到比较列表
        addStockToComparison(ticker);

        // 清空输入框
        document.getElementById('comparisonTickerInput').value = '';

        // 更新比较图表
        updateComparisonChart();
    });
}

// 添加股票到比较列表
function addStockToComparison(ticker) {
    // 添加到比较列表
    comparedStocks.push(ticker);

    // 更新比较列表UI
    updateComparisonList();
}

// 从比较列表中移除股票
function removeStockFromComparison(ticker) {
    // 从比较列表中移除
    comparedStocks = comparedStocks.filter(stock => stock !== ticker);

    // 更新比较列表UI
    updateComparisonList();

    // 更新比较图表
    updateComparisonChart();
}

// 更新比较列表UI
function updateComparisonList() {
    const listContainer = document.getElementById('comparisonStocksList');
    listContainer.innerHTML = '';

    if (comparedStocks.length === 0) {
        listContainer.innerHTML = '<div class="text-center py-3">请添加股票进行比较</div>';
        return;
    }

    comparedStocks.forEach(ticker => {
        const item = document.createElement('div');
        item.className = 'comparison-stock-item d-flex justify-content-between align-items-center mb-2 p-2 border rounded';

        // 随机颜色
        const color = getRandomColor(ticker);

        item.innerHTML = `
            <div>
                <span class="comparison-color-indicator" style="background-color: ${color};"></span>
                <strong>${ticker}</strong>
            </div>
            <button class="btn btn-sm btn-outline-danger remove-comparison-btn" data-ticker="${ticker}">移除</button>
        `;

        listContainer.appendChild(item);
    });

    // 添加移除按钮事件
    document.querySelectorAll('.remove-comparison-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const ticker = this.getAttribute('data-ticker');
            removeStockFromComparison(ticker);
        });
    });
}

// 更新比较图表
async function updateComparisonChart() {
    if (comparedStocks.length === 0) {
        document.getElementById('comparisonChartContainer').innerHTML = '<div class="text-center py-5"><p class="text-muted">请添加股票进行比较</p></div>';
        return;
    }

    // 显示加载动画
    document.getElementById('comparisonChartContainer').innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 获取所有股票的数据
        const datasets = [];
        const promises = [];

        // 为每个股票获取数据
        comparedStocks.forEach(ticker => {
            promises.push(fetchStockComparisonData(ticker));
        });

        // 等待所有请求完成
        const results = await Promise.all(promises);

        // 处理结果
        results.forEach((data, index) => {
            const ticker = comparedStocks[index];
            const color = getRandomColor(ticker);

            datasets.push({
                label: ticker,
                data: data.normalizedPrices,
                borderColor: color,
                backgroundColor: color + '20', // 添加透明度
                borderWidth: 2,
                pointRadius: 0,
                pointHoverRadius: 5,
                fill: false
            });
        });

        // 创建图表容器
        document.getElementById('comparisonChartContainer').innerHTML = '<canvas id="comparisonChart" height="300"></canvas>';

        // 获取canvas上下文
        const ctx = document.getElementById('comparisonChart').getContext('2d');

        // 销毁旧图表
        if (comparisonChart) {
            comparisonChart.destroy();
        }

        // 创建新图表
        comparisonChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: results[0].dates,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '股票价格比较（归一化）'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value.toFixed(2) + '%';
                            }
                        }
                    }
                },
                // 禁用鼠标滚轮缩放，避免影响页面滚动
                events: ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'],
                hover: {
                    mode: 'nearest',
                    intersect: true
                }
            }
        });
    } catch (error) {
        console.error('更新比较图表失败:', error);
        document.getElementById('comparisonChartContainer').innerHTML = `<div class="text-center py-5"><p class="text-danger">更新比较图表失败: ${error.message}</p></div>`;
    }
}

// 获取股票比较数据
async function fetchStockComparisonData(ticker) {
    try {
        // 构建API URL
        const url = `${API_BASE_URL}/stock/${ticker}/chart?price_type=收盘&duration=30`;

        // 调用API
        const response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const data = await response.json();

        // 处理数据
        const dates = data.dates || [];
        const prices = data.values || [];

        // 归一化价格（转换为百分比变化）
        const normalizedPrices = [];
        if (prices.length > 0) {
            const basePrice = prices[0];
            for (let i = 0; i < prices.length; i++) {
                normalizedPrices.push((prices[i] / basePrice * 100) - 100);
            }
        }

        return {
            dates: dates,
            prices: prices,
            normalizedPrices: normalizedPrices
        };
    } catch (error) {
        console.error(`获取${ticker}比较数据失败:`, error);

        // 使用模拟数据
        return generateMockComparisonData(ticker);
    }
}

// 生成模拟比较数据
function generateMockComparisonData(ticker) {
    const dates = [];
    const prices = [];
    const normalizedPrices = [];

    // 生成日期（过去30天）
    const today = new Date();
    for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        dates.push(date.toISOString().split('T')[0]);
    }

    // 生成价格数据
    let basePrice = 100 + Math.random() * 100;
    for (let i = 0; i < 30; i++) {
        // 根据股票代码生成不同的价格走势
        const tickerSum = ticker.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
        const trend = (tickerSum % 3) - 1; // -1, 0, 或 1

        basePrice += (Math.random() - 0.5 + trend * 0.1) * 5;
        prices.push(basePrice);
    }

    // 归一化价格
    const firstPrice = prices[0];
    for (let i = 0; i < prices.length; i++) {
        normalizedPrices.push((prices[i] / firstPrice * 100) - 100);
    }

    return {
        dates: dates,
        prices: prices,
        normalizedPrices: normalizedPrices
    };
}

// 根据股票代码生成一致的随机颜色
function getRandomColor(ticker) {
    // 使用股票代码的字符和作为随机种子
    const seed = ticker.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);

    // 预定义的颜色列表
    const colors = [
        '#0d6efd', // 蓝色
        '#dc3545', // 红色
        '#fd7e14', // 橙色
        '#198754', // 绿色
        '#6f42c1', // 紫色
        '#20c997', // 青色
        '#0dcaf0', // 浅蓝色
        '#6c757d', // 灰色
        '#d63384', // 粉色
        '#ffc107'  // 黄色
    ];

    // 使用种子选择颜色
    return colors[seed % colors.length];
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否存在股票比较面板
    if (document.getElementById('stockComparisonPanel')) {
        initStockComparison();
    }
});
