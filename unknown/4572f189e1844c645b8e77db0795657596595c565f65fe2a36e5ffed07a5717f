import asyncio

from db.db_factory import DBFactory
from utils.logger import logger
# import yfinance as yf
from dotenv import load_dotenv
import os

class StockDataScraper:
    def __init__(self):
        self.db_client = self.initialize_db_client()

    @staticmethod
    def initialize_db_client():
        """
        Initialize the database client using the factory.

        Returns:
            object: Database client instance (SQLite or PostgreSQL)
        """
        load_dotenv()  # Load environment variables
        return DBFactory.get_db_client()

    def fetch_stock_data_sync(self, ticker, period='1mo'):
        """
        Synchronously fetches historical stock data for a given ticker.
        """
        # ticker_data = yf.Ticker(ticker)
        # return ticker_data.history(period=period)
        return None

    def insert_data_into_db(self, ticker, historical_data):
        """
        Inserts historical stock data for a given ticker into the database.
        """
        try:
            for date, row in historical_data.iterrows():
                data = {
                    "ticker": ticker,
                    "date": date.date(),
                    "open": row["Open"],
                    "high": row["High"],
                    "low": row["Low"],
                    "close": row["Close"],
                    "volume": row["Volume"],
                }
                self.db_client.create("stock_data", data)  # Assuming table is named `stock_data`
            logger.info(f"Data for {ticker} successfully inserted into the database.")
        except Exception as e:
            logger.error(f"Error inserting data for {ticker}: {e}")
            raise

    def scrape_all_tickers(self, tickers):
        """
        Fetches and stores stock data for all tickers.
        """
        for ticker in tickers:
            try:
                logger.info(f"Scraping data for {ticker}...")
                historical_data = self.fetch_stock_data_sync(ticker)
                self.insert_data_into_db(ticker, historical_data)
            except Exception as e:
                logger.error(f"Error scraping data for {ticker}: {e}")

# Example usage
if __name__ == "__main__":
    top_50_tickers = [
        "AAPL", "MSFT", "GOOG", "AMZN", "TSLA", "NVDA"
    ]

    scraper = StockDataScraper()
    scraper.scrape_all_tickers(top_50_tickers)
