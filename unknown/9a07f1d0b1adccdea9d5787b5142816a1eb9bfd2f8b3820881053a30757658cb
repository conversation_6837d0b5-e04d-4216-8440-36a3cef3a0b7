import unittest
import sys
import os
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入DataHub类
from rest_api.routes.datahub_routes import DataHub
from qtunnel import BarSize, DoRight, RunMode

class TestDataHub(unittest.TestCase):
    """测试DataHub类的各种方法"""

    @classmethod
    def setUpClass(cls):
        """在所有测试之前运行一次，初始化DataHub实例"""
        cls.data_hub = DataHub(
            data_path="d:/RoboQuant2",  # 请根据实际路径修改
            market="fut",
            period="day",
            block_name="main"
        )
        # 测试用的股票代码和期货代码
        cls.stock_code = "600000.SH"  # 浦发银行
        cls.index_code = "000001.SH"  # 上证指数
        cls.future_code = "IF9999.SF"  # 沪深300指数期货主力合约

    def test_get_hist_data(self):
        """测试获取历史数据"""
        # 测试日线数据
        df_day = self.data_hub.get_hist_data(self.stock_code, period="1mo", interval="1d")
        self.assertIsInstance(df_day, pd.DataFrame)
        self.assertFalse(df_day.empty, "日线数据不应为空")
        self.assertIn("datetime", df_day.columns)
        self.assertIn("open", df_day.columns)
        self.assertIn("close", df_day.columns)
        
        # 测试5分钟数据
        df_5min = self.data_hub.get_hist_data(self.stock_code, period="1d", interval="5m")
        self.assertIsInstance(df_5min, pd.DataFrame)
        # 注意：如果没有5分钟数据，这个测试可能会失败
        
        # 测试无效的时间间隔
        with self.assertRaises(ValueError):
            self.data_hub.get_hist_data(self.stock_code, period="1mo", interval="invalid")

    def test_get_kline_data(self):
        """测试获取K线数据"""
        # 测试日线数据
        df_day = self.data_hub.get_kline_data(self.stock_code, BarSize.day)
        self.assertIsInstance(df_day, pd.DataFrame)
        self.assertFalse(df_day.empty, "日线数据不应为空")
        self.assertIn("datetime", df_day.columns)
        self.assertIn("open", df_day.columns)
        self.assertIn("close", df_day.columns)
        
        # 测试5分钟数据
        df_5min = self.data_hub.get_kline_data(self.stock_code, BarSize.min5)
        self.assertIsInstance(df_5min, pd.DataFrame)
        
        # 测试前复权
        df_forward = self.data_hub.get_kline_data(self.stock_code, BarSize.day, do_right=DoRight.forward)
        self.assertIsInstance(df_forward, pd.DataFrame)

    def test_get_stock_info(self):
        """测试获取股票基本信息"""
        info = self.data_hub.get_stock_info(self.stock_code)
        self.assertIsInstance(info, dict)
        # 具体字段取决于数据源的实现

    def test_get_index_data(self):
        """测试获取指数数据"""
        df = self.data_hub.get_index_data(self.index_code)
        self.assertIsInstance(df, pd.DataFrame)
        self.assertFalse(df.empty, "指数数据不应为空")
        self.assertIn("datetime", df.columns)
        self.assertIn("close", df.columns)

    def test_get_industry_data(self):
        """测试获取行业指数数据"""
        # 这里使用一个示例行业代码，实际测试时需要替换为有效的行业代码
        industry_code = "BK0475"  # 银行业
        df = self.data_hub.get_industry_data(industry_code)
        self.assertIsInstance(df, pd.DataFrame)
        # 行业数据可能为空，取决于数据源

    @unittest.skip("暂时跳过行业成分股测试")
    def test_get_industry_stocks(self):
        """测试获取行业成分股"""
        # 这里使用一个示例行业代码，实际测试时需要替换为有效的行业代码
        industry_code = "BK0475"  # 银行业
        stocks = self.data_hub.get_industry_stocks(industry_code)
        self.assertIsInstance(stocks, list)
        # 行业成分股可能为空，取决于数据源

    def test_get_all_stocks(self):
        """测试获取所有股票代码"""
        stocks = self.data_hub.get_all_stocks()
        self.assertIsInstance(stocks, list)
        self.assertGreater(len(stocks), 0, "股票列表不应为空")

    def test_get_trading_dates(self):
        """测试获取交易日历"""
        # 获取最近30天的交易日历
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        dates = self.data_hub.get_trading_dates(start_date, end_date)
        self.assertIsInstance(dates, list)
        # 交易日历可能为空，取决于数据源和日期范围

    def test_get_concept_stocks(self):
        """测试获取概念股票"""
        # 这里使用一个示例概念代码，实际测试时需要替换为有效的概念代码
        concept_code = "BK0901"  # 人工智能
        stocks = self.data_hub.get_concept_stocks(concept_code)
        self.assertIsInstance(stocks, list)
        # 概念股可能为空，取决于数据源

    def test_get_all_concepts(self):
        """测试获取所有概念代码"""
        concepts = self.data_hub.get_all_concepts()
        self.assertIsInstance(concepts, list)
        # 概念列表可能为空，取决于数据源

    def test_get_financial_data(self):
        """测试获取财务数据"""
        # 测试资产负债表
        df_balance = self.data_hub.get_financial_data(self.stock_code, 'balance')
        self.assertIsInstance(df_balance, pd.DataFrame)
        
        # 测试利润表
        df_income = self.data_hub.get_financial_data(self.stock_code, 'income')
        self.assertIsInstance(df_income, pd.DataFrame)
        
        # 测试现金流量表
        df_cash_flow = self.data_hub.get_financial_data(self.stock_code, 'cash_flow')
        self.assertIsInstance(df_cash_flow, pd.DataFrame)

    def test_get_fund_holdings(self):
        """测试获取基金持仓数据"""
        # 这里使用一个示例基金代码，实际测试时需要替换为有效的基金代码
        fund_code = "000001"  # 华夏成长
        df = self.data_hub.get_fund_holdings(fund_code)
        self.assertIsInstance(df, pd.DataFrame)
        # 基金持仓数据可能为空，取决于数据源

    def test_get_margin_data(self):
        """测试获取融资融券数据"""
        df = self.data_hub.get_margin_data(self.stock_code)
        self.assertIsInstance(df, pd.DataFrame)
        # 融资融券数据可能为空，取决于数据源

    def test_get_dividend_data(self):
        """测试获取分红送转数据"""
        df = self.data_hub.get_dividend_data(self.stock_code)
        self.assertIsInstance(df, pd.DataFrame)
        # 分红送转数据可能为空，取决于数据源

    def test_get_index_weight(self):
        """测试获取指数成分权重"""
        df = self.data_hub.get_index_weight(self.index_code)
        self.assertIsInstance(df, pd.DataFrame)
        # 指数成分权重数据可能为空，取决于数据源

    def test_get_future_info(self):
        """测试获取期货合约信息"""
        info = self.data_hub.get_future_info(self.future_code)
        self.assertIsInstance(info, dict)
        # 具体字段取决于数据源的实现

    def test_get_option_info(self):
        """测试获取期权合约信息"""
        # 这里使用一个示例期权代码，实际测试时需要替换为有效的期权代码
        option_code = "10003000"
        info = self.data_hub.get_option_info(option_code)
        self.assertIsInstance(info, dict)
        # 具体字段取决于数据源的实现

    def test_get_block_data(self):
        """测试获取板块数据"""
        # 测试获取沪深300成分股
        hs300 = self.data_hub.ds.get_block_data("沪深300")
        self.assertIsInstance(hs300, list)
        self.assertGreater(len(hs300), 0, "沪深300成分股不应为空")
        
        # 测试获取中证500成分股
        zz500 = self.data_hub.ds.get_block_data("中证500")
        self.assertIsInstance(zz500, list)
        self.assertGreater(len(zz500), 0, "中证500成分股不应为空")

if __name__ == '__main__':
    unittest.main()
