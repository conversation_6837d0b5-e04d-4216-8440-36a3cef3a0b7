<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投资组合 - 智能体投顾助手</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../static/css/style.css">
    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
</head>
<body>
    <!-- 导航栏 -->
    {% include 'components/navbar.html' %}

    <div class="container mt-4">
        <!-- 欢迎区域 -->
        <div class="row mb-4" id="welcomeSection">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title">投资组合管理</h2>
                        <p class="card-text">本页面提供投资组合管理功能，您可以查看您的投资组合列表、详细持仓信息、历史业绩和统计指标。</p>
                        <div class="alert alert-info" id="loginAlert">
                            <i class="bi bi-info-circle"></i> 请先登录以查看您的投资组合
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 投资组合区域 -->
        <div class="row" id="portfolioSection" style="display: none;">
            <!-- 投资组合列表 -->
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">投资组合列表</h5>
                        <button class="btn btn-sm btn-primary" id="createPortfolioBtn">
                            <i class="bi bi-plus-circle"></i> 新建投资组合
                        </button>
                    </div>

                    <!-- 投资组合摘要表格 -->
                    <div class="card-body" id="portfolioSummarySection">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>组合名称</th>
                                        <th>账户余额</th>
                                        <th>单位净值</th>
                                        <th>总收益</th>
                                        <th>已实现盈亏</th>
                                        <th>日盈亏</th>
                                        <th>运行状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="portfolioSummaryBody">
                                    <!-- 投资组合摘要数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 空投资组合提示 -->
                    <div class="card-body" id="portfolioListEmpty" style="display: none;">
                        <div class="text-center py-4">
                            <p class="text-muted">您还没有创建任何投资组合</p>
                            <button class="btn btn-primary" id="createFirstPortfolioBtn">创建第一个投资组合</button>
                        </div>
                    </div>

                    <!-- 投资组合详情区域 -->
                    <div class="card-body" id="portfolioDetailSection" style="display: none;">
                        <h4 id="portfolioName" class="mb-4">投资组合名称</h4>

                        <!-- 投资组合概览 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">总资产</h6>
                                        <h4 id="totalValue" class="mb-0">¥0.00</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">现金余额</h6>
                                        <h4 id="cashBalance" class="mb-0">¥0.00</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">总收益</h6>
                                        <h4 id="totalPnl" class="mb-0 text-success">¥0.00</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">日收益</h6>
                                        <h4 id="dailyPnl" class="mb-0 text-success">¥0.00</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 策略控制区域 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="card-title mb-0">策略控制</h5>
                                            <div>
                                                <button class="btn btn-outline-primary me-2" id="toggleStrategyBtn">
                                                    <i class="bi bi-info-circle"></i> 显示策略信息
                                                </button>
                                                <button class="btn btn-primary" id="strategyBtn">
                                                    <i class="bi bi-gear"></i> 策略设置
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <p class="mb-1">策略状态</p>
                                                    <h6 id="strategyStatus">未运行</h6>
                                                </div>
                                                <div class="col-md-3">
                                                    <p class="mb-1">策略名称</p>
                                                    <h6 id="strategyName">未设置</h6>
                                                </div>
                                                <div class="col-md-3">
                                                    <p class="mb-1">模型名称</p>
                                                    <h6 id="modelName">未设置</h6>
                                                </div>
                                                <div class="col-md-3">
                                                    <p class="mb-1">更新时间</p>
                                                    <h6 id="updateTime">-</h6>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 策略详细信息区域 -->
                                        <div id="strategyInfoSection" style="display: none;" class="mt-3">
                                            <hr>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6>策略详情</h6>
                                                    <div class="mb-3">
                                                        <p class="mb-1">策略名称</p>
                                                        <div class="text-muted" id="strategyNameDisplay">未设置</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <p class="mb-1">模型名称</p>
                                                        <div class="text-muted" id="modelNameDisplay">未设置</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <p class="mb-1">运行状态</p>
                                                        <span class="badge bg-light text-dark" id="strategyStatusBadge">未启用</span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>策略描述</h6>
                                                    <div class="text-muted" id="strategyDescriptionDisplay">未设置</div>
                                                    <h6 class="mt-3">策略参数</h6>
                                                    <pre class="bg-light p-2 rounded" id="strategyParamsDisplay">未设置参数</pre>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 业绩图表区域 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">业绩走势</h5>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-secondary active" data-period="1m">1月</button>
                                                <button class="btn btn-sm btn-outline-secondary" data-period="3m">3月</button>
                                                <button class="btn btn-sm btn-outline-secondary" data-period="6m">6月</button>
                                                <button class="btn btn-sm btn-outline-secondary" data-period="1y">1年</button>
                                                <button class="btn btn-sm btn-outline-secondary" data-period="all">全部</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="performanceChartContainer">
                                            <div id="performanceChart" style="height: 400px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 统计指标 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">统计指标</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <p class="mb-1">总收益率</p>
                                                <h5 id="totalReturn" class="mb-0">0.00%</h5>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1">年化收益率</p>
                                                <h5 id="annualReturn" class="mb-0">0.00%</h5>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1">夏普比率</p>
                                                <h5 id="sharpeRatio" class="mb-0">0.00</h5>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1">最大回撤率</p>
                                                <h5 id="maxDrawdown" class="mb-0">0.00%</h5>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-3">
                                                <p class="mb-1">波动率</p>
                                                <h5 id="volatility" class="mb-0">0.00%</h5>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1">索提诺比率</p>
                                                <h5 id="sortinoRatio" class="mb-0">0.00</h5>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1">阿尔法</p>
                                                <h5 id="alpha" class="mb-0">0.00</h5>
                                            </div>
                                            <div class="col-md-3">
                                                <p class="mb-1">贝塔</p>
                                                <h5 id="beta" class="mb-0">0.00</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 持仓明细和订单历史标签页 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <ul class="nav nav-tabs card-header-tabs" id="positionOrderTabs" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="positions-tab" data-bs-toggle="tab" data-bs-target="#positions-content" type="button" role="tab" aria-controls="positions-content" aria-selected="true">持仓明细</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders-content" type="button" role="tab" aria-controls="orders-content" aria-selected="false">订单历史</button>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="card-body">
                                        <div class="tab-content" id="positionOrderTabContent">
                                            <!-- 持仓明细标签页内容 -->
                                            <div class="tab-pane fade show active" id="positions-content" role="tabpanel" aria-labelledby="positions-tab">
                                                <!-- 股票持仓表格 -->
                                                <div class="table-responsive" id="stockPositionsTable">
                                                    <table class="table table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th>标的</th>
                                                                <th>名称</th>
                                                                <th>数量</th>
                                                                <th>平均成本</th>
                                                                <th>交易费用</th>
                                                                <th>盈亏</th>
                                                                <th>成本ATR</th>
                                                                <th>回撤ATR</th>
                                                                <th>操作</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="stockPositionsBody">
                                                            <!-- 股票持仓数据将通过JavaScript动态加载 -->
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <!-- 期货持仓表格 -->
                                                <div class="table-responsive" id="futurePositionsTable" style="display: none;">
                                                    <table class="table table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th>标的</th>
                                                                <th>名称</th>
                                                                <th>方向</th>
                                                                <th>数量</th>
                                                                <th>平均成本</th>
                                                                <th>交易费用</th>
                                                                <th>盈亏</th>
                                                                <th>成本ATR</th>
                                                                <th>回撤ATR</th>
                                                                <th>操作</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="futurePositionsBody">
                                                            <!-- 期货持仓数据将通过JavaScript动态加载 -->
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                            <!-- 订单历史标签页内容 -->
                                            <div class="tab-pane fade" id="orders-content" role="tabpanel" aria-labelledby="orders-tab">
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th>标的</th>
                                                                <th>名称</th>
                                                                <th>价格</th>
                                                                <th>数量</th>
                                                                <th>方向</th>
                                                                <th>开平仓</th>
                                                                <th>盈亏</th>
                                                                <th>时间</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="ordersBody">
                                                            <!-- 订单数据将通过JavaScript动态加载 -->
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 策略设置模态框 -->
    <div class="modal fade" id="strategyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">策略设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="strategyForm">
                        <div class="mb-3">
                            <label class="form-label">策略状态</label>
                            <select class="form-select" id="strategyRunState">
                                <option value="RUNNING">运行</option>
                                <option value="STOPPED">停止</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">策略名称</label>
                            <select class="form-select" id="strategyNameSelect">
                                <option value="TREND">趋势跟踪</option>
                                <option value="GRID">网格交易</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">预测模型</label>
                            <select class="form-select" id="modelNameSelect">
                                <!-- 模型选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">策略参数</label>
                            <textarea class="form-control" id="strategyParams" rows="5" placeholder="请输入JSON格式的策略参数"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">策略描述</label>
                            <textarea class="form-control" id="strategyDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveStrategyBtn">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                            <div class="text-end mt-1">
                                <a href="#" id="forgotPasswordLink" class="small">忘记密码？</a>
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">登录</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="mb-3">
                            <label for="registerUsername" class="form-label">用户名</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="registerUsername" required>
                                <button class="btn btn-outline-secondary" type="button" id="generateUsernameBtn">生成用户名</button>
                            </div>
                            <div class="form-text" id="usernameHelp">用户名不能重复，点击生成按钮可自动生成用户名</div>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">电子邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerFullName" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="registerFullName">
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerConfirmPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="registerConfirmPassword" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">注册</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建投资组合模态框 -->
    <div class="modal fade" id="createPortfolioModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新投资组合</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createPortfolioForm">
                        <div class="mb-3">
                            <label for="portfolioNameInput" class="form-label">投资组合名称</label>
                            <input type="text" class="form-control" id="portfolioNameInput" required>
                        </div>
                        <div class="mb-3">
                            <label for="portfolioDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="portfolioDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="initialCash" class="form-label">初始资金</label>
                            <div class="input-group">
                                <span class="input-group-text">¥</span>
                                <input type="number" class="form-control" id="initialCash" value="100000" min="1000" step="1000" required>
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">创建投资组合</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 Toast -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="loadingToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <strong class="me-auto">正在处理</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="loadingToastMessage">
                正在加载数据...
            </div>
        </div>
    </div>

    <!-- 升级订阅模态框 -->
    <div class="modal fade" id="upgradeSubscriptionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">升级订阅</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="bi bi-lock-fill text-warning" style="font-size: 3rem;"></i>
                        <h4 class="mt-3">功能限制</h4>
                        <p class="text-muted">创建投资组合功能仅对进阶版和专业版用户开放</p>
                    </div>

                    <div class="row g-3 mb-4">
                        <!-- 进阶版 -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white text-center">
                                    <h5 class="mb-0">进阶版</h5>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h4 class="card-title text-center mb-3">¥199<small>/月</small></h4>
                                    <ul class="list-unstyled mb-4">
                                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>创建最多5个投资组合</li>
                                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>使用高级技术指标</li>
                                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>实时行情数据</li>
                                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>多策略支持</li>
                                    </ul>
                                    <div class="mt-auto">
                                        <button class="btn btn-primary w-100" id="upgradeToAdvancedBtn">升级到进阶版</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 专业版 -->
                        <div class="col-md-6">
                            <div class="card h-100 border-primary">
                                <div class="card-header bg-primary text-white text-center">
                                    <h5 class="mb-0">专业版</h5>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h4 class="card-title text-center mb-3">¥599<small>/月</small></h4>
                                    <ul class="list-unstyled mb-4">
                                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>无限投资组合</li>
                                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>全部技术指标</li>
                                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>专业AI预测</li>
                                        <li class="mb-2"><i class="bi bi-check-circle-fill text-success me-2"></i>优先客户支持</li>
                                    </ul>
                                    <div class="mt-auto">
                                        <button class="btn btn-primary w-100" id="upgradeToProfessionalBtn">升级到专业版</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">暂不升级</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 智能体投顾助手. 保留所有权利.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../static/js/config.js"></script>
    <script src="../static/js/auth-interceptor.js"></script>
    <script src="../static/js/navbar.js"></script>
    <script src="../static/js/portfolio.js"></script>
</body>
</html>

