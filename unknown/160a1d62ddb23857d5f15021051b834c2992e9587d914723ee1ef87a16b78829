import unittest
from datetime import datetime
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from web_app.data_fetcher_akshare import (
    format_stock_code,
    get_stock_info,
    get_stock_data,
    get_candlestick_data,
    get_intraday_data
)

class TestDataFetcherAkshare(unittest.TestCase):
    """测试数据获取模块的测试类"""

    def setUp(self):
        """测试前的设置"""
        self.test_stock_codes = [
            '600000',  # 浦发银行
            'sh000001',  # 上证指数
            # 'sz000001',  # 平安银行
            # '300750',  # 宁德时代
            # 'BABA'     # 阿里巴巴
        ]
        # 在每次API调用之间添加延时，避免请求过于频繁
        self.api_delay = 1

    def tearDown(self):
        """测试后的清理"""
        time.sleep(self.api_delay)  # 每个测试用例结束后等待一段时间

    def test_format_stock_code(self):
        """测试股票代码格式化函数"""
        test_cases = [
            ('600000', 'sh600000'),    # 上海股票
            ('000001', 'sz000001'),    # 深圳股票
            ('300750', 'sz300750'),    # 创业板
            ('430047', 'bj430047'),    # 北交所
            ('BABA', 'sh9988'),        # 美股转换
            ('sh600000', 'sh600000'),  # 已有前缀
            ('sz000001', 'sz000001'),  # 已有前缀
            ('999999', 'sh000001')     # 无效代码
        ]
        
        for input_code, expected_output in test_cases:
            with self.subTest(input_code=input_code):
                self.assertEqual(format_stock_code(input_code), expected_output)

    def test_get_stock_info(self):
        """测试获取股票信息函数"""
        for code in self.test_stock_codes:
            with self.subTest(code=code):
                result = get_stock_info(code)
                
                # 验证返回的数据结构
                self.assertIsInstance(result, dict)
                
                # 验证必要字段
                required_fields = [
                    'code', 'name', 'price', 'change', 'changePercent',
                    'open', 'high', 'low', 'volume', 'amount'
                ]
                for field in required_fields:
                    self.assertIn(field, result)
                
                # 验证数据有效性
                self.assertGreater(result['price'], 0)
                self.assertGreater(result['volume'], 0)
                self.assertGreater(result['amount'], 0)
                
                time.sleep(self.api_delay)

    def test_get_stock_data(self):
        """测试获取股票历史数据函数"""
        periods = ['1d', '5d', '1mo']
        intervals = ['1d', '5m']
        
        for code in self.test_stock_codes[:2]:  # 只测试部分股票代码
            for period in periods:
                for interval in intervals:
                    with self.subTest(code=code, period=period, interval=interval):
                        result = get_stock_data(code, period, interval)
                        
                        # 验证返回的数据结构
                        self.assertIsInstance(result, dict)
                        self.assertIn('dates', result)
                        self.assertIn('values', result)
                        
                        # 验证数据有效性
                        self.assertGreater(len(result['dates']), 0)
                        self.assertEqual(len(result['dates']), len(result['values']))
                        
                        # 验证日期是否按顺序排列
                        # self.assertEqual(result['dates'], sorted(result['dates']))
                        
                        time.sleep(self.api_delay)

    def test_get_candlestick_data(self):
        """测试获取K线图数据函数"""
        periods = ['5d', '1mo']
        intervals = ['5m', '1d']
        
        for code in self.test_stock_codes[:2]:  # 只测试部分股票代码
            for period in periods:
                for interval in intervals:
                    with self.subTest(code=code, period=period, interval=interval):
                        result = get_candlestick_data(code, period, interval)
                        
                        # 验证返回的数据结构
                        self.assertIsInstance(result, list)
                        self.assertGreater(len(result), 0)
                        
                        # 验证每个数据点的格式
                        for item in result:
                            self.assertIn('time', item)
                            self.assertIn('open', item)
                            self.assertIn('high', item)
                            self.assertIn('low', item)
                            self.assertIn('close', item)
                            self.assertIn('volume', item)
                            
                            # 验证价格逻辑
                            self.assertGreaterEqual(item['high'], item['open'])
                            self.assertGreaterEqual(item['high'], item['close'])
                            self.assertLessEqual(item['low'], item['open'])
                            self.assertLessEqual(item['low'], item['close'])
                        
                        # 验证时间序列是否按顺序
                        # times = [item['time'] for item in result]
                        # self.assertEqual(times, sorted(times))
                        
                        time.sleep(self.api_delay)

    @unittest.skip("skip intraday data test")
    def test_get_intraday_data(self):
        """测试获取日内数据函数"""
        # 仅在交易时间内测试
        now = datetime.now()
        hour = now.hour
        minute = now.minute
        is_trading_time = (
            (9 <= hour < 11) or 
            (hour == 11 and minute <= 30) or 
            (13 <= hour < 15)
        )
        
        if not is_trading_time:
            self.skipTest("非交易时间，跳过日内数据测试")
        
        for code in self.test_stock_codes[:2]:  # 只测试部分股票代码
            with self.subTest(code=code):
                result = get_intraday_data(code)
                
                # 验证返回的数据结构
                self.assertIsInstance(result, list)
                self.assertGreater(len(result), 0)
                
                # 验证每个数据点的格式
                for item in result:
                    self.assertIn('time', item)
                    self.assertIn('value', item)
                    self.assertGreater(item['value'], 0)
                
                # 验证时间序列是否按顺序
                times = [item['time'] for item in result]
                self.assertEqual(times, sorted(times))
                
                time.sleep(self.api_delay)

    @unittest.skip("skip error handling test")
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的股票代码
        invalid_codes = ['000000', 'INVALID', '99999999']
        
        for code in invalid_codes:
            with self.subTest(code=code):
                # 测试get_stock_info的错误处理
                result = get_stock_info(code)
                self.assertIsInstance(result, dict)
                self.assertIn('code', result)
                self.assertIn('name', result)
                
                # 测试get_stock_data的错误处理
                result = get_stock_data(code)
                self.assertIsInstance(result, dict)
                self.assertIn('dates', result)
                self.assertIn('values', result)
                
                time.sleep(self.api_delay)

if __name__ == '__main__':
    unittest.main(verbosity=2)