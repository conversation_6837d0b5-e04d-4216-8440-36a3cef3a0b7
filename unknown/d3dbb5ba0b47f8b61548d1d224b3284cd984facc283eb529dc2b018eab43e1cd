"""
<PERSON> agent for the AI Hedge Fund module.

This agent analyzes stocks using <PERSON>'s investment principles,
particularly his reflexivity theory and macro trend trading approach.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json
import numpy as np

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_financial_metrics, get_market_cap, search_line_items, get_price_data
from ..tools.macro_data import get_macro_indicators, analyze_macro_trends
from ..utils.llm import call_llm


class SorosSignal(BaseModel):
    """Signal generated by the <PERSON> agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    reflexivity_analysis: str = Field(description="Analysis of reflexive dynamics")
    macro_trend: str = Field(description="Identified macro trend")
    boom_bust_phase: str = Field(description="Current phase in boom-bust cycle")


def george_soros_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using <PERSON>'s principles and LLM reasoning.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with <PERSON>'s analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Define the macro indicators to analyze
    indicators = [
        "GDP",
        "CPI",
        "UNEMPLOYMENT",
        "INTEREST_RATE",
    ]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    soros_analysis = {}
    
    # Update progress status if available
    if "progress" in state["metadata"]:
        for ticker in tickers:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Fetching macro data")
    
    # Fetch macro data
    macro_data = get_macro_indicators(indicators, start_date, end_date)
    
    # Analyze macro trends
    macro_trend_analysis = analyze_macro_trends(macro_data)
    
    # Determine current macro environment
    macro_environment = determine_macro_environment(macro_trend_analysis)
    
    # Store macro analysis
    analysis_data["macro_trends"] = macro_trend_analysis
    analysis_data["macro_environment"] = macro_environment
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Fetching financial metrics")
        
        # Fetch required data
        metrics = get_financial_metrics(ticker, end_date, period="ttm", limit=5)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Gathering financial line items")
        
        financial_line_items = search_line_items(
            ticker,
            [
                "revenue",
                "net_income",
                "earnings_per_share",
                "total_assets",
                "total_liabilities",
                "cash_and_cash_equivalents",
                "long_term_debt",
                "inventory",
                "accounts_receivable",
                "research_and_development",
            ],
            end_date,
            limit=5,  # Get 5 years of data for trend analysis
        )
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Getting price data")
        
        # Get price data
        price_data = get_price_data(ticker, start_date, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Getting market cap")
        
        # Get current market cap
        market_cap = get_market_cap(ticker, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Analyzing reflexivity")
        
        # Analyze reflexivity dynamics
        reflexivity_analysis = analyze_reflexivity(ticker, price_data, financial_line_items)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Analyzing boom-bust cycle")
        
        # Analyze boom-bust cycle
        boom_bust_analysis = analyze_boom_bust_cycle(ticker, price_data, macro_environment)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Analyzing trend alignment")
        
        # Analyze trend alignment
        trend_alignment = analyze_trend_alignment(ticker, price_data, macro_environment)
        
        # Calculate total score
        total_score = (
            reflexivity_analysis["score"] + 
            boom_bust_analysis["score"] + 
            trend_alignment["score"]
        )
        
        max_possible_score = (
            reflexivity_analysis["max_score"] + 
            boom_bust_analysis["max_score"] + 
            trend_alignment["max_score"]
        )
        
        # Generate trading signal based on score
        if total_score >= 0.7 * max_possible_score:
            signal = "bullish"
        elif total_score <= 0.3 * max_possible_score:
            signal = "bearish"
        else:
            signal = "neutral"
        
        # Combine all analysis results
        analysis_data[ticker] = {
            "signal": signal,
            "score": total_score,
            "max_score": max_possible_score,
            "reflexivity_analysis": reflexivity_analysis,
            "boom_bust_analysis": boom_bust_analysis,
            "trend_alignment": trend_alignment,
            "market_cap": market_cap,
            "macro_environment": macro_environment,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Generating Soros analysis")
        
        # Generate detailed analysis using LLM
        soros_output = generate_soros_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        soros_analysis[ticker] = {
            "signal": soros_output.signal,
            "confidence": soros_output.confidence,
            "reasoning": soros_output.reasoning,
            "reflexivity_analysis": soros_output.reflexivity_analysis,
            "macro_trend": soros_output.macro_trend,
            "boom_bust_phase": soros_output.boom_bust_phase,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("george_soros_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(soros_analysis, "George Soros Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["george_soros_agent"] = soros_analysis
    
    return state


def determine_macro_environment(macro_trend_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    Determine the current macro environment based on macro trends.
    
    Args:
        macro_trend_analysis: Analysis of macro trends
        
    Returns:
        Dictionary with macro environment assessment
    """
    # Extract trend directions
    gdp_trend = macro_trend_analysis.get("GDP", {}).get("trend_direction", "stable")
    inflation_trend = macro_trend_analysis.get("CPI", {}).get("trend_direction", "stable")
    interest_rate_trend = macro_trend_analysis.get("INTEREST_RATE", {}).get("trend_direction", "stable")
    
    # Determine economic environment
    if gdp_trend == "increasing":
        if inflation_trend == "increasing":
            economic_phase = "expansion_with_inflation"
        else:
            economic_phase = "healthy_expansion"
    elif gdp_trend == "decreasing":
        if inflation_trend == "increasing":
            economic_phase = "stagflation"
        else:
            economic_phase = "recession"
    else:  # stable
        if inflation_trend == "increasing":
            economic_phase = "inflationary_pressure"
        else:
            economic_phase = "stable_economy"
    
    # Determine monetary policy stance
    if interest_rate_trend == "increasing":
        monetary_policy = "tightening"
    elif interest_rate_trend == "decreasing":
        monetary_policy = "easing"
    else:
        monetary_policy = "neutral"
    
    # Determine market regime
    if economic_phase in ["healthy_expansion", "expansion_with_inflation"] and monetary_policy != "tightening":
        market_regime = "risk_on"
    elif economic_phase in ["recession", "stagflation"] or monetary_policy == "tightening":
        market_regime = "risk_off"
    else:
        market_regime = "neutral"
    
    return {
        "economic_phase": economic_phase,
        "monetary_policy": monetary_policy,
        "market_regime": market_regime,
        "gdp_trend": gdp_trend,
        "inflation_trend": inflation_trend,
        "interest_rate_trend": interest_rate_trend,
    }


def analyze_reflexivity(ticker: str, price_data: Any, financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze reflexivity dynamics with George Soros's perspective.
    
    Args:
        ticker: Stock ticker symbol
        price_data: Historical price data
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with reflexivity analysis results
    """
    score = 0
    max_score = 5
    reasoning = []
    
    # Check if we have enough data
    if price_data.empty or not financial_line_items or len(financial_line_items) < 3:
        return {
            "score": 0,
            "max_score": max_score,
            "details": "Insufficient data for reflexivity analysis",
            "reflexivity_type": "unknown",
        }
    
    # Extract price and fundamental data
    prices = price_data["close"].values
    
    # Calculate price momentum (rate of change)
    if len(prices) > 20:
        price_momentum_short = (prices[-1] / prices[-10] - 1) * 100  # 10-day momentum
        price_momentum_long = (prices[-1] / prices[-60] - 1) * 100 if len(prices) > 60 else 0  # 60-day momentum
    else:
        price_momentum_short = 0
        price_momentum_long = 0
    
    # Extract fundamental data
    revenue_growth = []
    eps_growth = []
    
    for i in range(len(financial_line_items) - 1):
        current = financial_line_items[i]
        previous = financial_line_items[i + 1]
        
        if hasattr(current, "revenue") and hasattr(previous, "revenue") and previous.revenue:
            revenue_growth.append((current.revenue / previous.revenue - 1) * 100)
        
        if hasattr(current, "earnings_per_share") and hasattr(previous, "earnings_per_share") and previous.earnings_per_share:
            eps_growth.append((current.earnings_per_share / previous.earnings_per_share - 1) * 100)
    
    # Calculate average growth rates
    avg_revenue_growth = np.mean(revenue_growth) if revenue_growth else 0
    avg_eps_growth = np.mean(eps_growth) if eps_growth else 0
    
    # Detect reflexivity patterns
    
    # 1. Positive reflexivity: Price momentum accelerating and fundamentals improving
    if price_momentum_short > 5 and price_momentum_short > price_momentum_long and avg_revenue_growth > 10 and avg_eps_growth > 10:
        score += 2
        reasoning.append("Positive reflexivity detected: Price momentum accelerating with strong fundamental improvement")
        reflexivity_type = "positive"
    
    # 2. Negative reflexivity: Price momentum deteriorating and fundamentals weakening
    elif price_momentum_short < -5 and price_momentum_short < price_momentum_long and avg_revenue_growth < 0 and avg_eps_growth < 0:
        score -= 2
        reasoning.append("Negative reflexivity detected: Price momentum deteriorating with weakening fundamentals")
        reflexivity_type = "negative"
    
    # 3. Potential reflexivity inflection: Divergence between price and fundamentals
    elif (price_momentum_short > 10 and avg_eps_growth < 0) or (price_momentum_short < -10 and avg_eps_growth > 10):
        score += 0  # Neutral but important to note
        reasoning.append("Potential reflexivity inflection point: Divergence between price momentum and fundamentals")
        reflexivity_type = "inflection"
    
    # 4. Stable/No reflexivity: Price and fundamentals aligned without acceleration
    else:
        reasoning.append("No significant reflexivity dynamics detected")
        reflexivity_type = "stable"
    
    # Check for price acceleration/deceleration
    if len(prices) > 60:
        recent_volatility = np.std(prices[-20:]) / np.mean(prices[-20:]) * 100
        longer_volatility = np.std(prices[-60:]) / np.mean(prices[-60:]) * 100
        
        if recent_volatility > longer_volatility * 1.5:
            score += 1
            reasoning.append(f"Increasing price volatility detected (recent: {recent_volatility:.2f}%, longer: {longer_volatility:.2f}%)")
        
        # Check for parabolic price movement (potential bubble)
        if price_momentum_short > 20 and price_momentum_short > price_momentum_long * 2:
            score -= 1
            reasoning.append("Potential bubble formation: Parabolic price movement detected")
    
    # Check for fundamental acceleration/deceleration
    if len(revenue_growth) >= 2:
        if revenue_growth[0] > revenue_growth[1] * 1.5:
            score += 1
            reasoning.append("Accelerating revenue growth detected")
        elif revenue_growth[0] < revenue_growth[1] * 0.5:
            score -= 1
            reasoning.append("Decelerating revenue growth detected")
    
    # Adjust score based on reflexivity type
    if reflexivity_type == "positive":
        score = max(1, score)  # Ensure positive score for positive reflexivity
    elif reflexivity_type == "negative":
        score = min(-1, score)  # Ensure negative score for negative reflexivity
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
        "reflexivity_type": reflexivity_type,
    }


def analyze_boom_bust_cycle(ticker: str, price_data: Any, macro_environment: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze boom-bust cycle with George Soros's perspective.
    
    Args:
        ticker: Stock ticker symbol
        price_data: Historical price data
        macro_environment: Current macro environment
        
    Returns:
        Dictionary with boom-bust cycle analysis results
    """
    score = 0
    max_score = 3
    reasoning = []
    
    # Check if we have enough data
    if price_data.empty or len(price_data) < 60:
        return {
            "score": 0,
            "max_score": max_score,
            "details": "Insufficient data for boom-bust cycle analysis",
            "cycle_phase": "unknown",
        }
    
    # Extract price data
    prices = price_data["close"].values
    
    # Calculate various metrics
    if len(prices) > 252:  # At least 1 year of data
        # Calculate 52-week high and low
        year_high = np.max(prices[-252:])
        year_low = np.min(prices[-252:])
        current_price = prices[-1]
        
        # Calculate distance from 52-week high and low
        distance_from_high = (year_high - current_price) / year_high * 100
        distance_from_low = (current_price - year_low) / year_low * 100
        
        # Calculate recent trend (last 3 months)
        recent_trend = (current_price / prices[-63] - 1) * 100 if len(prices) > 63 else 0
    else:
        # Use available data
        year_high = np.max(prices)
        year_low = np.min(prices)
        current_price = prices[-1]
        distance_from_high = (year_high - current_price) / year_high * 100
        distance_from_low = (current_price - year_low) / year_low * 100
        recent_trend = (current_price / prices[0] - 1) * 100
    
    # Determine cycle phase based on price action and macro environment
    
    # 1. Early boom phase
    if distance_from_low < 30 and distance_from_high > 30 and recent_trend > 0 and macro_environment["economic_phase"] in ["healthy_expansion", "stable_economy"]:
        cycle_phase = "early_boom"
        score += 2
        reasoning.append(f"Early boom phase detected: {distance_from_low:.1f}% from low, positive recent trend of {recent_trend:.1f}%")
    
    # 2. Late boom phase
    elif distance_from_high < 10 and recent_trend > 0 and macro_environment["economic_phase"] in ["expansion_with_inflation", "inflationary_pressure"]:
        cycle_phase = "late_boom"
        score += 0  # Neutral - opportunity but with increasing risk
        reasoning.append(f"Late boom phase detected: Near 52-week high ({distance_from_high:.1f}% from high), with {recent_trend:.1f}% recent trend")
    
    # 3. Early bust phase
    elif distance_from_high > 10 and distance_from_high < 30 and recent_trend < 0 and macro_environment["monetary_policy"] == "tightening":
        cycle_phase = "early_bust"
        score -= 1
        reasoning.append(f"Early bust phase detected: {distance_from_high:.1f}% from high with negative recent trend of {recent_trend:.1f}%")
    
    # 4. Late bust phase
    elif distance_from_high > 30 and recent_trend < 0 and macro_environment["economic_phase"] in ["recession", "stagflation"]:
        cycle_phase = "late_bust"
        score += 1  # Slightly positive - approaching potential bottom
        reasoning.append(f"Late bust phase detected: {distance_from_high:.1f}% from high, potential bottoming process")
    
    # 5. Recovery phase
    elif distance_from_high > 20 and recent_trend > 0 and macro_environment["monetary_policy"] == "easing":
        cycle_phase = "recovery"
        score += 2
        reasoning.append(f"Recovery phase detected: {distance_from_high:.1f}% from high but positive recent trend of {recent_trend:.1f}%")
    
    # 6. Indeterminate phase
    else:
        cycle_phase = "indeterminate"
        reasoning.append("Indeterminate cycle phase: Mixed signals in price action and macro environment")
    
    # Consider monetary policy impact
    if macro_environment["monetary_policy"] == "easing" and cycle_phase in ["early_bust", "late_bust"]:
        score += 1
        reasoning.append("Accommodative monetary policy during bust phase is positive")
    elif macro_environment["monetary_policy"] == "tightening" and cycle_phase in ["late_boom", "early_boom"]:
        score -= 1
        reasoning.append("Tightening monetary policy during boom phase is negative")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
        "cycle_phase": cycle_phase,
    }


def analyze_trend_alignment(ticker: str, price_data: Any, macro_environment: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze trend alignment with George Soros's perspective.
    
    Args:
        ticker: Stock ticker symbol
        price_data: Historical price data
        macro_environment: Current macro environment
        
    Returns:
        Dictionary with trend alignment analysis results
    """
    score = 0
    max_score = 3
    reasoning = []
    
    # Check if we have enough data
    if price_data.empty or len(price_data) < 20:
        return {
            "score": 0,
            "max_score": max_score,
            "details": "Insufficient data for trend alignment analysis",
            "trend_direction": "unknown",
        }
    
    # Extract price data
    prices = price_data["close"].values
    
    # Calculate moving averages
    if len(prices) >= 200:
        ma50 = np.mean(prices[-50:])
        ma200 = np.mean(prices[-200:])
        
        # Determine trend based on moving average relationship
        if prices[-1] > ma50 > ma200:
            trend_direction = "strong_uptrend"
            score += 2
            reasoning.append(f"Strong uptrend: Price > MA50 > MA200 (Price: {prices[-1]:.2f}, MA50: {ma50:.2f}, MA200: {ma200:.2f})")
        elif prices[-1] > ma50 and ma50 < ma200:
            trend_direction = "early_uptrend"
            score += 1
            reasoning.append(f"Early uptrend: Price > MA50 but MA50 < MA200 (Price: {prices[-1]:.2f}, MA50: {ma50:.2f}, MA200: {ma200:.2f})")
        elif prices[-1] < ma50 < ma200:
            trend_direction = "strong_downtrend"
            score -= 2
            reasoning.append(f"Strong downtrend: Price < MA50 < MA200 (Price: {prices[-1]:.2f}, MA50: {ma50:.2f}, MA200: {ma200:.2f})")
        elif prices[-1] < ma50 and ma50 > ma200:
            trend_direction = "early_downtrend"
            score -= 1
            reasoning.append(f"Early downtrend: Price < MA50 but MA50 > MA200 (Price: {prices[-1]:.2f}, MA50: {ma50:.2f}, MA200: {ma200:.2f})")
        else:
            trend_direction = "neutral"
            reasoning.append(f"Neutral trend: Mixed moving average signals (Price: {prices[-1]:.2f}, MA50: {ma50:.2f}, MA200: {ma200:.2f})")
    else:
        # Use simple trend analysis for limited data
        short_term = (prices[-1] / prices[-min(20, len(prices)):].mean() - 1) * 100
        
        if short_term > 5:
            trend_direction = "uptrend"
            score += 1
            reasoning.append(f"Uptrend detected: {short_term:.1f}% above short-term average")
        elif short_term < -5:
            trend_direction = "downtrend"
            score -= 1
            reasoning.append(f"Downtrend detected: {short_term:.1f}% below short-term average")
        else:
            trend_direction = "neutral"
            reasoning.append(f"Neutral trend: {short_term:.1f}% from short-term average")
    
    # Check alignment with macro environment
    market_regime = macro_environment["market_regime"]
    
    if (trend_direction in ["strong_uptrend", "early_uptrend"] and market_regime == "risk_on") or \
       (trend_direction in ["strong_downtrend", "early_downtrend"] and market_regime == "risk_off"):
        score += 1
        reasoning.append(f"Trend aligned with macro environment: {trend_direction} in {market_regime} regime")
    elif (trend_direction in ["strong_uptrend", "early_uptrend"] and market_regime == "risk_off") or \
         (trend_direction in ["strong_downtrend", "early_downtrend"] and market_regime == "risk_on"):
        score -= 1
        reasoning.append(f"Trend misaligned with macro environment: {trend_direction} in {market_regime} regime")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
        "trend_direction": trend_direction,
    }


def generate_soros_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> SorosSignal:
    """
    Generate detailed analysis using LLM with George Soros's principles.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        SorosSignal object with the investment decision
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a George Soros AI agent. Decide on investment signals based on George Soros's principles:
    - Reflexivity Theory: Markets are inherently biased and self-reinforcing feedback loops exist between prices and fundamentals
    - Boom-Bust Cycles: Markets move in cycles driven by credit, sentiment, and policy responses
    - Macro Trend Trading: Identify and trade with major macro trends, but be willing to reverse when trends change
    - Inflection Points: Focus on identifying turning points in markets where consensus is wrong
    - Risk Management: Take large positions when high-conviction opportunities arise, but manage risk carefully
    - Fallibility: Recognize that all analyses are inherently imperfect and subject to revision

    When providing your reasoning, be thorough and specific by:
    1. Analyzing reflexivity dynamics between market perception and fundamentals
    2. Identifying the current phase in the boom-bust cycle
    3. Assessing alignment with major macro trends
    4. Evaluating potential inflection points where consensus may be wrong
    5. Using George Soros's contrarian, macro-focused style in your explanation

    For example, if bullish: "The market is underestimating the reflexive relationship between improving sentiment and fundamentals. We're in the early phase of a boom cycle with accommodative monetary policy, creating a self-reinforcing upward trend..."
    For example, if bearish: "A negative reflexive process is underway as deteriorating fundamentals and tightening monetary policy reinforce each other. The market consensus remains too optimistic, failing to recognize we've entered the bust phase of the cycle..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following data, create the investment signal as George Soros would:

    Analysis Data for {ticker}:
    {json.dumps(analysis_data[ticker], indent=2)}

    Macro Environment:
    {json.dumps(analysis_data["macro_environment"], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string",
      "reflexivity_analysis": "string describing the reflexive dynamics",
      "macro_trend": "string describing the identified macro trend",
      "boom_bust_phase": "string describing the current phase in the boom-bust cycle"
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_soros_signal():
        ticker_data = analysis_data.get(ticker, {})
        signal = ticker_data.get("signal", "neutral")
        score = ticker_data.get("score", 0)
        max_score = ticker_data.get("max_score", 1)
        confidence = (score / max_score * 100) if max_score > 0 else 50.0
        
        reflexivity_analysis = ticker_data.get("reflexivity_analysis", {})
        boom_bust_analysis = ticker_data.get("boom_bust_analysis", {})
        trend_alignment = ticker_data.get("trend_alignment", {})
        
        return SorosSignal(
            signal=signal,
            confidence=confidence,
            reasoning="Analysis based on quantitative metrics only. Unable to generate detailed reasoning.",
            reflexivity_analysis=reflexivity_analysis.get("details", "No reflexivity analysis available"),
            macro_trend=trend_alignment.get("trend_direction", "unknown"),
            boom_bust_phase=boom_bust_analysis.get("cycle_phase", "unknown"),
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=SorosSignal,
        agent_name="george_soros_agent",
        default_factory=create_default_soros_signal,
    )
