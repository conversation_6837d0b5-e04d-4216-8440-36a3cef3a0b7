"""
TdxDataService WebSocket 客户端测试脚本

这个脚本用于测试 TdxDataService 的 WebSocket 功能，包括：
1. 连接到 WebSocket 服务器
2. 订阅股票行情
3. 接收行情更新
4. 获取股票数据
5. 取消订阅

使用方法：
python test_tdx_websocket_client.py [选项]

选项：
--url URL           指定 WebSocket 服务器 URL，默认为 ws://localhost:8000/api/v1/tdx/ws
--symbols SYMBOLS   指定要订阅的股票代码，多个代码用逗号分隔，默认为 000001.SZ,600000.SH
--timeout TIMEOUT   指定接收超时时间（秒），默认为 30
"""

import asyncio
import json
import argparse
import websockets
import signal
import sys
from datetime import datetime

# 全局变量
running = True

def signal_handler(sig, frame):
    """信号处理函数"""
    global running
    print("\n接收到中断信号，正在退出...")
    running = False
    
# 注册信号处理函数
signal.signal(signal.SIGINT, signal_handler)

async def websocket_client(url, symbols, timeout):
    """WebSocket 客户端"""
    global running
    
    try:
        print(f"连接到 WebSocket 服务器: {url}")
        async with websockets.connect(url) as websocket:
            print(f"WebSocket 连接成功！时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 订阅股票行情
            symbol_list = symbols.split(",")
            print(f"\n订阅股票行情: {symbol_list}")
            subscribe_msg = {
                "action": "subscribe",
                "symbols": symbol_list
            }
            await websocket.send(json.dumps(subscribe_msg))
            response = await websocket.recv()
            print(f"订阅响应: {response}")
            
            # 接收行情更新
            print(f"\n等待行情更新 (最多 {timeout} 秒)...")
            print("按 Ctrl+C 退出")
            
            start_time = asyncio.get_event_loop().time()
            message_count = 0
            
            while running and (asyncio.get_event_loop().time() - start_time) < timeout:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    message_count += 1
                    print(f"\n收到消息 #{message_count}，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"消息内容: {response}")
                    
                    # 解析 JSON 消息
                    try:
                        data = json.loads(response)
                        if "type" in data and data["type"] == "quote_update":
                            print(f"收到行情更新: {data['symbol']}")
                            print(f"价格: {data['data'].get('price')}, 成交量: {data['data'].get('volume')}")
                    except json.JSONDecodeError:
                        print("无法解析 JSON 消息")
                except asyncio.TimeoutError:
                    # 超时，继续等待
                    pass
            
            # 取消订阅
            print(f"\n取消订阅股票行情: {symbol_list}")
            unsubscribe_msg = {
                "action": "unsubscribe",
                "symbols": symbol_list
            }
            await websocket.send(json.dumps(unsubscribe_msg))
            response = await websocket.recv()
            print(f"取消订阅响应: {response}")
            
            # 获取股票数据
            if len(symbol_list) > 0:
                symbol = symbol_list[0]
                market = 0 if symbol.endswith(".SZ") else 1
                code = symbol.split(".")[0]
                
                print(f"\n获取股票数据: {symbol}")
                get_data_msg = {
                    "action": "get_stock_data",
                    "symbol": code,
                    "market": market,
                    "period": "day",
                    "count": 5
                }
                await websocket.send(json.dumps(get_data_msg))
                response = await websocket.recv()
                print(f"获取股票数据响应: {response}")
            
            print("\nWebSocket 测试完成")
    except Exception as e:
        print(f"WebSocket 测试出错: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="TdxDataService WebSocket 客户端测试脚本")
    parser.add_argument("--url", type=str, default="ws://localhost:8000/api/v1/tdx/ws", help="指定 WebSocket 服务器 URL")
    parser.add_argument("--symbols", type=str, default="000001.SZ,600000.SH", help="指定要订阅的股票代码，多个代码用逗号分隔")
    parser.add_argument("--timeout", type=int, default=30, help="指定接收超时时间（秒）")
    
    args = parser.parse_args()
    
    # 运行 WebSocket 客户端
    asyncio.run(websocket_client(args.url, args.symbols, args.timeout))
    
if __name__ == "__main__":
    main()
