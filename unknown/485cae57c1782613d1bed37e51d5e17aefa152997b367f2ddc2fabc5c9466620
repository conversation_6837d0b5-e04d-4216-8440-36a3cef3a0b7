#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库工厂模块

该模块提供数据库客户端工厂，根据配置返回适当的数据库客户端实例。
"""

from db.db_config import DBConfig, DB_TYPE_SQLITE, DB_TYPE_POSTGRES
from db.sqlite_db import SQLiteDBClient
from db.postgres_db import PostgresDBClient
from utils.logger import logger

class DBFactory:
    """数据库工厂类"""
    
    @staticmethod
    def get_db_client():
        """
        获取数据库客户端实例
        
        根据配置返回SQLite或PostgreSQL数据库客户端实例。
        
        Returns:
            object: 数据库客户端实例
        """
        db_type, config = DBConfig.get_db_config()
        
        if db_type == DB_TYPE_SQLITE:
            return SQLiteDBClient(config["db_path"])
        elif db_type == DB_TYPE_POSTGRES:
            return PostgresDBClient(
                host=config["host"],
                database=config["database"],
                user=config["user"],
                password=config["password"],
                port=config["port"]
            )
        else:
            # 这种情况不应该发生，因为DBConfig.get_db_config已经验证了数据库类型
            logger.error(f"未知的数据库类型: {db_type}")
            raise ValueError(f"未知的数据库类型: {db_type}")
