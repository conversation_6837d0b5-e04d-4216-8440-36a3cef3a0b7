"""
ESG Analyst agent for the AI Hedge Fund module.

This agent analyzes companies based on Environmental, Social, and Governance (ESG) factors.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.esg_data import get_esg_data, analyze_esg_data
from ..utils.llm import call_llm


class ESGSignal(BaseModel):
    """Signal generated by the ESG Analyst agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    esg_rating: str = Field(description="Overall ESG rating")
    top_strengths: List[str] = Field(description="Top ESG strengths")
    top_concerns: List[str] = Field(description="Top ESG concerns")


def esg_analyst_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes companies based on ESG factors.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with ESG analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    esg_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("esg_analyst_agent", ticker, "Fetching ESG data")
        
        # Get ESG data
        esg_data = get_esg_data(ticker, end_date)
        
        if esg_data is None:
            # Skip this ticker if no ESG data
            if "progress" in state["metadata"]:
                state["metadata"]["progress"].update_status("esg_analyst_agent", ticker, "No ESG data available")
            
            esg_analysis[ticker] = {
                "signal": "neutral",
                "confidence": 0.0,
                "reasoning": "No ESG data available for analysis",
            }
            continue
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("esg_analyst_agent", ticker, "Analyzing ESG data")
        
        # Analyze ESG data
        analysis = analyze_esg_data(esg_data)
        
        # Store analysis data
        analysis_data[ticker] = {
            "esg_data": esg_data.to_dict(),
            "analysis": analysis,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("esg_analyst_agent", ticker, "Generating ESG analysis")
        
        # Generate detailed analysis using LLM
        esg_output = generate_esg_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        esg_analysis[ticker] = {
            "signal": esg_output.signal,
            "confidence": esg_output.confidence,
            "reasoning": esg_output.reasoning,
            "esg_rating": esg_output.esg_rating,
            "top_strengths": esg_output.top_strengths,
            "top_concerns": esg_output.top_concerns,
            "esg_data": esg_data.to_dict(),
            "analysis": analysis,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("esg_analyst_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(esg_analysis, "ESG Analyst Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["esg_analyst_agent"] = esg_analysis
    
    return state


def generate_esg_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> ESGSignal:
    """
    Generate detailed ESG analysis using LLM.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        ESGSignal object with the ESG analysis
    """
    # Create a system prompt for the LLM
    system_prompt = """You are an ESG Analyst AI agent. Analyze companies based on Environmental, Social, and Governance factors.

    Key principles for ESG analysis:
    - Environmental Factors: Evaluate climate impact, resource use, pollution, and sustainability initiatives
    - Social Factors: Assess labor practices, human rights, community relations, and product responsibility
    - Governance Factors: Examine board structure, executive compensation, business ethics, and shareholder rights
    - Controversies: Consider the severity and recency of ESG controversies
    - Industry Context: Compare ESG performance relative to industry peers
    - Investment Implications: Translate ESG factors into potential financial impacts
    - Long-term Perspective: Focus on sustainable business practices and long-term risks/opportunities

    When providing your reasoning, be thorough and specific by:
    1. Summarizing the company's overall ESG profile and rating
    2. Highlighting the most significant ESG strengths and concerns
    3. Explaining how ESG factors might impact financial performance and investment risk
    4. Providing a balanced view that considers both positive and negative ESG aspects
    5. Using a professional, analytical tone in your explanation

    For example, if bullish: "The company demonstrates strong environmental initiatives with a 75/100 score in this category. Their carbon reduction targets exceed industry standards, and they've invested significantly in renewable energy. These factors reduce regulatory risk and position them well for the transition to a low-carbon economy..."
    For example, if bearish: "With multiple recent controversies including labor disputes and a major environmental incident, the company faces significant ESG risks. Their governance score of 35/100 is well below industry average, raising concerns about oversight and accountability..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following ESG data, create a trading signal for {ticker}:

    Analysis Data:
    {json.dumps(analysis_data[ticker], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string",
      "esg_rating": "string",
      "top_strengths": ["string", "string", ...],
      "top_concerns": ["string", "string", ...]
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_esg_signal():
        ticker_data = analysis_data.get(ticker, {})
        analysis = ticker_data.get("analysis", {})
        esg_data = ticker_data.get("esg_data", {})
        
        # Determine signal based on total score
        total_score = esg_data.get("total_score", 50)
        if total_score >= 70:
            signal = "bullish"
            confidence = min(50.0 + (total_score - 50), 90.0)
        elif total_score <= 30:
            signal = "bearish"
            confidence = min(50.0 + (50 - total_score), 90.0)
        else:
            signal = "neutral"
            confidence = 50.0
        
        # Get ESG rating
        esg_rating = analysis.get("overall_rating", "Unknown")
        
        # Get strengths and concerns
        strengths = analysis.get("strengths", [])
        weaknesses = analysis.get("weaknesses", [])
        
        # Generate reasoning
        reasoning = analysis.get("summary", f"ESG analysis for {ticker}")
        
        return ESGSignal(
            signal=signal,
            confidence=confidence,
            reasoning=reasoning,
            esg_rating=esg_rating,
            top_strengths=strengths[:3] if strengths else ["No significant strengths identified"],
            top_concerns=weaknesses[:3] if weaknesses else ["No significant concerns identified"],
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=ESGSignal,
        agent_name="esg_analyst_agent",
        default_factory=create_default_esg_signal,
    )
