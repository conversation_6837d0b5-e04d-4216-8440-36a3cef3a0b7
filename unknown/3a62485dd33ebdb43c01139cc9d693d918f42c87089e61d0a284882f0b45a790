"""
Backtest visualization module for the AI Hedge Fund.

This module provides functions for visualizing backtest results.
"""

from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import io
import base64
from datetime import datetime


def plot_backtest_performance(
    backtest_result: Dict[str, Any],
    title: Optional[str] = None,
) -> str:
    """
    Plot the performance of a backtest.
    
    Args:
        backtest_result: Dictionary with backtest results
        title: Plot title (optional)
        
    Returns:
        Base64-encoded image of the plot
    """
    if backtest_result.get("status") != "success":
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, backtest_result.get("message", "Unknown error"), ha="center", va="center", fontsize=14)
        ax.set_title(title or "Backtest Performance")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Extract data
    dates = pd.to_datetime(backtest_result.get("dates", []))
    portfolio_cumulative_returns = backtest_result.get("portfolio_cumulative_returns", [])
    benchmark_cumulative_returns = backtest_result.get("benchmark_cumulative_returns", [])
    
    if not dates.any() or not portfolio_cumulative_returns:
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, "No performance data available", ha="center", va="center", fontsize=14)
        ax.set_title(title or "Backtest Performance")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Create the plot
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Plot cumulative returns
    ax.plot(dates, portfolio_cumulative_returns, label="Portfolio", color="blue", linewidth=2)
    
    if benchmark_cumulative_returns:
        ax.plot(dates, benchmark_cumulative_returns, label="Benchmark", color="gray", linewidth=1.5, alpha=0.7)
    
    # Format the plot
    ax.set_xlabel("Date")
    ax.set_ylabel("Cumulative Return")
    ax.set_title(title or "Backtest Performance")
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # Add horizontal line at y=0
    ax.axhline(y=0, color="black", linestyle="--", alpha=0.5)
    
    # Format the x-axis to show dates nicely
    ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m"))
    ax.xaxis.set_major_locator(mdates.YearLocator())
    fig.autofmt_xdate()
    
    # Format y-axis as percentage
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f"{y:.0%}"))
    
    # Add key metrics as text
    metrics = backtest_result.get("metrics", {})
    metrics_text = (
        f"Total Return: {metrics.get('total_return', 0.0) * 100:.2f}%\n"
        f"Ann. Return: {metrics.get('annualized_return', 0.0) * 100:.2f}%\n"
        f"Volatility: {metrics.get('volatility', 0.0) * 100:.2f}%\n"
        f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0.0):.2f}\n"
        f"Max Drawdown: {metrics.get('max_drawdown', 0.0) * 100:.2f}%"
    )
    
    # Position the text box in figure coords
    props = dict(boxstyle="round", facecolor="white", alpha=0.8)
    ax.text(0.02, 0.98, metrics_text, transform=ax.transAxes, fontsize=10,
            verticalalignment="top", bbox=props)
    
    # Adjust layout
    plt.tight_layout()
    
    # Convert plot to base64-encoded image
    buffer = io.BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
    plt.close()
    
    return image_base64


def plot_drawdowns(
    backtest_result: Dict[str, Any],
    title: Optional[str] = None,
) -> str:
    """
    Plot the drawdowns of a backtest.
    
    Args:
        backtest_result: Dictionary with backtest results
        title: Plot title (optional)
        
    Returns:
        Base64-encoded image of the plot
    """
    if backtest_result.get("status") != "success":
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, backtest_result.get("message", "Unknown error"), ha="center", va="center", fontsize=14)
        ax.set_title(title or "Drawdowns")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Extract data
    dates = pd.to_datetime(backtest_result.get("dates", []))
    portfolio_returns = backtest_result.get("portfolio_returns", [])
    
    if not dates.any() or not portfolio_returns:
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, "No drawdown data available", ha="center", va="center", fontsize=14)
        ax.set_title(title or "Drawdowns")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Calculate drawdowns
    portfolio_returns_series = pd.Series(portfolio_returns, index=dates)
    cumulative_returns = (1 + portfolio_returns_series).cumprod()
    running_max = cumulative_returns.cummax()
    drawdowns = (cumulative_returns / running_max) - 1
    
    # Create the plot
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Plot drawdowns
    ax.fill_between(dates, 0, drawdowns, color="red", alpha=0.3)
    ax.plot(dates, drawdowns, color="red", linewidth=1)
    
    # Format the plot
    ax.set_xlabel("Date")
    ax.set_ylabel("Drawdown")
    ax.set_title(title or "Portfolio Drawdowns")
    ax.grid(True, alpha=0.3)
    
    # Format the x-axis to show dates nicely
    ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m"))
    ax.xaxis.set_major_locator(mdates.YearLocator())
    fig.autofmt_xdate()
    
    # Format y-axis as percentage
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f"{y:.0%}"))
    
    # Add key metrics as text
    metrics = backtest_result.get("metrics", {})
    metrics_text = (
        f"Max Drawdown: {metrics.get('max_drawdown', 0.0) * 100:.2f}%\n"
        f"Calmar Ratio: {metrics.get('calmar_ratio', 0.0):.2f}"
    )
    
    # Position the text box in figure coords
    props = dict(boxstyle="round", facecolor="white", alpha=0.8)
    ax.text(0.02, 0.02, metrics_text, transform=ax.transAxes, fontsize=10,
            verticalalignment="bottom", bbox=props)
    
    # Adjust layout
    plt.tight_layout()
    
    # Convert plot to base64-encoded image
    buffer = io.BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
    plt.close()
    
    return image_base64


def plot_monthly_returns(
    backtest_result: Dict[str, Any],
    title: Optional[str] = None,
) -> str:
    """
    Plot the monthly returns heatmap of a backtest.
    
    Args:
        backtest_result: Dictionary with backtest results
        title: Plot title (optional)
        
    Returns:
        Base64-encoded image of the plot
    """
    if backtest_result.get("status") != "success":
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, backtest_result.get("message", "Unknown error"), ha="center", va="center", fontsize=14)
        ax.set_title(title or "Monthly Returns")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Extract data
    dates = pd.to_datetime(backtest_result.get("dates", []))
    portfolio_returns = backtest_result.get("portfolio_returns", [])
    
    if not dates.any() or not portfolio_returns:
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, "No monthly returns data available", ha="center", va="center", fontsize=14)
        ax.set_title(title or "Monthly Returns")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Calculate monthly returns
    portfolio_returns_series = pd.Series(portfolio_returns, index=dates)
    monthly_returns = portfolio_returns_series.resample("M").apply(lambda x: (1 + x).prod() - 1)
    
    # Create a pivot table of monthly returns by year and month
    monthly_returns_table = pd.DataFrame({
        "Year": monthly_returns.index.year,
        "Month": monthly_returns.index.month,
        "Return": monthly_returns.values,
    })
    
    pivot_table = monthly_returns_table.pivot(index="Year", columns="Month", values="Return")
    
    # Create the plot
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Create heatmap
    sns.heatmap(
        pivot_table,
        annot=True,
        fmt=".1%",
        cmap="RdYlGn",
        center=0,
        linewidths=1,
        ax=ax,
        cbar_kws={"label": "Monthly Return"},
    )
    
    # Format the plot
    ax.set_title(title or "Monthly Returns Heatmap")
    ax.set_xlabel("Month")
    ax.set_ylabel("Year")
    
    # Set month names
    month_names = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    ax.set_xticklabels(month_names, rotation=0)
    
    # Adjust layout
    plt.tight_layout()
    
    # Convert plot to base64-encoded image
    buffer = io.BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
    plt.close()
    
    return image_base64


def plot_rolling_metrics(
    backtest_result: Dict[str, Any],
    window: int = 252,  # Default to 1 year
    title: Optional[str] = None,
) -> str:
    """
    Plot rolling metrics (returns, volatility, Sharpe ratio) of a backtest.
    
    Args:
        backtest_result: Dictionary with backtest results
        window: Rolling window size in days
        title: Plot title (optional)
        
    Returns:
        Base64-encoded image of the plot
    """
    if backtest_result.get("status") != "success":
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, backtest_result.get("message", "Unknown error"), ha="center", va="center", fontsize=14)
        ax.set_title(title or "Rolling Metrics")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Extract data
    dates = pd.to_datetime(backtest_result.get("dates", []))
    portfolio_returns = backtest_result.get("portfolio_returns", [])
    benchmark_returns = backtest_result.get("benchmark_returns", [])
    
    if not dates.any() or not portfolio_returns or len(dates) < window:
        # Create a simple error plot
        fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
        ax.text(0.5, 0.5, "Insufficient data for rolling metrics", ha="center", va="center", fontsize=14)
        ax.set_title(title or "Rolling Metrics")
        ax.axis("off")
        
        # Convert plot to base64-encoded image
        buffer = io.BytesIO()
        plt.savefig(buffer, format="png")
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
        plt.close()
        
        return image_base64
    
    # Convert to Series
    portfolio_returns_series = pd.Series(portfolio_returns, index=dates)
    benchmark_returns_series = pd.Series(benchmark_returns, index=dates) if benchmark_returns else None
    
    # Calculate rolling metrics
    # 1. Rolling annualized return
    rolling_return = portfolio_returns_series.rolling(window).apply(
        lambda x: (1 + x).prod() ** (252 / len(x)) - 1
    )
    
    # 2. Rolling annualized volatility
    rolling_volatility = portfolio_returns_series.rolling(window).std() * np.sqrt(252)
    
    # 3. Rolling Sharpe ratio (assuming 0% risk-free rate for simplicity)
    rolling_sharpe = rolling_return / rolling_volatility
    
    # 4. Rolling benchmark metrics (if available)
    if benchmark_returns_series is not None:
        rolling_benchmark_return = benchmark_returns_series.rolling(window).apply(
            lambda x: (1 + x).prod() ** (252 / len(x)) - 1
        )
        rolling_alpha = rolling_return - rolling_benchmark_return
    else:
        rolling_alpha = None
    
    # Create the plot with subplots
    fig, axs = plt.subplots(3, 1, figsize=(12, 12), sharex=True)
    
    # Plot rolling return
    axs[0].plot(dates[window-1:], rolling_return, color="blue", linewidth=2)
    if benchmark_returns_series is not None:
        axs[0].plot(dates[window-1:], rolling_benchmark_return, color="gray", linewidth=1.5, alpha=0.7, label="Benchmark")
    axs[0].set_ylabel("Annualized Return")
    axs[0].set_title("Rolling Annualized Return")
    axs[0].grid(True, alpha=0.3)
    axs[0].axhline(y=0, color="black", linestyle="--", alpha=0.5)
    if benchmark_returns_series is not None:
        axs[0].legend()
    
    # Plot rolling volatility
    axs[1].plot(dates[window-1:], rolling_volatility, color="red", linewidth=2)
    axs[1].set_ylabel("Annualized Volatility")
    axs[1].set_title("Rolling Annualized Volatility")
    axs[1].grid(True, alpha=0.3)
    
    # Plot rolling Sharpe ratio or alpha
    if rolling_alpha is not None:
        axs[2].plot(dates[window-1:], rolling_alpha, color="green", linewidth=2)
        axs[2].set_ylabel("Alpha")
        axs[2].set_title("Rolling Alpha")
    else:
        axs[2].plot(dates[window-1:], rolling_sharpe, color="purple", linewidth=2)
        axs[2].set_ylabel("Sharpe Ratio")
        axs[2].set_title("Rolling Sharpe Ratio")
    axs[2].grid(True, alpha=0.3)
    axs[2].axhline(y=0, color="black", linestyle="--", alpha=0.5)
    
    # Format the x-axis to show dates nicely
    axs[2].xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m"))
    axs[2].xaxis.set_major_locator(mdates.YearLocator())
    fig.autofmt_xdate()
    
    # Set overall title
    fig.suptitle(title or f"Rolling Metrics ({window} Days)", fontsize=16)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    
    # Convert plot to base64-encoded image
    buffer = io.BytesIO()
    plt.savefig(buffer, format="png")
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode("utf-8")
    plt.close()
    
    return image_base64
