# 智能体投顾助手 - Web应用

这是股票数据洞察应用的Web前端界面，提供了用户友好的方式来访问和可视化股票数据和相关新闻。

## 功能特点

- **股票数据可视化**：展示股票价格走势图表，支持不同时间周期（日、周、月、季、年）
- **股票详情展示**：显示股票的关键指标和财务数据
- **新闻聚合**：展示与特定股票相关的新闻，支持按主题筛选
- **高级图表**：使用lightweight-charts库实现日内走势图和历史走势图
- **技术指标分析**：提供移动平均线、RSI、MACD、布林带等技术指标分析
- **扩展技术指标**：提供成交量、OBV、ATR、ADX等高级技术指标
- **股票比较**：支持多只股票的历史表现对比，帮助用户做出更好的投资决策
- **股票搜索自动完成**：提供智能的股票代码和名称搜索功能
- **用户认证**：支持用户注册和登录功能
- **个人资料管理**：用户可以更新个人信息
- **API令牌管理**：用户可以生成和撤销API访问令牌
- **股票收藏**：用户可以收藏和管理自己感兴趣的股票

## 技术栈

- **前端**：HTML, CSS, JavaScript, Bootstrap 5
- **后端**：FastAPI, Jinja2模板
- **图表**：Chart.js, Lightweight Charts
- **数据源**：Yahoo Finance (yfinance)

## 目录结构

```
web_app/
├── static/             # 静态资源
│   ├── css/            # CSS样式文件
│   │   └── style.css   # 主样式文件
│   └── js/             # JavaScript文件
│       ├── main.js              # 主页面脚本
│       ├── profile.js           # 个人资料页面脚本
│       ├── autocomplete.js      # 股票搜索自动完成脚本
│       ├── technical-indicators.js # 技术指标脚本
│       ├── stock-comparison.js   # 股票比较脚本
│       ├── advanced-charts.js    # 高级图表脚本
│       └── extended-indicators.js # 扩展技术指标脚本
├── templates/          # HTML模板
│   ├── index.html      # 主页面
│   └── profile.html    # 个人资料页面
├── app.py              # FastAPI应用入口
└── start_web_app.bat    # Windows启动脚本
```

## 安装和运行

1. 确保已安装Python 3.7+和pip
2. 安装所需依赖：

```bash
pip install -r web_app/requirements.txt
```

3. 运行Web应用：

```bash
cd web_app
python run.py
```

或者直接运行启动脚本：

```bash
cd web_app
start_web_app.bat  # Windows
```

4. 在浏览器中访问：`http://localhost:8080`

## 数据源

本应用使用Yahoo Finance (yfinance)库获取实时股票数据，包括：

- 股票价格和交易量
- 日内分钟级别数据
- 历史价格数据
- 技术指标数据

所有数据通过内置API提供，无需外部服务。API端点路径为 `/api/stock/...`。

### 使用真实数据

本应用使用yfinance库从雅虎金融获取真实的股票数据，而不是使用模拟数据。这意味着您可以搜索任何有效的股票代码（如AAPL、MSFT、GOOG等），并获取其真实的历史数据和技术指标。

### 数据回退机制

当无法从雅虎金融获取真实数据时（例如网络问题、API限制或股票代码无效），系统会自动回退到使用模拟数据。这确保了即使在数据获取失败的情况下，用户仍然可以使用应用的所有功能。

模拟数据会模拟真实股票的价格波动和技术指标，但仅用于演示目的，不应用于实际投资决策。

如果需要更改API端点，请修改 `static/js/main.js`和 `static/js/profile.js`文件中的 `API_BASE_URL`常量。

## 新增功能

### K线图和技术分析

本应用提供了完整的K线图功能，包括：

- 真正的蜡烛图表示（开盘价、最高价、最低价和收盘价）
- 成交量柱状图，颜色与K线图一致
- 日内分钟级别数据图表
- 多周期K线图，支持从1分钟到日线的多种时间周期
- 可选时间范围，从1天到全部历史数据
- 多种技术指标，如移动平均线、RSI、MACD等

### 股票搜索自动完成

在搜索框中输入时，系统会自动提示匹配的股票代码和名称，提高用户搜索效率。支持按股票代码或公司名称搜索。

### 技术指标分析

提供了丰富的技术指标分析工具，包括：

- **移动平均线 (MA)**：显示5日、7日和20日移动平均线
- **相对强弱指标 (RSI)**：帮助识别超买和超卖区域
- **MACD**：移动平均线收散指标，用于判断价格趋势
- **布林带**：帮助识别价格波动性和可能的反转点

### 股票比较功能

允许用户同时比较多只股票的表现，帮助做出更好的投资决策。特点包括：

- 归一化的价格比较，便于直观对比不同价位的股票
- 支持添加和移除多只股票
- 不同股票使用不同颜色区分，提高可读性

## 开发说明

当前版本使用了模拟数据来展示UI功能。在实际部署时，需要将JavaScript文件中的模拟API调用替换为真实的API请求。


## 其它
