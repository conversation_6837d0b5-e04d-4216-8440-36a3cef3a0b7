import os
import json
import redis
import uuid
from dotenv import load_dotenv
from typing import List, Dict, Any, Optional, Union

# Load environment variables from .env file
load_dotenv()

class RedisDBClient:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(RedisDBClient, cls).__new__(cls)
        return cls._instance

    def __init__(self, host=None, port=None, db=None, password=None):
        if not hasattr(self, '_initialized'):  # Prevent reinitialization in the singleton
            # Load default values from environment variables if not provided
            self.host = host or os.getenv('REDIS_LOCAL_HOST', 'localhost')
            self.port = port or int(os.getenv('REDIS_LOCAL_PORT', '6379'))
            self.db = db or int(os.getenv('REDIS_LOCAL_DB', '0'))
            self.password = password or os.getenv('REDIS_LOCAL_PASSWORD', None)

            try:
                # Connect to Redis
                self.client = redis.Redis(
                    host=self.host,
                    port=self.port,
                    db=self.db,
                    password=self.password,
                    socket_timeout=5,
                    decode_responses=True  # Automatically decode responses to Python strings
                )
                # Test connection
                self.client.ping()
                print(f"Successfully connected to Redis at {self.host}:{self.port}")
                self._initialized = True
            except Exception as e:
                print(f"Warning: Could not connect to Redis: {e}")
                print("Operating in offline mode - data will not be saved to database")
                self.client = None
                self._initialized = True

    def _get_collection_prefix(self, collection_name: str) -> str:
        """Get the key prefix for a collection."""
        # In Redis, we'll use key prefixes to simulate collections
        collection_name = collection_name or os.getenv('COLLECTION_NAME', 'default_collection')
        return f"{collection_name}:"

    def _serialize(self, data: Any) -> str:
        """Serialize data to JSON string."""
        return json.dumps(data)

    def _deserialize(self, data: str) -> Any:
        """Deserialize JSON string to data."""
        if data is None:
            return None
        return json.loads(data)

    def insert_one(self, collection_name: str, document: Dict[str, Any]) -> str:
        """Insert a single document into a collection."""
        if self.client is None:
            print(f"Warning: Could not insert document into '{collection_name}' - Redis connection not available")
            return None

        try:
            # Generate a unique ID if not provided
            if '_id' not in document:
                document['_id'] = str(uuid.uuid4())

            # Create the key using collection prefix and document ID
            key = f"{self._get_collection_prefix(collection_name)}{document['_id']}"

            # Store the document as a JSON string
            self.client.set(key, self._serialize(document))

            # Add the key to a set that tracks all keys in this collection
            self.client.sadd(f"{collection_name}:keys", key)

            return document['_id']
        except Exception as e:
            print(f"Error inserting document: {e}")
            return None

    def insert_many(self, collection_name: str, documents: List[Dict[str, Any]]) -> List[str]:
        """Insert multiple documents into a collection."""
        if self.client is None:
            print(f"Warning: Could not insert documents into '{collection_name}' - Redis connection not available")
            return None

        try:
            inserted_ids = []
            pipe = self.client.pipeline()

            for document in documents:
                # Generate a unique ID if not provided
                if '_id' not in document:
                    document['_id'] = str(uuid.uuid4())

                # Create the key using collection prefix and document ID
                key = f"{self._get_collection_prefix(collection_name)}{document['_id']}"

                # Store the document as a JSON string
                pipe.set(key, self._serialize(document))

                # Add the key to a set that tracks all keys in this collection
                pipe.sadd(f"{collection_name}:keys", key)

                inserted_ids.append(document['_id'])

            # Execute all commands in the pipeline
            pipe.execute()

            return inserted_ids
        except Exception as e:
            print(f"Error inserting documents: {e}")
            return None

    def find(self, collection_name: str, query: Dict[str, Any] = None, projection: Dict[str, int] = None) -> List[Dict[str, Any]]:
        """Retrieve documents from a collection."""
        if self.client is None:
            print(f"Warning: Could not query '{collection_name}' - Redis connection not available")
            return []

        try:
            # Get all keys in the collection
            collection_keys = self.client.smembers(f"{collection_name}:keys")
            if not collection_keys:
                return []

            # Get all documents
            pipe = self.client.pipeline()
            for key in collection_keys:
                pipe.get(key)

            # Execute pipeline and get results
            results = pipe.execute()

            # Deserialize documents
            documents = [self._deserialize(result) for result in results if result]

            # Filter documents based on query
            if query:
                filtered_docs = []
                for doc in documents:
                    match = True
                    for key, value in query.items():
                        if key not in doc or doc[key] != value:
                            match = False
                            break
                    if match:
                        filtered_docs.append(doc)
                documents = filtered_docs

            # Apply projection if provided
            if projection:
                projected_docs = []
                for doc in documents:
                    projected_doc = {}
                    for key, include in projection.items():
                        if include == 1 and key in doc:
                            projected_doc[key] = doc[key]
                    if not projection or projected_doc:  # Include document if projection is empty or if it has projected fields
                        projected_docs.append(projected_doc)
                documents = projected_docs

            return documents
        except Exception as e:
            print(f"Error finding documents: {e}")
            return []

    def update_one(self, collection_name: str, query: Dict[str, Any], update: Dict[str, Any], upsert: bool = False) -> int:
        """Update a single document in a collection."""
        if self.client is None:
            print(f"Warning: Could not update document in '{collection_name}' - Redis connection not available")
            return 0

        try:
            # Find the document to update
            documents = self.find(collection_name, query)
            if not documents and not upsert:
                return 0

            modified_count = 0

            if documents:
                # Update the first matching document
                doc = documents[0]
                doc_id = doc['_id']

                # Apply updates
                if '$set' in update:
                    for key, value in update['$set'].items():
                        doc[key] = value

                # Save the updated document
                key = f"{self._get_collection_prefix(collection_name)}{doc_id}"
                self.client.set(key, self._serialize(doc))
                modified_count = 1
            elif upsert:
                # Create a new document
                new_doc = {}

                # Add query fields
                for key, value in query.items():
                    new_doc[key] = value

                # Apply updates
                if '$set' in update:
                    for key, value in update['$set'].items():
                        new_doc[key] = value

                # Insert the new document
                self.insert_one(collection_name, new_doc)
                modified_count = 1

            return modified_count
        except Exception as e:
            print(f"Error updating document: {e}")
            return 0

    def update_many(self, collection_name: str, query: Dict[str, Any], update: Dict[str, Any], upsert: bool = False) -> int:
        """Update multiple documents in a collection."""
        if self.client is None:
            print(f"Warning: Could not update documents in '{collection_name}' - Redis connection not available")
            return 0

        try:
            # Find documents to update
            documents = self.find(collection_name, query)
            if not documents and not upsert:
                return 0

            modified_count = 0
            pipe = self.client.pipeline()

            for doc in documents:
                doc_id = doc['_id']

                # Apply updates
                if '$set' in update:
                    for key, value in update['$set'].items():
                        doc[key] = value

                # Save the updated document
                key = f"{self._get_collection_prefix(collection_name)}{doc_id}"
                pipe.set(key, self._serialize(doc))
                modified_count += 1

            if not documents and upsert:
                # Create a new document
                new_doc = {}

                # Add query fields
                for key, value in query.items():
                    new_doc[key] = value

                # Apply updates
                if '$set' in update:
                    for key, value in update['$set'].items():
                        new_doc[key] = value

                # Generate ID and create key
                new_doc['_id'] = str(uuid.uuid4())
                key = f"{self._get_collection_prefix(collection_name)}{new_doc['_id']}"

                # Add to pipeline
                pipe.set(key, self._serialize(new_doc))
                pipe.sadd(f"{collection_name}:keys", key)
                modified_count += 1

            # Execute all commands in the pipeline
            pipe.execute()

            return modified_count
        except Exception as e:
            print(f"Error updating documents: {e}")
            return 0

    def delete_one(self, collection_name: str, query: Dict[str, Any]) -> int:
        """Delete a single document from a collection."""
        if self.client is None:
            print(f"Warning: Could not delete document from '{collection_name}' - Redis connection not available")
            return 0

        try:
            # Find the document to delete
            documents = self.find(collection_name, query)
            if not documents:
                return 0

            # Delete the first matching document
            doc = documents[0]
            doc_id = doc['_id']
            key = f"{self._get_collection_prefix(collection_name)}{doc_id}"

            # Remove the document and its key from the collection keys set
            pipe = self.client.pipeline()
            pipe.delete(key)
            pipe.srem(f"{collection_name}:keys", key)
            pipe.execute()

            return 1
        except Exception as e:
            print(f"Error deleting document: {e}")
            return 0

    def delete_many(self, collection_name: str, query: Dict[str, Any]) -> int:
        """Delete multiple documents from a collection."""
        if self.client is None:
            print(f"Warning: Could not delete documents from '{collection_name}' - Redis connection not available")
            return 0

        try:
            # Find documents to delete
            documents = self.find(collection_name, query)
            if not documents:
                return 0

            deleted_count = 0
            pipe = self.client.pipeline()

            for doc in documents:
                doc_id = doc['_id']
                key = f"{self._get_collection_prefix(collection_name)}{doc_id}"

                # Remove the document and its key from the collection keys set
                pipe.delete(key)
                pipe.srem(f"{collection_name}:keys", key)
                deleted_count += 1

            # Execute all commands in the pipeline
            pipe.execute()

            return deleted_count
        except Exception as e:
            print(f"Error deleting documents: {e}")
            return 0

    def check_connection(self) -> bool:
        """Check if the Redis connection is available."""
        if self.client is None:
            return False

        try:
            return self.client.ping()
        except Exception:
            return False
