# BarDataset2 RAG Retriever

这个模块提供了一个基于BarDataset2数据集的RAG（检索增强生成）检索库，支持多种嵌入向量算法，以及保存、新增、更新、加载、检索等功能。

## 功能特点

- **多种嵌入算法支持**：支持RNN、Transformer和CNN等多种嵌入算法
- **高效检索**：基于FAISS库实现高效的向量相似度检索
- **完整的数据管理**：支持索引的构建、保存、加载、更新和新增
- **灵活的API**：提供简洁易用的API，支持自定义嵌入算法

## 安装依赖

```bash
pip install numpy pandas torch faiss-cpu
```

如果需要GPU加速，可以安装`faiss-gpu`：

```bash
pip install faiss-gpu
```

## 快速开始

### 基本用法

```python
from bar_dataset_rag import BarDatasetRetriever, create_bar_dataset_retriever
from pyqlab.data.dataset.dataset_bar2 import BarDataset2
from pyqlab.rag.embedded import TimeSeriesRNNEmbedder

# 创建数据集
dataset = BarDataset2(...)

# 方法1：使用工厂函数创建检索器
retriever = create_bar_dataset_retriever(
    embedding_dim=64,
    embedder_type='rnn',
    n_neighbors=5,
    metric='l2'
)

# 方法2：手动创建检索器
retriever = BarDatasetRetriever(
    embedding_dim=64,
    n_neighbors=5,
    metric='l2'
)

# 创建嵌入模型
embedder = TimeSeriesRNNEmbedder(
    input_dim=1,
    hidden_dim=64,
    output_dim=64,
    num_layers=2,
    bidirectional=True
)

# 设置嵌入函数
def embedder_fn(data):
    # 将数据转换为嵌入向量的代码
    ...
    return embedding

retriever.set_embedder(embedder_fn)

# 构建索引
retriever.build_index(dataset)

# 检索相似数据
query_data = ...
distances, indices, metadata = retriever.retrieve(query_data, k=5)

# 保存索引
retriever.save("path/to/save/dir")

# 加载索引
new_retriever = BarDatasetRetriever(embedding_dim=64)
new_retriever.load("path/to/save/dir")

# 添加新数据
new_data = ...
new_metadata = {'source': 'new'}
new_idx = retriever.add_to_index(new_data, new_metadata)

# 更新数据
updated_data = ...
updated_metadata = {'updated': True}
retriever.update_index(new_idx, updated_data, updated_metadata)
```

### 运行示例

提供了一个完整的示例脚本`bar_dataset_rag_example.py`，可以通过以下命令运行：

```bash
python bar_dataset_rag_example.py --data_path="path/to/data" --visualize --save_dir="path/to/save/dir"
```

参数说明：
- `--data_path`：数据路径
- `--market`：市场类型（fut, stk）
- `--block_name`：板块名称（sf, main）
- `--period`：周期（day, min5）
- `--n_neighbors`：检索邻居数量
- `--metric`：相似度度量方式（l2, ip）
- `--use_factory`：是否使用工厂函数创建检索器
- `--save_dir`：索引保存路径
- `--visualize`：是否可视化检索结果

### 运行测试

可以通过以下命令运行单元测试：

```bash
python -m unittest test_bar_dataset_rag.py
```

## API参考

### BarDatasetRetriever

主要的检索器类，提供索引构建、检索、保存、加载等功能。

#### 初始化

```python
retriever = BarDatasetRetriever(embedding_dim, n_neighbors=5, metric='l2')
```

- `embedding_dim`：嵌入向量维度
- `n_neighbors`：默认检索邻居数量
- `metric`：相似度度量方式，支持'l2'（欧氏距离）和'ip'（内积）

#### 设置嵌入函数

```python
retriever.set_embedder(embedder_fn)
```

- `embedder_fn`：将数据转换为嵌入向量的函数

#### 构建索引

```python
retriever.build_index(dataset, embedder=None)
```

- `dataset`：BarDataset2数据集
- `embedder`：可选，嵌入函数，如果为None则使用之前设置的嵌入函数

#### 检索

```python
distances, indices, metadata = retriever.retrieve(query_data, k=None, embedder=None)
```

- `query_data`：查询数据
- `k`：检索邻居数量，如果为None则使用初始化时设置的n_neighbors
- `embedder`：可选，嵌入函数，如果为None则使用之前设置的嵌入函数
- 返回：距离、索引和元数据

#### 保存索引

```python
retriever.save(directory, prefix="bar_dataset_retriever")
```

- `directory`：保存目录
- `prefix`：文件名前缀

#### 加载索引

```python
retriever.load(directory, prefix="bar_dataset_retriever")
```

- `directory`：加载目录
- `prefix`：文件名前缀

#### 添加数据

```python
new_idx = retriever.add_to_index(data, metadata=None, embedder=None)
```

- `data`：要添加的数据
- `metadata`：可选，数据的元数据
- `embedder`：可选，嵌入函数，如果为None则使用之前设置的嵌入函数
- 返回：新添加数据的索引

#### 更新数据

```python
retriever.update_index(index, data, metadata=None, embedder=None)
```

- `index`：要更新的数据索引
- `data`：新数据
- `metadata`：可选，新的元数据
- `embedder`：可选，嵌入函数，如果为None则使用之前设置的嵌入函数

### 工厂函数

```python
retriever = create_bar_dataset_retriever(embedding_dim, embedder_type='rnn', n_neighbors=5, metric='l2', **kwargs)
```

- `embedding_dim`：嵌入向量维度
- `embedder_type`：嵌入算法类型，支持'rnn'、'transformer'和'cnn'
- `n_neighbors`：默认检索邻居数量
- `metric`：相似度度量方式，支持'l2'（欧氏距离）和'ip'（内积）
- `**kwargs`：传递给嵌入模型的其他参数

## 注意事项

1. 在使用前，确保已经安装了所有依赖库。
2. 对于大型数据集，建议使用GPU加速FAISS检索。
3. 选择合适的嵌入算法对检索效果有重要影响，可以根据数据特点选择不同的嵌入算法。
4. 更新索引操作会重建整个索引，对于频繁更新的场景可能会影响性能。

## 许可证

MIT
