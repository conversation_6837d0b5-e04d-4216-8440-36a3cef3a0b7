"""
Data models for the AI Hedge Fund module.

This file defines the data models used for financial data.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class Price(BaseModel):
    """Price data for a stock."""
    ticker: str
    time: str
    open: float
    high: float
    low: float
    close: float
    volume: int


class PriceResponse(BaseModel):
    """Response from the price API."""
    prices: List[Price]


class FinancialMetrics(BaseModel):
    """Financial metrics for a company."""
    ticker: str
    report_period: str
    period: str
    
    # Valuation metrics
    pe_ratio: Optional[float] = None
    price_to_book: Optional[float] = None
    price_to_sales: Optional[float] = None
    ev_to_ebitda: Optional[float] = None
    market_cap: Optional[float] = None
    
    # Profitability metrics
    return_on_equity: Optional[float] = None
    return_on_assets: Optional[float] = None
    profit_margin: Optional[float] = None
    operating_margin: Optional[float] = None
    
    # Liquidity metrics
    current_ratio: Optional[float] = None
    quick_ratio: Optional[float] = None
    
    # Debt metrics
    debt_to_equity: Optional[float] = None
    interest_coverage: Optional[float] = None
    
    # Growth metrics
    revenue_growth: Optional[float] = None
    earnings_growth: Optional[float] = None


class FinancialMetricsResponse(BaseModel):
    """Response from the financial metrics API."""
    financial_metrics: List[FinancialMetrics]


class LineItem(BaseModel):
    """Financial line item for a company."""
    ticker: str
    report_period: str
    period: str
    
    # Balance sheet items
    total_assets: Optional[float] = None
    total_liabilities: Optional[float] = None
    outstanding_shares: Optional[float] = None
    
    # Income statement items
    revenue: Optional[float] = None
    net_income: Optional[float] = None
    
    # Cash flow items
    capital_expenditure: Optional[float] = None
    depreciation_and_amortization: Optional[float] = None
    dividends_and_other_cash_distributions: Optional[float] = None
    issuance_or_purchase_of_equity_shares: Optional[float] = None


class LineItemResponse(BaseModel):
    """Response from the line items API."""
    search_results: List[LineItem]


class InsiderTrade(BaseModel):
    """Insider trade data."""
    ticker: str
    filing_date: str
    transaction_date: Optional[str] = None
    insider_name: str
    title: Optional[str] = None
    transaction_type: str
    shares: int
    price: Optional[float] = None
    value: Optional[float] = None


class InsiderTradeResponse(BaseModel):
    """Response from the insider trades API."""
    insider_trades: List[InsiderTrade]


class CompanyNews(BaseModel):
    """Company news data."""
    ticker: str
    date: str
    title: str
    summary: str
    url: str
    source: str
    sentiment: Optional[float] = None


class CompanyNewsResponse(BaseModel):
    """Response from the company news API."""
    news: List[CompanyNews]
