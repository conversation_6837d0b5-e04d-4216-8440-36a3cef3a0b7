from fastapi import FastAPI
from dotenv import load_dotenv
from config.config_loader import ConfigLoader
from rag_graphs.news_rag_graph.ingestion import DocumentSyncManager
from rest_api.routes import (
    # stock_routes,
    news_routes,
    user_routes)
from utils.logger import logger
from scraper.scraper_factory import StockScraperFactory, NewsScraperFactory
from datetime import datetime
from db.db_factory import DBFactory
from contextlib import asynccontextmanager

import asyncio
import os

# Load .env
load_dotenv()

# Initialize ConfigLoader
config_loader = ConfigLoader(config_file="config/config.json")

# Load configurations
SCRAPE_TICKERS = config_loader.get("SCRAPE_TICKERS")
SCRAPING_INTERVAL = config_loader.get("SCRAPING_INTERVAL", 3600)

if not SCRAPE_TICKERS:
    raise ValueError("No tickers found in config.json. Please check the configuration.")

def get_all_favorite_tickers():
    """
    从数据库获取所有用户的自选股列表

    返回:
        list: 所有用户自选股的股票代码列表
    """
    try:
        # 获取数据库客户端
        db_client = DBFactory.get_db_client()

        # 使用fetch_query方法执行查询
        query = "SELECT DISTINCT symbol FROM user_favorites"
        results, _ = db_client.fetch_query(query)

        # 提取股票代码
        tickers = [row[0] for row in results]

        logger.info(f"从数据库获取到 {len(tickers)} 个自选股代码")
        return tickers
    except Exception as e:
        logger.error(f"获取自选股列表失败: {e}")
        return []
    finally:
        if 'db_client' in locals():
            db_client.close()

def get_all_favorite_ticker_names():
    """
    从数据库获取所有用户的自选股列表，包含代码和名称

    返回:
        list: 所有用户自选股的股票代码和名称列表，格式为[(symbol, name), ...]
    """
    try:
        # 获取数据库客户端
        db_client = DBFactory.get_db_client()

        # 使用fetch_query方法执行查询
        query = "SELECT DISTINCT symbol, name FROM user_favorites"
        results, _ = db_client.fetch_query(query)

        # 提取股票代码和名称
        ticker_names = [(row[0], row[1]) for row in results]

        logger.info(f"从数据库获取到 {len(ticker_names)} 个自选股代码和名称")
        return ticker_names
    except Exception as e:
        logger.error(f"获取自选股代码和名称失败: {e}")
        return []
    finally:
        if 'db_client' in locals():
            db_client.close()

async def run_scrapers_in_background():
    """
    Run news_scraper and stock_scraper in parallel in the background.
    """
    loop = asyncio.get_event_loop()

    # 获取配置中的股票代码
    config_tickers = SCRAPE_TICKERS

    # 获取用户自选股代码
    favorite_tickers = get_all_favorite_tickers()

    # 获取用户自选股代码和名称
    favorite_ticker_names = get_all_favorite_ticker_names()

    # 合并并去重股票代码（用于股票数据抽取）
    all_tickers = list(set(config_tickers + favorite_tickers))

    # 为新闻抽取准备搜索关键词
    # 对于配置中的股票，直接使用代码
    news_search_terms = config_tickers.copy()

    # 对于自选股，使用"代码 名称"的形式进行搜索
    for symbol, name in favorite_ticker_names:
        # 如果是中国股票或期货（包含点号），使用名称进行搜索
        if '.' in symbol:
            news_search_terms.append(f"{name}")
        else:
            # 如果是美股，使用代码进行搜索
            news_search_terms.append(symbol)

    logger.info(f"共计抽取 {len(all_tickers)} 个股票代码，其中配置中 {len(config_tickers)} 个，自选股 {len(favorite_tickers)} 个")
    logger.info(f"新闻搜索关键词数量: {len(news_search_terms)} 个")

    stock_factory = StockScraperFactory()
    stock_scraper = stock_factory.create_scraper()

    news_factory = NewsScraperFactory()
    news_scraper = news_factory.create_scraper(collection_name=os.getenv("COLLECTION_NAME"),
                                               scrape_num_articles=int(os.getenv("SCRAPE_NUM_ARTICLES", 1)))

    # Run both scrapers concurrently
    await asyncio.gather(
        loop.run_in_executor(None, news_scraper.scrape_all_tickers, news_search_terms),
        loop.run_in_executor(None, stock_scraper.scrape_all_tickers, all_tickers)
    )
    # Sync scraped docs in Vector DB
    DocumentSyncManager().sync_documents()

async def scrape_in_interval(interval: int):
    """
    Runs the scraping task at regular intervals.
    """
    while True:
        logger.info(f"Starting scraping at {datetime.now()}")

        # Run scrapers in parallel
        await run_scrapers_in_background()

        hours = interval / 3600  # Convert seconds to hours
        logger.info(f"Scraping completed at {datetime.now()}. Next run in {hours:.2f} hours.")
        # Wait for the specified interval
        await asyncio.sleep(interval)

# 定义lifespan上下文管理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用程序生命周期事件处理
    """
    # 启动时执行
    task = asyncio.create_task(scrape_in_interval(SCRAPING_INTERVAL))
    yield
    # 关闭时执行（如果需要）
    task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        logger.info("数据抓取任务已取消")

# 初始化FastAPI应用程序，传入lifespan上下文管理器
app = FastAPI(lifespan=lifespan)

# Include routes
# app.include_router(stock_routes.router, prefix="/stock", tags=["Stock Data"])
app.include_router(news_routes.router, prefix="/news", tags=["News Articles"])
app.include_router(user_routes.router, prefix="/user", tags=["User Management"])

@app.get("/")
def root():
    return {"message": "Welcome to the Financial Data API"}

if __name__ == "__main__":
    import sys
    import os

    # 获取当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 将当前目录添加到Python路径
    # sys.path.insert(0, current_dir)
    print(current_dir)

    # 将项目根目录添加到Python路径
    sys.path.append(os.path.abspath(os.path.join(current_dir, '..')))
    
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
