"""
Backtest engine for the AI Hedge Fund.

This module provides functions for backtesting investment strategies.
"""

from typing import Dict, List, Any, Optional, Tuple, Callable
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

from ..tools.api import get_price_data
from ..portfolio.optimizer import optimize_portfolio


def run_backtest(
    strategy: Callable[[pd.DataFrame, Dict[str, Any]], Dict[str, float]],
    tickers: List[str],
    start_date: str,
    end_date: str,
    initial_capital: float = 100000.0,
    rebalance_frequency: str = "monthly",
    strategy_params: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Run a backtest for a given investment strategy.

    Args:
        strategy: Strategy function that takes price data and parameters and returns weights
        tickers: List of ticker symbols to include in the backtest
        start_date: Start date for the backtest (YYYY-MM-DD)
        end_date: End date for the backtest (YYYY-MM-DD)
        initial_capital: Initial capital for the backtest
        rebalance_frequency: Rebalancing frequency ('daily', 'weekly', 'monthly', 'quarterly')
        strategy_params: Additional parameters for the strategy function

    Returns:
        Dictionary with backtest results
    """
    # Initialize parameters
    if strategy_params is None:
        strategy_params = {}

    # Get price data for all tickers
    price_data = {}
    for ticker in tickers:
        ticker_data = get_price_data(ticker, start_date, end_date)
        if not ticker_data.empty:
            price_data[ticker] = ticker_data

    if not price_data:
        return {
            "status": "error",
            "message": "Failed to retrieve price data for any ticker",
        }

    # Create a DataFrame with all close prices
    close_prices = pd.DataFrame({ticker: data["close"] for ticker, data in price_data.items()})

    # Create a DataFrame with all adjusted close prices (for total return calculation)
    if "adj_close" in next(iter(price_data.values())).columns:
        adj_close_prices = pd.DataFrame({ticker: data["adj_close"] for ticker, data in price_data.items()})
    else:
        adj_close_prices = close_prices.copy()

    # Calculate returns
    returns = adj_close_prices.pct_change().dropna()

    # Determine rebalancing dates
    if rebalance_frequency == "daily":
        rebalance_dates = returns.index
    elif rebalance_frequency == "weekly":
        rebalance_dates = returns.index[returns.index.weekday == 4]  # Friday
    elif rebalance_frequency == "monthly":
        rebalance_dates = returns.index[returns.index.is_month_end]
    elif rebalance_frequency == "quarterly":
        rebalance_dates = returns.index[returns.index.is_quarter_end]
    else:
        return {
            "status": "error",
            "message": f"Invalid rebalance frequency: {rebalance_frequency}",
        }

    # Add the first date as a rebalancing date
    if len(rebalance_dates) > 0 and returns.index[0] not in rebalance_dates:
        rebalance_dates = returns.index[[0]].append(rebalance_dates)

    # Initialize portfolio
    portfolio = {
        "date": [],
        "capital": [],
        "weights": [],
        "positions": [],
        "cash": [],
    }

    # Initialize with cash
    portfolio["date"].append(returns.index[0])
    portfolio["capital"].append(initial_capital)
    portfolio["weights"].append({})
    portfolio["positions"].append({})
    portfolio["cash"].append(initial_capital)

    # Run the backtest
    current_capital = initial_capital
    current_weights = {}
    current_positions = {}
    current_cash = initial_capital

    for i, date in enumerate(returns.index):
        # Check if this is a rebalancing date
        is_rebalance_date = date in rebalance_dates

        if is_rebalance_date:
            # Get the strategy weights
            try:
                # Get data up to the current date
                historical_data = {
                    "close": close_prices.loc[:date],
                    "returns": returns.loc[:date],
                }

                # Run the strategy
                new_weights = strategy(historical_data, strategy_params)

                # Validate weights
                if not isinstance(new_weights, dict):
                    return {
                        "status": "error",
                        "message": "Strategy function must return a dictionary of weights",
                    }

                # Normalize weights to ensure they sum to 1
                weight_sum = sum(new_weights.values())
                if weight_sum > 0:
                    new_weights = {ticker: weight / weight_sum for ticker, weight in new_weights.items()}

                # Update current weights
                current_weights = new_weights

                # Calculate new positions
                current_positions = {}
                allocated_capital = 0

                for ticker, weight in current_weights.items():
                    if ticker in close_prices.columns and date in close_prices.index:
                        price = close_prices.loc[date, ticker]
                        position_value = current_capital * weight
                        shares = position_value / price
                        current_positions[ticker] = shares
                        allocated_capital += position_value

                # Update cash
                current_cash = current_capital - allocated_capital

            except Exception as e:
                return {
                    "status": "error",
                    "message": f"Error during rebalancing: {str(e)}",
                }

        # Calculate portfolio value
        portfolio_value = current_cash

        for ticker, shares in current_positions.items():
            if ticker in close_prices.columns and date in close_prices.index:
                price = close_prices.loc[date, ticker]
                portfolio_value += shares * price

        # Update current capital
        current_capital = portfolio_value

        # Store portfolio state
        portfolio["date"].append(date)
        portfolio["capital"].append(current_capital)
        portfolio["weights"].append(current_weights.copy())
        portfolio["positions"].append(current_positions.copy())
        portfolio["cash"].append(current_cash)

    # Calculate portfolio returns
    portfolio_returns = pd.Series(
        [0.0] + [(portfolio["capital"][i] / portfolio["capital"][i-1] - 1) for i in range(1, len(portfolio["capital"]))],
        index=portfolio["date"],
    )

    # Calculate benchmark returns (using equal-weighted portfolio)
    benchmark_weights = {ticker: 1.0 / len(tickers) for ticker in tickers}
    benchmark_returns = returns.dot(pd.Series(benchmark_weights))

    # Calculate cumulative returns
    portfolio_cumulative_returns = (1 + portfolio_returns).cumprod() - 1
    benchmark_cumulative_returns = (1 + benchmark_returns).cumprod() - 1

    # Calculate metrics
    metrics = calculate_backtest_metrics(portfolio_returns, benchmark_returns)

    # Create result dictionary
    result = {
        "status": "success",
        "initial_capital": initial_capital,
        "final_capital": portfolio["capital"][-1],
        "total_return": (portfolio["capital"][-1] / initial_capital - 1),
        "metrics": metrics,
        "portfolio_returns": portfolio_returns.tolist(),
        "benchmark_returns": benchmark_returns.tolist(),
        "portfolio_cumulative_returns": portfolio_cumulative_returns.tolist(),
        "benchmark_cumulative_returns": benchmark_cumulative_returns.tolist(),
        "dates": [d.strftime("%Y-%m-%d") for d in portfolio["date"]],
        "rebalance_dates": [d.strftime("%Y-%m-%d") for d in rebalance_dates],
        "final_weights": portfolio["weights"][-1],
        "start_date": start_date,
        "end_date": end_date,
        "tickers": tickers,
        "rebalance_frequency": rebalance_frequency,
    }

    return result


def calculate_backtest_metrics(
    portfolio_returns: pd.Series,
    benchmark_returns: pd.Series,
    risk_free_rate: float = 0.0,
) -> Dict[str, float]:
    """
    Calculate performance metrics for a backtest.

    Args:
        portfolio_returns: Series of portfolio returns
        benchmark_returns: Series of benchmark returns
        risk_free_rate: Annual risk-free rate

    Returns:
        Dictionary with performance metrics
    """
    # Annualization factor (assuming daily returns)
    annualization_factor = 252
    daily_risk_free_rate = (1 + risk_free_rate) ** (1 / annualization_factor) - 1

    # Calculate total return
    portfolio_total_return = (1 + portfolio_returns).prod() - 1
    benchmark_total_return = (1 + benchmark_returns).prod() - 1

    # Calculate annualized return
    portfolio_ann_return = (1 + portfolio_total_return) ** (annualization_factor / len(portfolio_returns)) - 1
    benchmark_ann_return = (1 + benchmark_total_return) ** (annualization_factor / len(benchmark_returns)) - 1

    # Calculate volatility
    portfolio_volatility = portfolio_returns.std() * np.sqrt(annualization_factor)
    benchmark_volatility = benchmark_returns.std() * np.sqrt(annualization_factor)

    # Calculate Sharpe ratio
    portfolio_excess_returns = portfolio_returns - daily_risk_free_rate
    benchmark_excess_returns = benchmark_returns - daily_risk_free_rate

    portfolio_sharpe = portfolio_excess_returns.mean() / portfolio_returns.std() * np.sqrt(annualization_factor) if portfolio_returns.std() > 0 else 0
    benchmark_sharpe = benchmark_excess_returns.mean() / benchmark_returns.std() * np.sqrt(annualization_factor) if benchmark_returns.std() > 0 else 0

    # Calculate Sortino ratio (downside risk only)
    portfolio_downside_returns = portfolio_returns.copy()
    portfolio_downside_returns[portfolio_downside_returns > 0] = 0
    portfolio_downside_risk = portfolio_downside_returns.std() * np.sqrt(annualization_factor)

    benchmark_downside_returns = benchmark_returns.copy()
    benchmark_downside_returns[benchmark_downside_returns > 0] = 0
    benchmark_downside_risk = benchmark_downside_returns.std() * np.sqrt(annualization_factor)

    portfolio_sortino = (portfolio_ann_return - risk_free_rate) / portfolio_downside_risk if portfolio_downside_risk > 0 else 0
    benchmark_sortino = (benchmark_ann_return - risk_free_rate) / benchmark_downside_risk if benchmark_downside_risk > 0 else 0

    # Calculate maximum drawdown
    portfolio_cumulative_returns = (1 + portfolio_returns).cumprod()
    portfolio_running_max = portfolio_cumulative_returns.cummax()
    portfolio_drawdown = (portfolio_cumulative_returns / portfolio_running_max) - 1
    portfolio_max_drawdown = portfolio_drawdown.min()

    benchmark_cumulative_returns = (1 + benchmark_returns).cumprod()
    benchmark_running_max = benchmark_cumulative_returns.cummax()
    benchmark_drawdown = (benchmark_cumulative_returns / benchmark_running_max) - 1
    benchmark_max_drawdown = benchmark_drawdown.min()

    # Calculate Calmar ratio
    portfolio_calmar = portfolio_ann_return / abs(portfolio_max_drawdown) if portfolio_max_drawdown < 0 else 0
    benchmark_calmar = benchmark_ann_return / abs(benchmark_max_drawdown) if benchmark_max_drawdown < 0 else 0

    # Calculate beta
    covariance = portfolio_returns.cov(benchmark_returns)
    variance = benchmark_returns.var()
    beta = covariance / variance if variance > 0 else 1.0

    # Calculate alpha (Jensen's alpha)
    alpha = portfolio_ann_return - (risk_free_rate + beta * (benchmark_ann_return - risk_free_rate))

    # Calculate information ratio
    tracking_error = (portfolio_returns - benchmark_returns).std() * np.sqrt(annualization_factor)
    information_ratio = (portfolio_ann_return - benchmark_ann_return) / tracking_error if tracking_error > 0 else 0

    # Calculate win rate
    win_rate = (portfolio_returns > 0).mean()
    benchmark_win_rate = (benchmark_returns > 0).mean()

    # Calculate average win/loss
    avg_win = portfolio_returns[portfolio_returns > 0].mean() if len(portfolio_returns[portfolio_returns > 0]) > 0 else 0
    avg_loss = portfolio_returns[portfolio_returns < 0].mean() if len(portfolio_returns[portfolio_returns < 0]) > 0 else 0
    win_loss_ratio = abs(avg_win / avg_loss) if avg_loss < 0 else 0

    # Return metrics
    return {
        "total_return": float(portfolio_total_return),
        "benchmark_total_return": float(benchmark_total_return),
        "annualized_return": float(portfolio_ann_return),
        "benchmark_annualized_return": float(benchmark_ann_return),
        "volatility": float(portfolio_volatility),
        "benchmark_volatility": float(benchmark_volatility),
        "sharpe_ratio": float(portfolio_sharpe),
        "benchmark_sharpe_ratio": float(benchmark_sharpe),
        "sortino_ratio": float(portfolio_sortino),
        "benchmark_sortino_ratio": float(benchmark_sortino),
        "max_drawdown": float(portfolio_max_drawdown),
        "benchmark_max_drawdown": float(benchmark_max_drawdown),
        "calmar_ratio": float(portfolio_calmar),
        "benchmark_calmar_ratio": float(benchmark_calmar),
        "beta": float(beta),
        "alpha": float(alpha),
        "information_ratio": float(information_ratio),
        "tracking_error": float(tracking_error),
        "win_rate": float(win_rate),
        "benchmark_win_rate": float(benchmark_win_rate),
        "win_loss_ratio": float(win_loss_ratio),
    }


def generate_backtest_report(
    backtest_result: Dict[str, Any],
    include_monthly_returns: bool = True,
    include_drawdowns: bool = True,
) -> str:
    """
    Generate an HTML report for a backtest.

    Args:
        backtest_result: Dictionary with backtest results
        include_monthly_returns: Whether to include monthly returns table
        include_drawdowns: Whether to include drawdowns table

    Returns:
        HTML string with the backtest report
    """
    if backtest_result.get("status") != "success":
        return f"<h2>Error</h2><p>{backtest_result.get('message', 'Unknown error')}</p>"

    # Extract data
    metrics = backtest_result.get("metrics", {})
    start_date = backtest_result.get("start_date", "")
    end_date = backtest_result.get("end_date", "")
    tickers = backtest_result.get("tickers", [])
    rebalance_frequency = backtest_result.get("rebalance_frequency", "")
    initial_capital = backtest_result.get("initial_capital", 0.0)
    final_capital = backtest_result.get("final_capital", 0.0)
    final_weights = backtest_result.get("final_weights", {})

    # Create HTML report
    html_parts = [
        f"<h2>Backtest Report</h2>",
        f"<p><b>Period:</b> {start_date} to {end_date}</p>",
        f"<p><b>Universe:</b> {', '.join(tickers)}</p>",
        f"<p><b>Rebalance Frequency:</b> {rebalance_frequency.capitalize()}</p>",
        f"<p><b>Initial Capital:</b> ${initial_capital:,.2f}</p>",
        f"<p><b>Final Capital:</b> ${final_capital:,.2f}</p>",
        f"<p><b>Total Return:</b> {metrics.get('total_return', 0.0) * 100:.2f}% (Benchmark: {metrics.get('benchmark_total_return', 0.0) * 100:.2f}%)</p>",
        f"<p><b>Annualized Return:</b> {metrics.get('annualized_return', 0.0) * 100:.2f}% (Benchmark: {metrics.get('benchmark_annualized_return', 0.0) * 100:.2f}%)</p>",
    ]

    # Add performance metrics table
    html_parts.append("<h3>Performance Metrics</h3>")
    html_parts.append("<table border='1' cellpadding='5'>")
    html_parts.append("<tr><th>Metric</th><th>Portfolio</th><th>Benchmark</th></tr>")

    # Add key metrics
    html_parts.append(f"<tr><td>Annualized Return</td><td>{metrics.get('annualized_return', 0.0) * 100:.2f}%</td><td>{metrics.get('benchmark_annualized_return', 0.0) * 100:.2f}%</td></tr>")
    html_parts.append(f"<tr><td>Volatility</td><td>{metrics.get('volatility', 0.0) * 100:.2f}%</td><td>{metrics.get('benchmark_volatility', 0.0) * 100:.2f}%</td></tr>")
    html_parts.append(f"<tr><td>Sharpe Ratio</td><td>{metrics.get('sharpe_ratio', 0.0):.2f}</td><td>{metrics.get('benchmark_sharpe_ratio', 0.0):.2f}</td></tr>")
    html_parts.append(f"<tr><td>Sortino Ratio</td><td>{metrics.get('sortino_ratio', 0.0):.2f}</td><td>{metrics.get('benchmark_sortino_ratio', 0.0):.2f}</td></tr>")
    html_parts.append(f"<tr><td>Maximum Drawdown</td><td>{metrics.get('max_drawdown', 0.0) * 100:.2f}%</td><td>{metrics.get('benchmark_max_drawdown', 0.0) * 100:.2f}%</td></tr>")
    html_parts.append(f"<tr><td>Calmar Ratio</td><td>{metrics.get('calmar_ratio', 0.0):.2f}</td><td>{metrics.get('benchmark_calmar_ratio', 0.0):.2f}</td></tr>")
    html_parts.append(f"<tr><td>Beta</td><td>{metrics.get('beta', 0.0):.2f}</td><td>1.00</td></tr>")
    html_parts.append(f"<tr><td>Alpha</td><td>{metrics.get('alpha', 0.0) * 100:.2f}%</td><td>0.00%</td></tr>")
    html_parts.append(f"<tr><td>Information Ratio</td><td>{metrics.get('information_ratio', 0.0):.2f}</td><td>N/A</td></tr>")
    html_parts.append(f"<tr><td>Tracking Error</td><td>{metrics.get('tracking_error', 0.0) * 100:.2f}%</td><td>N/A</td></tr>")
    html_parts.append(f"<tr><td>Win Rate</td><td>{metrics.get('win_rate', 0.0) * 100:.2f}%</td><td>{metrics.get('benchmark_win_rate', 0.0) * 100:.2f}%</td></tr>")
    html_parts.append(f"<tr><td>Win/Loss Ratio</td><td>{metrics.get('win_loss_ratio', 0.0):.2f}</td><td>N/A</td></tr>")

    html_parts.append("</table>")

    # Add final portfolio weights
    if final_weights:
        html_parts.append("<h3>Final Portfolio Weights</h3>")
        html_parts.append("<table border='1' cellpadding='5'>")
        html_parts.append("<tr><th>Ticker</th><th>Weight</th></tr>")

        # Sort weights by value (descending)
        sorted_weights = sorted(final_weights.items(), key=lambda x: abs(x[1]), reverse=True)

        for ticker, weight in sorted_weights:
            html_parts.append(f"<tr><td>{ticker}</td><td>{weight * 100:.2f}%</td></tr>")

        html_parts.append("</table>")

    # Add monthly returns table if requested
    if include_monthly_returns:
        # Convert returns to DataFrame
        dates = pd.to_datetime(backtest_result.get("dates", []))
        portfolio_returns = pd.Series(backtest_result.get("portfolio_returns", []), index=dates)

        # Calculate monthly returns
        monthly_returns = portfolio_returns.resample("M").apply(lambda x: (1 + x).prod() - 1)

        # Create monthly returns table by year and month
        if not monthly_returns.empty:
            # Create a pivot table of monthly returns by year and month
            monthly_returns_table = pd.DataFrame({
                "Year": monthly_returns.index.year,
                "Month": monthly_returns.index.month,
                "Return": monthly_returns.values,
            })

            pivot_table = monthly_returns_table.pivot(index="Year", columns="Month", values="Return")

            # Add annual returns
            pivot_table["Annual"] = pivot_table.apply(lambda x: (1 + x.dropna()).prod() - 1, axis=1)

            # Add HTML table
            html_parts.append("<h3>Monthly Returns</h3>")
            html_parts.append("<table border='1' cellpadding='5'>")

            # Add header row with month names
            month_names = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Annual"]
            html_parts.append("<tr><th>Year</th>" + "".join([f"<th>{month}</th>" for month in month_names]) + "</tr>")

            # Add data rows
            for year, row in pivot_table.iterrows():
                html_parts.append(f"<tr><td>{year}</td>")

                for month in range(1, 13):
                    if month in row and not pd.isna(row[month]):
                        cell_value = row[month] * 100
                        cell_color = "green" if cell_value >= 0 else "red"
                        html_parts.append(f"<td style='color: {cell_color}'>{cell_value:.2f}%</td>")
                    else:
                        html_parts.append("<td></td>")

                # Add annual return
                if "Annual" in row and not pd.isna(row["Annual"]):
                    annual_value = row["Annual"] * 100
                    annual_color = "green" if annual_value >= 0 else "red"
                    html_parts.append(f"<td style='color: {annual_color}'><b>{annual_value:.2f}%</b></td>")
                else:
                    html_parts.append("<td></td>")

                html_parts.append("</tr>")

            html_parts.append("</table>")

    # Add drawdowns table if requested
    if include_drawdowns:
        # Convert returns to DataFrame
        dates = pd.to_datetime(backtest_result.get("dates", []))
        portfolio_returns = pd.Series(backtest_result.get("portfolio_returns", []), index=dates)

        # Calculate drawdowns
        cumulative_returns = (1 + portfolio_returns).cumprod()
        running_max = cumulative_returns.cummax()
        drawdowns = (cumulative_returns / running_max) - 1

        # Find drawdown periods
        is_in_drawdown = drawdowns < 0
        drawdown_starts = is_in_drawdown.astype(int).diff() == 1
        drawdown_ends = is_in_drawdown.astype(int).diff() == -1

        # Get start and end dates of drawdowns
        drawdown_periods = []
        current_start = None
        current_drawdown = 0

        for date, value in drawdowns.items():
            if value < 0:
                if current_start is None:
                    current_start = date
                current_drawdown = min(current_drawdown, value)
            elif current_start is not None:
                # Drawdown ended
                drawdown_periods.append({
                    "start": current_start,
                    "end": date,
                    "depth": current_drawdown,
                    "length": (date - current_start).days,
                    "recovery": (date - current_start).days,
                })
                current_start = None
                current_drawdown = 0

        # Add ongoing drawdown if any
        if current_start is not None:
            drawdown_periods.append({
                "start": current_start,
                "end": drawdowns.index[-1],
                "depth": current_drawdown,
                "length": (drawdowns.index[-1] - current_start).days,
                "recovery": "Ongoing",
            })

        # Sort drawdowns by depth (worst first)
        drawdown_periods.sort(key=lambda x: x["depth"])

        # Add HTML table for top 5 drawdowns
        if drawdown_periods:
            html_parts.append("<h3>Top Drawdowns</h3>")
            html_parts.append("<table border='1' cellpadding='5'>")
            html_parts.append("<tr><th>#</th><th>Start Date</th><th>End Date</th><th>Depth</th><th>Length (Days)</th><th>Recovery (Days)</th></tr>")

            for i, period in enumerate(drawdown_periods[:5]):
                html_parts.append(f"<tr>")
                html_parts.append(f"<td>{i+1}</td>")
                html_parts.append(f"<td>{period['start'].strftime('%Y-%m-%d')}</td>")
                html_parts.append(f"<td>{period['end'].strftime('%Y-%m-%d')}</td>")
                html_parts.append(f"<td>{period['depth'] * 100:.2f}%</td>")
                html_parts.append(f"<td>{period['length']}</td>")
                html_parts.append(f"<td>{period['recovery']}</td>")
                html_parts.append(f"</tr>")

            html_parts.append("</table>")

    return "<br>".join(html_parts)
