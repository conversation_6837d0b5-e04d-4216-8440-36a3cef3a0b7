// 全局变量
let currentPortfolio = null;
let performanceChart = null;
let isLoggedIn = false;
let currentUser = null;
let accessToken = null;

// 使用config.js中定义的API基础URL和版本
// API_BASE_URL 和 API_VERSION 变量已在config.js中定义

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化事件监听器
    initEventListeners();

    // 检查本地存储中是否有登录信息
    checkLoginStatus();

    // 初始化导航菜单
    if (typeof initNavbar === 'function') {
        initNavbar();
    }
});

// 初始化事件监听器
function initEventListeners() {
    // 登录按钮点击事件
    document.getElementById('loginBtn').addEventListener('click', function() {
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    });

    // 注册按钮点击事件
    document.getElementById('registerBtn').addEventListener('click', function() {
        const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
        registerModal.show();
    });

    // 登录表单提交事件
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;
        login(username, password);
    });

    // 注册表单提交事件
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const username = document.getElementById('registerUsername').value;
        const email = document.getElementById('registerEmail').value;
        const fullName = document.getElementById('registerFullName').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('registerConfirmPassword').value;

        if (password !== confirmPassword) {
            alert('两次输入的密码不一致');
            return;
        }

        register(username, email, fullName, password);
    });

    // 创建投资组合按钮点击事件
    document.getElementById('createPortfolioBtn').addEventListener('click', function() {
        checkSubscriptionAndShowModal();
    });

    // 创建第一个投资组合按钮点击事件
    document.getElementById('createFirstPortfolioBtn').addEventListener('click', function() {
        checkSubscriptionAndShowModal();
    });

    // 升级订阅按钮点击事件
    document.getElementById('upgradeToAdvancedBtn').addEventListener('click', function() {
        upgradeSubscription('advanced');
    });

    document.getElementById('upgradeToProfessionalBtn').addEventListener('click', function() {
        upgradeSubscription('professional');
    });

    // 创建投资组合表单提交事件
    document.getElementById('createPortfolioForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const portfolioName = document.getElementById('portfolioNameInput').value;
        const description = document.getElementById('portfolioDescription').value;
        const initialCash = document.getElementById('initialCash').value;

        createPortfolio(portfolioName, description, initialCash);
    });

    // 移除返回列表按钮点击事件，因为我们现在使用标签页

    // 移除持仓类型切换按钮事件监听器，因为一个投资组合只能有一种账户类型

    // 交易策略按钮点击事件
    document.getElementById('strategyBtn').addEventListener('click', function() {
        // 如果有当前投资组合，则检查订阅级别并显示相应模态框
        if (currentPortfolio) {
            checkSubscriptionAndShowStrategyModal(currentPortfolio);
        }
    });

    // 切换显示/隐藏策略信息按钮点击事件
    document.getElementById('toggleStrategyBtn').addEventListener('click', function() {
        const strategyInfoSection = document.getElementById('strategyInfoSection');
        const toggleBtn = document.getElementById('toggleStrategyBtn');

        if (strategyInfoSection.style.display === 'none') {
            // 显示策略信息
            strategyInfoSection.style.display = 'block';
            toggleBtn.textContent = '隐藏策略信息';
        } else {
            // 隐藏策略信息
            strategyInfoSection.style.display = 'none';
            toggleBtn.textContent = '显示策略信息';
        }
    });

    // 保存策略设置按钮点击事件
    document.getElementById('saveStrategyBtn').addEventListener('click', function() {
        // 触发策略表单提交事件
        document.getElementById('strategyForm').dispatchEvent(new Event('submit'));
    });

    // 策略表单提交事件
    document.getElementById('strategyForm').addEventListener('submit', function(e) {
        e.preventDefault();
        if (!currentPortfolio) {
            alert('未选择投资组合');
            return;
        }

        const strategyStatus = document.getElementById('strategyStatus').value;
        const strategyName = document.getElementById('strategyName').value;
        const modelName = document.getElementById('modelName').value;
        const strategyParams = document.getElementById('strategyParams').value;
        const strategyDescription = document.getElementById('strategyDescription').value;

        // 验证策略参数是否为有效的JSON
        let parsedParams;
        try {
            parsedParams = JSON.parse(strategyParams);
        } catch (e) {
            alert('策略参数必须是有效的JSON格式');
            return;
        }

        // 保存策略设置
        saveStrategySettings(
            currentPortfolio,
            strategyStatus,
            strategyName,
            modelName,
            parsedParams,
            strategyDescription
        );
    });

    // 业绩周期按钮点击事件
    const periodButtons = document.querySelectorAll('[data-period]');
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有按钮的active类
            periodButtons.forEach(btn => btn.classList.remove('active'));
            // 添加当前按钮的active类
            this.classList.add('active');

            // 获取选中的周期
            const period = this.getAttribute('data-period');

            // 如果有当前投资组合，则更新业绩图表
            if (currentPortfolio) {
                fetchPortfolioPerformance(currentPortfolio, period);
            }
        });
    });
}

// 检查登录状态
function checkLoginStatus() {
    // 从本地存储中获取登录信息
    const storedToken = localStorage.getItem('accessToken');
    const storedUser = localStorage.getItem('user');

    if (storedToken && storedUser) {
        try {
            accessToken = storedToken;
            currentUser = JSON.parse(storedUser);
            isLoggedIn = true;

            // 更新UI显示已登录状态
            updateLoginUI();

            // 获取投资组合列表
            fetchPortfolios();

            // 检查用户订阅级别并更新菜单显示
            // 使用setTimeout确保其他脚本已加载
            setTimeout(() => {
                if (typeof checkSubscriptionAndUpdateMenu === 'function') {
                    checkSubscriptionAndUpdateMenu();
                }
            }, 300);
        } catch (e) {
            console.error('解析存储的用户信息失败:', e);
            // 清除无效的存储数据
            localStorage.removeItem('user');
            localStorage.removeItem('accessToken');
        }
    } else {
        // 显示登录提示
        document.getElementById('loginAlert').style.display = 'block';
        document.getElementById('portfolioSection').style.display = 'none';
    }
}

// 更新登录UI
function updateLoginUI() {
    const loginBtn = document.getElementById('loginBtn');
    const registerBtn = document.getElementById('registerBtn');

    if (isLoggedIn && currentUser) {
        // 创建用户下拉菜单
        const userDropdownContainer = document.createElement('div');
        userDropdownContainer.className = 'dropdown';

        // 创建下拉菜单按钮
        userDropdownContainer.innerHTML = `
            <div class="d-flex align-items-center user-dropdown" data-bs-toggle="dropdown" aria-expanded="false" role="button">
                <div class="user-avatar">${currentUser.username.charAt(0).toUpperCase()}</div>
                <span class="ms-2">${currentUser.username}</span>
                <i class="bi bi-chevron-down ms-1"></i>
            </div>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i>个人资料</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>账号设置</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
            </ul>
        `;

        // 替换登录和注册按钮
        loginBtn.parentNode.replaceChild(userDropdownContainer, loginBtn);
        registerBtn.style.display = 'none';

        // 添加退出登录按钮事件
        setTimeout(() => {
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('确定要退出登录吗？')) {
                        logout();
                    }
                });
            }
        }, 100);

        // 隐藏登录提示，显示投资组合区域
        document.getElementById('loginAlert').style.display = 'none';
        document.getElementById('portfolioSection').style.display = 'block';

        // 检查用户订阅级别并更新菜单显示
        if (typeof checkSubscriptionAndUpdateMenu === 'function') {
            checkSubscriptionAndUpdateMenu();
        }
    } else {
        // 恢复默认UI
        if (document.querySelector('.dropdown')) {
            // 如果已经有下拉菜单，则恢复原来的登录按钮
            const dropdown = document.querySelector('.dropdown');
            const newLoginBtn = document.createElement('button');
            newLoginBtn.className = 'btn btn-light me-2';
            newLoginBtn.id = 'loginBtn';
            newLoginBtn.textContent = '登录';
            dropdown.parentNode.replaceChild(newLoginBtn, dropdown);

            // 恢复注册按钮
            registerBtn.style.display = '';
            registerBtn.textContent = '注册';
            registerBtn.classList.remove('btn-outline-danger');
            registerBtn.classList.add('btn-outline-light');

            // 重新添加登录按钮事件
            newLoginBtn.addEventListener('click', function() {
                const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
                loginModal.show();
            });
        }

        // 显示登录提示，隐藏投资组合区域
        document.getElementById('loginAlert').style.display = 'block';
        document.getElementById('portfolioSection').style.display = 'none';
    }
}

// 登录
async function login(username, password) {
    // 显示加载状态
    const submitBtn = document.querySelector('#loginForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 登录中...';
    submitBtn.disabled = true;

    try {
        // 调用API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                'username': username,
                'password': password
            })
        });

        if (!response.ok) {
            throw new Error(`登录失败: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.access_token) {
            // 获取用户信息
            const userResponse = await fetch(`${API_BASE_URL}/${API_VERSION}/user/me`, {
                headers: {
                    'Authorization': `Bearer ${data.access_token}`
                }
            });

            if (!userResponse.ok) {
                throw new Error(`获取用户信息失败: ${userResponse.status}`);
            }

            const userData = await userResponse.json();

            // 从响应中提取用户数据
            const userInfo = userData.data || {
                username: data.username || username,
                email: data.email || '',
                full_name: data.full_name || ''
            };

            // 保存登录信息
            currentUser = userInfo;
            accessToken = data.access_token;
            isLoggedIn = true;

            // 保存到本地存储
            localStorage.setItem('user', JSON.stringify(currentUser));
            localStorage.setItem('accessToken', accessToken);

            // 更新UI
            updateLoginUI();

            // 获取投资组合列表
            fetchPortfolios();

            // 检查用户订阅级别并更新菜单显示
            if (typeof checkSubscriptionAndUpdateMenu === 'function') {
                checkSubscriptionAndUpdateMenu();
            }

            // 关闭登录模态框
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            loginModal.hide();

            // 显示成功消息
            alert('登录成功！');
        } else {
            throw new Error('登录响应中缺少访问令牌');
        }
    } catch (error) {
        console.error('登录失败:', error);
        alert(`登录失败: ${error.message}`);
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 注册
async function register(username, email, fullName, password) {
    // 显示加载状态
    const submitBtn = document.querySelector('#registerForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 注册中...';
    submitBtn.disabled = true;

    try {
        // 调用API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                email: email,
                full_name: fullName,
                password: password
            })
        });

        if (!response.ok) {
            throw new Error(`注册失败: ${response.status}`);
        }

        await response.json(); // 解析响应但不使用返回值

        // 关闭注册模态框
        const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
        registerModal.hide();

        // 重置表单
        document.getElementById('registerForm').reset();

        // 显示成功消息
        alert('注册成功！请登录您的账号。');

        // 打开登录模态框
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();

    } catch (error) {
        console.error('注册失败:', error);
        alert(`注册失败: ${error.message}`);
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 登出
function logout() {
    // 清除登录信息
    currentUser = null;
    accessToken = null;
    isLoggedIn = false;

    // 清除本地存储
    localStorage.removeItem('user');
    localStorage.removeItem('accessToken');

    // 更新UI
    updateLoginUI();
}

// 获取投资组合列表
async function fetchPortfolios() {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法获取投资组合列表');
        return;
    }

    // 显示加载状态
    const summaryBody = document.getElementById('portfolioSummaryBody');
    summaryBody.innerHTML = '<tr><td colspan="8" class="text-center py-4"><div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div> 正在加载投资组合数据...</td></tr>';

    // 隐藏详情区域
    document.getElementById('portfolioDetailSection').style.display = 'none';

    try {
        // 调用投资组合摘要API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/portfolios-summary`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取投资组合摘要失败: ${response.status}`);
        }

        const data = await response.json();

        // 调试输出API返回的数据结构
        console.log('API返回的投资组合摘要数据:', data);

        // 获取投资组合摘要数据
        let portfolioSummaries = [];

        // 处理数据结构
        if (data.code === 200 && data.data) {
            portfolioSummaries = data.data;
        }

        console.log('处理后的投资组合摘要列表:', portfolioSummaries);

        // 检查是否有投资组合
        if (portfolioSummaries && portfolioSummaries.length > 0) {
            // 渲染投资组合摘要表格
            renderPortfolioSummary(portfolioSummaries);
            document.getElementById('portfolioSummarySection').style.display = 'block';
            document.getElementById('portfolioListEmpty').style.display = 'none';
        } else {
            // 显示空列表提示
            document.getElementById('portfolioSummarySection').style.display = 'none';
            document.getElementById('portfolioListEmpty').style.display = 'block';
        }

    } catch (error) {
        console.error('获取投资组合摘要失败:', error);
        summaryBody.innerHTML = `<tr><td colspan="8" class="text-center py-4 text-danger">获取投资组合摘要失败: ${error.message}</td></tr>`;
    }
}

// 渲染投资组合摘要表格
function renderPortfolioSummary(portfolios) {
    const summaryBody = document.getElementById('portfolioSummaryBody');
    summaryBody.innerHTML = '';

    // 调试输出投资组合摘要数据
    console.log('渲染投资组合摘要数据:', portfolios);

    // 初始化汇总数据
    let totalAccountBalance = 0;
    let totalRealizedPnl = 0;
    let totalDailyPnl = 0;

    portfolios.forEach(portfolio => {
        // 处理数据 - 确保所有数值字段都转换为数字
        const accountBalance = typeof portfolio.account_balance === 'string' ? parseFloat(portfolio.account_balance) : portfolio.account_balance;
        const unitNetValue = typeof portfolio.unit_net_value === 'string' ? parseFloat(portfolio.unit_net_value) : portfolio.unit_net_value;
        const totalReturns = typeof portfolio.total_returns === 'string' ? parseFloat(portfolio.total_returns) : portfolio.total_returns;
        const realizedPnl = typeof portfolio.realized_pnl === 'string' ? parseFloat(portfolio.realized_pnl) : portfolio.realized_pnl;
        const dailyPnl = typeof portfolio.daily_pnl === 'string' ? parseFloat(portfolio.daily_pnl) : portfolio.daily_pnl;
        const runState = typeof portfolio.run_state === 'string' ? parseInt(portfolio.run_state) : portfolio.run_state;

        // 累加账户余额、已实现盈亏和日盈亏
        totalAccountBalance += isNaN(accountBalance) ? 0 : accountBalance;
        totalRealizedPnl += isNaN(realizedPnl) ? 0 : realizedPnl;
        totalDailyPnl += isNaN(dailyPnl) ? 0 : dailyPnl;

        // 设置盈亏颜色 - 符合中国市场习惯（红涨绿跌）
        const totalReturnsClass = totalReturns >= 0 ? 'text-danger' : 'text-success'; // 正值用红色，负值用绿色
        const realizedPnlClass = realizedPnl >= 0 ? 'text-danger' : 'text-success'; // 正值用红色，负值用绿色
        const dailyPnlClass = dailyPnl >= 0 ? 'text-danger' : 'text-success'; // 正值用红色，负值用绿色

        // 设置运行状态样式
        let statusBadge = '';
        switch (runState) {
            case 1:
                statusBadge = '<span class="badge bg-success">\u8fd0\u884c\u4e2d</span>';
                break;
            case 2:
                statusBadge = '<span class="badge bg-warning text-dark">\u6682\u505c</span>';
                break;
            default:
                statusBadge = '<span class="badge bg-light text-dark">\u672a\u542f\u7528</span>';
        }

        // 创建行
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><a href="#" class="portfolio-link" data-portfolio="${portfolio.name}">${portfolio.name}</a></td>
            <td>${formatCurrency(accountBalance || 0)}</td>
            <td>${unitNetValue ? unitNetValue.toFixed(4) : '1.0000'}</td>
            <td class="${totalReturnsClass}">${formatPercentage(totalReturns)}</td>
            <td class="${realizedPnlClass}">${formatCurrency(realizedPnl || 0)}</td>
            <td class="${dailyPnlClass}">${formatCurrency(dailyPnl || 0)}</td>
            <td>${statusBadge}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary view-portfolio" data-portfolio="${portfolio.name}">
                    <i class="bi bi-eye"></i> 查看
                </button>
            </td>
        `;
        summaryBody.appendChild(row);
    });

    // 添加汇总行
    if (portfolios.length > 0) {
        // 设置汇总行的样式
        const totalRealizedPnlClass = totalRealizedPnl >= 0 ? 'text-danger' : 'text-success';
        const totalDailyPnlClass = totalDailyPnl >= 0 ? 'text-danger' : 'text-success';

        // 创建汇总行
        const summaryRow = document.createElement('tr');
        summaryRow.className = 'table-light fw-bold';
        summaryRow.innerHTML = `
            <td class="text-end">汇总:</td>
            <td>${formatCurrency(totalAccountBalance)}</td>
            <td></td>
            <td></td>
            <td class="${totalRealizedPnlClass}">${formatCurrency(totalRealizedPnl)}</td>
            <td class="${totalDailyPnlClass}">${formatCurrency(totalDailyPnl)}</td>
            <td colspan="2"></td>
        `;
        summaryBody.appendChild(summaryRow);
    }

    // 添加查看投资组合按钮的点击事件
    document.querySelectorAll('.view-portfolio, .portfolio-link').forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const portfolioName = this.getAttribute('data-portfolio');
            viewPortfolio(portfolioName);
        });
    });
}

// 格式化百分比函数
function formatPercentage(value) {
    if (value === null || value === undefined) return '0.00%';
    return `${(value * 100).toFixed(2)}%`;
}

// 格式化货币函数
function formatCurrency(value) {
    if (value === null || value === undefined) return '¥0.00';
    return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(value);
}

// 查看投资组合详情
async function viewPortfolio(portfolioName) {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法查看投资组合详情');
        return;
    }

    // 更新UI状态 - 显示详情区域，隐藏空列表提示
    document.getElementById('portfolioDetailSection').style.display = 'block';
    document.getElementById('portfolioListEmpty').style.display = 'none';

    // 保存当前投资组合名称
    currentPortfolio = portfolioName;

    try {
        // 调试输出请求的投资组合名称
        console.log('请求投资组合详情:', portfolioName);

        // 调用API获取投资组合详情
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/portfolios/${portfolioName}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取投资组合详情失败: ${response.status}`);
        }

        const data = await response.json();

        // 调试输出API返回的数据
        console.log('投资组合详情数据:', data);

        // 兼容不同的数据结构
        if ((data.code === 200 && data.data) || data.portfolio) {

            // 获取投资组合数据，兼容不同的数据结构
            let portfolioData;

            if (data.code === 200 && data.data) {
                // API响应包裹在APIResponse中
                portfolioData = data.data;
            } else if (data.portfolio) {
                // 直接返回的portfolio对象
                portfolioData = data.portfolio;
            } else {
                // 其他情况
                portfolioData = data;
            }

            console.log('处理后的投资组合数据:', portfolioData);

            // 获取投资组合摘要数据，以获取更多财务信息
            try {
                const summaryResponse = await fetch(`${API_BASE_URL}/${API_VERSION}/portfolios-summary`, {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });

                if (summaryResponse.ok) {
                    const summaryData = await summaryResponse.json();
                    console.log('投资组合摘要数据:', summaryData);

                    if (summaryData.code === 200 && summaryData.data) {
                        // 找到当前投资组合的摘要数据
                        const portfolioSummary = summaryData.data.find(summary => summary.name === portfolioName);

                        if (portfolioSummary) {
                            console.log('当前投资组合摘要:', portfolioSummary);

                            // 将摘要数据合并到投资组合数据中
                            portfolioData = {
                                ...portfolioData,
                                account_balance: portfolioSummary.account_balance,
                                account_currency: portfolioSummary.account_currency,
                                units: portfolioSummary.units,
                                unit_net_value: portfolioSummary.unit_net_value,
                                total_returns: portfolioSummary.total_returns,
                                realized_pnl: portfolioSummary.realized_pnl,
                                daily_pnl: portfolioSummary.daily_pnl
                            };
                        }
                    }
                }
            } catch (summaryError) {
                console.warn('获取投资组合摘要数据失败:', summaryError);
                // 继续处理，即使摘要数据获取失败
            }

            // 渲染投资组合详情
            renderPortfolioDetail(portfolioData);

            // 获取投资组合业绩数据
            const activePeriodBtn = document.querySelector('[data-period].active');
            const period = activePeriodBtn ? activePeriodBtn.getAttribute('data-period') : '1m';
            fetchPortfolioPerformance(portfolioName, period);

            // 详情区域已在函数开始时显示
        } else {
            throw new Error('返回数据格式错误');
        }

    } catch (error) {
        console.error('查看投资组合详情失败:', error);
        alert(`查看投资组合详情失败: ${error.message}`);
    }
}

// 更新业绩指标显示
function updatePerformanceMetrics(metrics) {
    if (!metrics) return;

    try {
        // 处理数据，确保是数字类型
        const returnRate = typeof metrics.return_rate === 'string' ? parseFloat(metrics.return_rate) : metrics.return_rate;
        const annualReturn = typeof metrics.annual_return === 'string' ? parseFloat(metrics.annual_return) : metrics.annual_return;
        const sharpe = typeof metrics.sharpe === 'string' ? parseFloat(metrics.sharpe) : metrics.sharpe;
        const maxDrawdown = typeof metrics.max_drawdown === 'string' ? parseFloat(metrics.max_drawdown) : metrics.max_drawdown;
        const volatility = typeof metrics.volatility === 'string' ? parseFloat(metrics.volatility) : metrics.volatility;
        const sortino = typeof metrics.sortino === 'string' ? parseFloat(metrics.sortino) : metrics.sortino;
        const alpha = typeof metrics.alpha === 'string' ? parseFloat(metrics.alpha) : metrics.alpha;
        const beta = typeof metrics.beta === 'string' ? parseFloat(metrics.beta) : metrics.beta;

        // 更新统计指标显示
        document.getElementById('totalReturn').textContent = `${(returnRate * 100).toFixed(2)}%`;
        document.getElementById('annualReturn').textContent = `${(annualReturn * 100).toFixed(2)}%`;
        document.getElementById('sharpeRatio').textContent = sharpe.toFixed(2);
        document.getElementById('maxDrawdown').textContent = `${(maxDrawdown * 100).toFixed(2)}%`;
        document.getElementById('volatility').textContent = `${(volatility * 100).toFixed(2)}%`;
        document.getElementById('sortinoRatio').textContent = sortino.toFixed(2);
        document.getElementById('alpha').textContent = alpha.toFixed(2);
        document.getElementById('beta').textContent = beta.toFixed(2);

        // 设置颜色 - 符合中国市场习惯（红涨绿跌）
        const sharpeElement = document.getElementById('sharpeRatio');
        if (sharpe > 1) {
            sharpeElement.className = 'mb-0 text-danger'; // 正值用红色
        } else if (sharpe < 0) {
            sharpeElement.className = 'mb-0 text-success'; // 负值用绿色
        } else {
            sharpeElement.className = 'mb-0';
        }

        const sortinoElement = document.getElementById('sortinoRatio');
        if (sortino > 1) {
            sortinoElement.className = 'mb-0 text-danger'; // 正值用红色
        } else if (sortino < 0) {
            sortinoElement.className = 'mb-0 text-success'; // 负值用绿色
        } else {
            sortinoElement.className = 'mb-0';
        }

        const alphaElement = document.getElementById('alpha');
        if (alpha > 0) {
            alphaElement.className = 'mb-0 text-danger'; // 正值用红色
        } else if (alpha < 0) {
            alphaElement.className = 'mb-0 text-success'; // 负值用绿色
        } else {
            alphaElement.className = 'mb-0';
        }

        // 设置总收益率和年化收益率的颜色
        const totalReturnElement = document.getElementById('totalReturn');
        if (returnRate > 0) {
            totalReturnElement.className = 'mb-0 text-danger'; // 正值用红色
        } else if (returnRate < 0) {
            totalReturnElement.className = 'mb-0 text-success'; // 负值用绿色
        } else {
            totalReturnElement.className = 'mb-0';
        }

        const annualReturnElement = document.getElementById('annualReturn');
        if (annualReturn > 0) {
            annualReturnElement.className = 'mb-0 text-danger'; // 正值用红色
        } else if (annualReturn < 0) {
            annualReturnElement.className = 'mb-0 text-success'; // 负值用绿色
        } else {
            annualReturnElement.className = 'mb-0';
        }
    } catch (e) {
        console.error('更新业绩指标显示失败:', e);
    }
}

// 渲染投资组合详情
function renderPortfolioDetail(portfolio) {
    // 调试输出投资组合详情数据
    console.log('渲染投资组合详情:', portfolio);

    // 获取投资组合名称，兼容不同的属性名
    const portfolioName = portfolio.portfolio_name || portfolio.name || portfolio.id || '投资组合';

    // 更新标题
    document.getElementById('portfolioName').textContent = portfolioName;

    // 从投资组合数据中获取必要的信息
    // 获取账户类型，兼容不同的数据结构
    const accountType = portfolio.type || (portfolio.account ? portfolio.account.type : 'stock');

    // 从投资组合数据中获取总资产、现金余额、总收益和日收益
    // 首先尝试使用account_balance字段
    let accountBalance = typeof portfolio.account_balance === 'string' ?
                        parseFloat(portfolio.account_balance) : portfolio.account_balance;

    // 如果没有account_balance，尝试使用units和unit_net_value计算
    if (accountBalance === undefined || accountBalance === null || isNaN(accountBalance)) {
        const units = typeof portfolio.units === 'string' ? parseFloat(portfolio.units) : portfolio.units;
        const unitNetValue = typeof portfolio.unit_net_value === 'string' ? parseFloat(portfolio.unit_net_value) : portfolio.unit_net_value;

        if (units !== undefined && unitNetValue !== undefined && !isNaN(units) && !isNaN(unitNetValue)) {
            accountBalance = units * unitNetValue;
        } else {
            accountBalance = 0; // 默认值
        }
    }

    // 获取现金余额 - 如果没有直接的现金余额数据，假设持仓占总资产的30%
    const cashBalance = accountBalance * 0.7; // 默认值

    // 获取总收益和日收益
    let totalPnl = typeof portfolio.total_returns === 'string' ? parseFloat(portfolio.total_returns) : portfolio.total_returns;
    let dailyPnl = typeof portfolio.daily_pnl === 'string' ? parseFloat(portfolio.daily_pnl) : portfolio.daily_pnl;

    // 如果没有直接的总收益和日收益数据，使用默认值
    if (totalPnl === undefined || totalPnl === null || isNaN(totalPnl)) {
        totalPnl = 0;
    }

    if (dailyPnl === undefined || dailyPnl === null || isNaN(dailyPnl)) {
        dailyPnl = 0;
    }

    // 更新概览数据
    document.getElementById('totalValue').textContent = formatCurrency(accountBalance || 0);
    document.getElementById('cashBalance').textContent = formatCurrency(cashBalance || 0);

    // 设置总收益和日收益的颜色 - 符合中国市场习惯（红涨绿跌）
    const totalPnlElement = document.getElementById('totalPnl');
    totalPnlElement.textContent = formatCurrency(totalPnl);
    totalPnlElement.className = totalPnl >= 0 ? 'text-danger' : 'text-success'; // 正值用红色，负值用绿色

    const dailyPnlElement = document.getElementById('dailyPnl');
    dailyPnlElement.textContent = formatCurrency(dailyPnl);
    dailyPnlElement.className = dailyPnl >= 0 ? 'text-danger' : 'text-success'; // 正值用红色，负值用绿色

    // 渲染持仓明细
    // 清空持仓表格
    const stockPositionsBody = document.getElementById('stockPositionsBody');
    const futurePositionsBody = document.getElementById('futurePositionsBody');
    stockPositionsBody.innerHTML = '';
    futurePositionsBody.innerHTML = '';

    // 根据账户类型显示相应的持仓表格
    if (accountType === 'stock') {
        document.getElementById('stockPositionsTable').style.display = 'block';
        document.getElementById('futurePositionsTable').style.display = 'none';
    } else if (accountType === 'future') {
        document.getElementById('stockPositionsTable').style.display = 'none';
        document.getElementById('futurePositionsTable').style.display = 'block';
    }

    if (portfolio.positions && portfolio.positions.length > 0) {
        // 处理持仓数据
        portfolio.positions.forEach(position => {
                // 处理数据 - 确保所有数值字段都转换为数字
                const quantity = typeof position.quantity === 'string' ? parseInt(position.quantity) : position.quantity;
                const avgPrice = typeof position.avg_price === 'string' ? parseFloat(position.avg_price) : position.avg_price;
                const transactionCost = typeof position.transaction_cost === 'string' ? parseFloat(position.transaction_cost) : position.transaction_cost;
                const pnl = typeof position.pnl === 'string' ? parseFloat(position.pnl) : position.pnl;
                const costAtr = typeof position.cost_atr === 'string' ? parseFloat(position.cost_atr) : position.cost_atr;
                const drawdownAtr = typeof position.drawdown_atr === 'string' ? parseFloat(position.drawdown_atr) : position.drawdown_atr;

                // 设置盈亏颜色 - 符合中国市场习惯（红涨绿跌）
                const pnlClass = pnl >= 0 ? 'text-danger' : 'text-success'; // 正值用红色，负值用绿色

                if (accountType === 'stock') {
                    // 股票持仓
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${position.label || ''}</td>
                        <td>${position.name || ''}</td>
                        <td>${isNaN(quantity) ? 0 : quantity}</td>
                        <td>${avgPrice ? avgPrice.toFixed(2) : 'N/A'}</td>
                        <td>${transactionCost ? transactionCost.toFixed(2) : 'N/A'}</td>
                        <td class="${pnlClass}">${isNaN(pnl) ? '¥0.00' : formatCurrency(pnl)}</td>
                        <td>${costAtr ? costAtr.toFixed(2) : 'N/A'}</td>
                        <td>${drawdownAtr ? drawdownAtr.toFixed(2) : 'N/A'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary add-to-watchlist" data-symbol="${position.label || ''}" data-name="${position.name || ''}" data-type="stock">
                                <i class="bi bi-star"></i> 加入自选
                            </button>
                        </td>
                    `;
                    stockPositionsBody.appendChild(row);
                } else if (accountType === 'future') {
                    // 期货持仓
                    const row = document.createElement('tr');
                    // 设置方向的颜色 - 符合中国市场习惯（红涨绿跌）
                    const direction = position.direction || '';
                    // 兼容不同的方向表示方式：多/多头/买入 和 空/空头/卖出
                    // 多头用红色，空头用绿色，符合中国市场习惯
                    const directionClass = direction.includes('多') || direction.includes('买') ? 'text-danger' :
                                         direction.includes('空') || direction.includes('卖') ? 'text-success' : '';

                    row.innerHTML = `
                        <td>${position.label || ''}</td>
                        <td>${position.name || ''}</td>
                        <td class="${directionClass}">${direction}</td>
                        <td>${isNaN(quantity) ? 0 : quantity}</td>
                        <td>${avgPrice ? avgPrice.toFixed(2) : 'N/A'}</td>
                        <td>${transactionCost ? transactionCost.toFixed(2) : 'N/A'}</td>
                        <td class="${pnlClass}">${isNaN(pnl) ? '¥0.00' : formatCurrency(pnl)}</td>
                        <td>${costAtr ? costAtr.toFixed(2) : 'N/A'}</td>
                        <td>${drawdownAtr ? drawdownAtr.toFixed(2) : 'N/A'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary add-to-watchlist" data-symbol="${position.label || ''}" data-name="${position.name || ''}" data-type="future">
                                <i class="bi bi-star"></i> 加入自选
                            </button>
                        </td>
                    `;
                    futurePositionsBody.appendChild(row);
                }
            });
        } else {
            // 没有持仓数据
            stockPositionsBody.innerHTML = '<tr><td colspan="8" class="text-center py-4">暂无股票持仓</td></tr>';
            futurePositionsBody.innerHTML = '<tr><td colspan="9" class="text-center py-4">暂无期货持仓</td></tr>';
        }

    // 更新统计指标
    if (portfolio.performance_metrics) {
        // 使用Portfolio的PerformanceMetrics数据
        updatePerformanceMetrics(portfolio.performance_metrics);
    } else {
        // 没有指标数据，显示默认值
        document.getElementById('totalReturn').textContent = '0.00%';
        document.getElementById('annualReturn').textContent = '0.00%';
        document.getElementById('sharpeRatio').textContent = '0.00';
        document.getElementById('maxDrawdown').textContent = '0.00%';
        document.getElementById('volatility').textContent = '0.00%';
        document.getElementById('sortinoRatio').textContent = '0.00';
        document.getElementById('alpha').textContent = '0.00';
        document.getElementById('beta').textContent = '0.00';
    }

    // 渲染订单历史
    const ordersBody = document.getElementById('ordersBody');
    ordersBody.innerHTML = '';

    if (portfolio.orders && portfolio.orders.length > 0) {
        // 按时间排序，最新的订单在前
        const sortedOrders = [...portfolio.orders].sort((a, b) => {
            return new Date(b.order_time) - new Date(a.order_time);
        });

        sortedOrders.forEach(order => {
            const row = document.createElement('tr');

            // 处理数据
            const price = typeof order.price === 'string' ? parseFloat(order.price) : order.price;
            const quantity = typeof order.quantity === 'string' ? parseInt(order.quantity) : order.quantity;
            const pnl = typeof order.pnl === 'string' ? parseFloat(order.pnl) : order.pnl;
            // 符合中国市场习惯（红涨绿跌）
            const pnlClass = pnl >= 0 ? 'text-danger' : 'text-success'; // 正值用红色，负值用绿色

            // 格式化时间
            const orderTime = new Date(order.order_time);
            const formattedTime = orderTime.toLocaleString('zh-CN');

            // 设置方向和开平仓的样式 - 符合中国市场习惯
            // 买入用红色，卖出用绿色
            const sideClass = order.side === '买入' ? 'text-danger' : 'text-success';

            row.innerHTML = `
                <td>${order.label || ''}</td>
                <td>${order.name || ''}</td>
                <td>${price ? price.toFixed(2) : 'N/A'}</td>
                <td>${isNaN(quantity) ? 0 : quantity}</td>
                <td class="${sideClass}">${order.side || ''}</td>
                <td>${order.position_effect || ''}</td>
                <td class="${pnlClass}">${isNaN(pnl) ? '¥0.00' : formatCurrency(pnl)}</td>
                <td>${formattedTime}</td>
            `;

            ordersBody.appendChild(row);
        });
    } else {
        ordersBody.innerHTML = '<tr><td colspan="8" class="text-center py-4">暂无订单记录</td></tr>';
    }

    // 添加“加入自选”按钮的点击事件
    document.querySelectorAll('.add-to-watchlist').forEach(button => {
        button.addEventListener('click', async function() {
            const symbol = this.getAttribute('data-symbol');
            const name = this.getAttribute('data-name');
            const type = this.getAttribute('data-type'); // 获取标的类型

            // 显示加载状态
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 添加中...';
            this.disabled = true;

            // 调用添加自选股函数
            const success = await addToWatchlist(symbol, name, type);

            // 恢复按钮状态
            if (success) {
                this.innerHTML = '<i class="bi bi-star-fill"></i> 已添加';
                this.classList.remove('btn-outline-primary');
                this.classList.add('btn-success');
                this.disabled = true;

                // 显示成功提示
                const symbol = this.getAttribute('data-symbol');
                const name = this.getAttribute('data-name');
                alert(`已成功将 ${name}(${symbol}) 添加到自选股`);
            } else {
                this.innerHTML = originalText;
                this.disabled = false;
            }
        });
    });

    // 渲染策略信息
    renderStrategyInfo(portfolio);
}

// 渲染策略信息
function renderStrategyInfo(portfolio) {
    // 获取策略信息元素
    const strategyNameDisplay = document.getElementById('strategyNameDisplay');
    const modelNameDisplay = document.getElementById('modelNameDisplay');
    const strategyDescriptionDisplay = document.getElementById('strategyDescriptionDisplay');
    const strategyParamsDisplay = document.getElementById('strategyParamsDisplay');
    const strategyStatusBadge = document.getElementById('strategyStatusBadge');

    // 检查是否有策略信息
    if (portfolio.strategy_name && portfolio.strategy_name !== 'null' && portfolio.strategy_name !== 'undefined') {
        // 显示策略名称
        strategyNameDisplay.textContent = getStrategyDisplayName(portfolio.strategy_name);

        // 显示模型名称 - 兼容不同的字段名
        const modelName = portfolio.model_name || portfolio.onnx_name || '未设置';
        modelNameDisplay.textContent = modelName;

        // 显示策略状态
        const runState = parseInt(portfolio.run_state || '0');
        strategyStatusBadge.textContent = getStrategyStatusText(runState);
        strategyStatusBadge.className = getStrategyStatusClass(runState);

        // 尝试解析策略参数
        try {
            // 先检查是否有策略参数字段
            let strategyParams = null;

            if (portfolio.strategy_params) {
                // 新的API返回结构
                strategyParams = portfolio.strategy_params;
            } else if (portfolio.return_trend && portfolio.return_trend !== 'null' && portfolio.return_trend !== 'undefined') {
                // 旧的API返回结构，参数存储在return_trend字段中
                strategyParams = JSON.parse(portfolio.return_trend);
            }

            if (strategyParams) {
                strategyParamsDisplay.textContent = JSON.stringify(strategyParams, null, 2);
            } else {
                strategyParamsDisplay.textContent = '未设置参数';
            }
        } catch (e) {
            console.error('解析策略参数失败:', e);
            strategyParamsDisplay.textContent = '参数格式错误';
        }

        // 显示策略描述
        const description = portfolio.description || portfolio.return_hl || '无描述';
        strategyDescriptionDisplay.textContent = description;
    } else {
        // 没有策略信息，显示默认值
        strategyNameDisplay.textContent = '未设置';
        modelNameDisplay.textContent = '未设置';
        strategyDescriptionDisplay.textContent = '未设置';
        strategyParamsDisplay.textContent = '未设置';
        strategyStatusBadge.textContent = '未启用';
        strategyStatusBadge.className = 'badge bg-light text-dark';
    }
}

// 获取策略状态文本
function getStrategyStatusText(status) {
    switch (parseInt(status)) {
        case 1:
            return '已启用';
        case 2:
            return '暂停中';
        default:
            return '未启用';
    }
}

// 获取策略状态类名
function getStrategyStatusClass(status) {
    switch (parseInt(status)) {
        case 1:
            return 'badge bg-success';
        case 2:
            return 'badge bg-warning text-dark';
        default:
            return 'badge bg-light text-dark';
    }
}

// 获取策略显示名称
function getStrategyDisplayName(strategyName) {
    const strategyMap = {
        'trend_follow': '趋势跟踪策略',
        'mean_reversion': '均值回归策略',
        'momentum': '动量策略',
        'grid_trading': '网格交易策略',
        'custom': '自定义策略'
    };
    return strategyMap[strategyName] || strategyName;
}

// 获取投资组合业绩数据
async function fetchPortfolioPerformance(portfolioName, period) {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法获取投资组合业绩数据');
        return;
    }

    // 保存当前选择的周期，供前端使用
    currentPeriod = period;

    try {
        // 调用API，不再传递日期范围参数
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/portfolios/${portfolioName}/performance`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取投资组合业绩数据失败: ${response.status}`);
        }

        const data = await response.json();

        // 添加调试信息
        console.log('业绩数据返回:', data);

        if (data.code === 200 && data.data) {
            // 添加调试信息
            console.log('业绩数据详情:', data.data);

            // 从新的数据结构中提取历史业绩数据
            const performanceData = data.data.historical_performance;
            const performanceMetrics = data.data.performance_metrics;

            console.log('历史业绩数据:', performanceData);
            console.log('业绩指标数据:', performanceMetrics);

            // 更新统计指标显示
            updatePerformanceMetrics(performanceMetrics);

            // 渲染业绩图表
            renderPerformanceChart(performanceData, period);
        } else {
            throw new Error('返回数据格式错误');
        }

    } catch (error) {
        console.error('获取投资组合业绩数据失败:', error);
        document.getElementById('performanceChartContainer').innerHTML = `<div class="alert alert-danger">获取业绩数据失败: ${error.message}</div>`;
    }
}

// 全局变量保存当前选择的周期
let currentPeriod = '1m';

// 渲染业绩图表
function renderPerformanceChart(performanceData, period = '1m') {
    const chartContainer = document.getElementById('performanceChartContainer');

    // 清空图表容器
    chartContainer.innerHTML = '';

    // 添加调试信息
    console.log('原始业绩数据:', {
        dates: performanceData.dates,
        values: performanceData.values,
        returns: performanceData.returns,
        daily_pnls: performanceData.daily_pnls,
        hasValues: !!performanceData.values,
        hasReturns: !!performanceData.returns,
        hasDailyPnls: !!performanceData.daily_pnls,
        datesLength: performanceData.dates ? performanceData.dates.length : 0
    });

    // 如果没有日期数据，显示提示
    if (!performanceData.dates || performanceData.dates.length === 0) {
        console.log('没有日期数据，显示提示');
        chartContainer.innerHTML = '<div class="alert alert-info">暂无业绩数据</div>';
        return;
    }

    // 创建图表
    const chart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: 400,
        layout: {
            backgroundColor: '#ffffff',
            textColor: '#333',
        },
        grid: {
            vertLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
            horzLines: {
                color: 'rgba(197, 203, 206, 0.5)',
            },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
            autoScale: true,  // 自动缩放价格轴
            scaleMargins: {
                top: 0.1,  // 上边距
                bottom: 0.1,  // 下边距
            },
        },
        timeScale: {
            borderColor: 'rgba(197, 203, 206, 0.8)',
            rightOffset: 5,  // 右侧留出一小部分空间
            barSpacing: 10,  // 数据点之间的间距
            fixLeftEdge: true,  // 固定左边缘
            lockVisibleTimeRangeOnResize: true,  // 调整大小时锁定可见时间范围
            rightBarStaysOnScroll: true,  // 滚动时保持最右侧的数据点可见
        },
        // 禁用鼠标滚轮缩放，避免影响页面滚动
        handleScroll: {
            mouseWheel: false,
        },
        // 禁用鼠标滚轮缩放
        handleScale: {
            mouseWheel: false,
        },
    });

    // 准备数据
    let filteredDates = [...performanceData.dates]; // 复制日期数组，避免修改原始数据
    let filteredValues = [];
    let filteredReturns = [];
    let filteredDailyPnls = [];
    let chartTitle = '投资组合业绩';

    // 准备所有可能的数据类型
    chartTitle += ' (收益率和日盈亏)';

    // 准备净值数据
    if (performanceData.values && performanceData.values.length > 0) {
        // 使用values数据
        console.log('使用values数据');
        filteredValues = [...performanceData.values]; // 复制数组，避免修改原始数据
    } else {
        // 如果没有values数据，从1.0开始创建默认值
        filteredValues = [1.0];
        for (let i = 1; i < filteredDates.length; i++) {
            filteredValues[i] = filteredValues[i-1];
        }
    }

    // 准备收益率数据
    if (performanceData.returns && performanceData.returns.length > 0) {
        // 使用returns数据
        console.log('使用returns数据');
        filteredReturns = [...performanceData.returns]; // 复制数组，避免修改原始数据

        // 如果没有values数据，使用returns计算净值
        if (!performanceData.values || performanceData.values.length === 0) {
            console.log('使用returns数据计算净值');
            filteredValues = [1.0];
            for (let i = 1; i < performanceData.returns.length; i++) {
                const returnRate = performanceData.returns[i];
                if (returnRate !== null && returnRate !== undefined && !isNaN(returnRate)) {
                    filteredValues[i] = filteredValues[i-1] * (1 + returnRate);
                } else {
                    filteredValues[i] = filteredValues[i-1];
                }
            }
        }
    } else {
        // 如果没有returns数据，创建空数组
        filteredReturns = new Array(filteredDates.length).fill(0);
    }

    // 准备日盈亏数据
    if (performanceData.daily_pnls && performanceData.daily_pnls.length > 0) {
        // 使用daily_pnls数据
        console.log('使用daily_pnls数据');
        filteredDailyPnls = [...performanceData.daily_pnls]; // 复制数组，避免修改原始数据

        // 如果没有values和returns数据，使用daily_pnls计算净值
        if ((!performanceData.values || performanceData.values.length === 0) &&
            (!performanceData.returns || performanceData.returns.length === 0)) {
            console.log('使用daily_pnls数据计算净值');
            filteredValues = [1.0];
            for (let i = 1; i < performanceData.daily_pnls.length; i++) {
                const pnl = performanceData.daily_pnls[i];
                if (pnl !== null && pnl !== undefined && !isNaN(pnl)) {
                    filteredValues[i] = filteredValues[i-1] * (1 + pnl / 100);
                } else {
                    filteredValues[i] = filteredValues[i-1];
                }
            }
        }
    } else {
        // 如果没有daily_pnls数据，创建空数组
        filteredDailyPnls = new Array(filteredDates.length).fill(0);
    }

    // 在图表上方添加标题
    const titleElement = document.createElement('div');
    titleElement.style.textAlign = 'center';
    titleElement.style.fontSize = '16px';
    titleElement.style.fontWeight = 'bold';
    titleElement.style.marginBottom = '10px';
    titleElement.textContent = chartTitle;
    chartContainer.insertBefore(titleElement, chartContainer.firstChild);

    // 创建收益率为主曲线 - 使用红色为主色调，符合中国市场习惯
    const returnSeries = chart.addAreaSeries({
        topColor: 'rgba(255, 82, 82, 0.56)', // 红色系
        bottomColor: 'rgba(255, 82, 82, 0.04)',
        lineColor: 'rgba(255, 82, 82, 1)',
        lineWidth: 2,
        title: '收益率(%)',
    });

    // 检查数组长度
    const allArrays = [filteredValues, filteredReturns, filteredDailyPnls];
    const arrayLengths = [
        filteredDates.length,
        ...allArrays.map(arr => arr.length)
    ];

    // 记录数组长度信息，但不进行截断处理
    if (new Set(arrayLengths).size > 1) {
        console.warn('数组长度不一致，但不进行截断处理');
        console.log('各数组长度:', {
            dates: filteredDates.length,
            values: filteredValues.length,
            returns: filteredReturns.length,
            daily_pnls: filteredDailyPnls.length
        });
    }

    // 如果有日期数据，根据周期过滤
    if (filteredDates.length > 0) {
        const now = new Date();
        let cutoffDate = new Date();

        // 根据周期计算过滤日期
        switch (period) {
            case '1m':
                cutoffDate.setMonth(now.getMonth() - 1);
                break;
            case '3m':
                cutoffDate.setMonth(now.getMonth() - 3);
                break;
            case '6m':
                cutoffDate.setMonth(now.getMonth() - 6);
                break;
            case '1y':
                cutoffDate.setFullYear(now.getFullYear() - 1);
                break;
            case 'all':
                // 不过滤
                cutoffDate = new Date('2000-01-01');
                break;
        }

        // 转换为时间戳进行比较
        const cutoffTime = cutoffDate.getTime();

        // 过滤数据
        const filteredIndices = [];
        for (let i = 0; i < filteredDates.length; i++) {
            try {
                const dateStr = filteredDates[i];
                const dateObj = new Date(dateStr);

                // 检查日期是否有效
                if (!isNaN(dateObj.getTime())) {
                    // 只保留大于等于截止日期的数据
                    if (dateObj.getTime() >= cutoffTime) {
                        filteredIndices.push(i);
                    }
                } else {
                    console.warn(`无效的日期格式: ${dateStr}`);
                }
            } catch (e) {
                console.error(`处理日期时出错: ${filteredDates[i]}`, e);
            }
        }

        // 使用过滤后的索引提取所有数据
        const newDates = [];
        const newValues = [];
        const newReturns = [];
        const newDailyPnls = [];

        for (const idx of filteredIndices) {
            newDates.push(filteredDates[idx]);
            newValues.push(filteredValues[idx]);
            newReturns.push(filteredReturns[idx]);
            newDailyPnls.push(filteredDailyPnls[idx]);
        }

        filteredDates = newDates;
        filteredValues = newValues;
        filteredReturns = newReturns;
        filteredDailyPnls = newDailyPnls;
    }

    // 添加调试信息
    console.log('过滤后的数据:', {
        filteredDates,
        filteredValues,
        filteredReturns,
        filteredDailyPnls,
        datesLength: filteredDates.length,
        valuesLength: filteredValues.length,
        returnsLength: filteredReturns.length,
        dailyPnlsLength: filteredDailyPnls.length
    });

    // 准备图表数据 - 净值曲线
    const valueData = [];
    // 准备图表数据 - 收益率曲线
    const returnData = [];
    // 准备图表数据 - 日盈亏柱状图
    const pnlData = [];

    // 格式化日期函数
    const formatDate = (dateStr) => {
        try {
            const dateObj = new Date(dateStr);
            if (!isNaN(dateObj.getTime())) {
                return dateObj.toISOString().split('T')[0];
            }
            return dateStr;
        } catch (e) {
            console.warn(`格式化日期失败: ${dateStr}`, e);
            return dateStr;
        }
    };

    // 准备所有数据类型 - 处理数组长度不一致的情况
    for (let i = 0; i < filteredDates.length; i++) {
        const date = filteredDates[i];
        const formattedDate = formatDate(date);

        // 净值数据 - 检查索引是否在有效范围内
        if (i < filteredValues.length) {
            const value = filteredValues[i];
            if (value !== null && value !== undefined && !isNaN(value)) {
                valueData.push({
                    time: formattedDate,
                    value: value
                });
            }
        }

        // 收益率数据 - 检查索引是否在有效范围内
        if (i < filteredReturns.length) {
            const returnValue = filteredReturns[i];
            if (returnValue !== null && returnValue !== undefined && !isNaN(returnValue)) {
                returnData.push({
                    time: formattedDate,
                    value: returnValue * 100 // 转换为百分比显示
                });
            }
        }

        // 日盈亏数据 - 检查索引是否在有效范围内
        if (i < filteredDailyPnls.length) {
            const pnlValue = filteredDailyPnls[i];
            if (pnlValue !== null && pnlValue !== undefined && !isNaN(pnlValue)) {
                pnlData.push({
                    time: formattedDate,
                    value: pnlValue
                });
            }
        }
    }

    // 添加调试信息
    console.log('图表数据:', {
        valueData,
        returnData,
        pnlData
    });

    // 设置收益率数据作为主曲线
    if (returnData.length > 0) {
        returnSeries.setData(returnData);

        // 如果数据点少于5个，添加虚拟数据点使图表更美观
        if (returnData.length < 5) {
            console.log('数据点过少，调整时间轴显示');

            // 调整时间轴使数据居中显示
            const timeScale = chart.timeScale();

            // 设置可见范围为数据点数量的两倍，使数据居中
            timeScale.setVisibleLogicalRange({
                from: -returnData.length,  // 左侧留出空白
                to: returnData.length * 2   // 右侧留出空白
            });

            // 调整数据点间距，使得数据点更分散
            timeScale.applyOptions({
                barSpacing: Math.min(50, 300 / returnData.length)  // 根据数据点数量调整间距
            });
        } else {
            // 数据点足够多，调整时间轴使所有数据可见
            const timeScale = chart.timeScale();
            timeScale.fitContent();
        }
    } else {
        // 如果没有收益率数据，创建一个空的图表
        console.log('没有有效的收益率数据，创建空图表');
        // 创建一个空的数据点，使图表能够显示
        const today = new Date().toISOString().split('T')[0];
        returnSeries.setData([{ time: today, value: 0.0 }]);
    }

    // 添加日盈亏柱状图
    if (pnlData.length > 0) {
        // 创建日盈亏柱状图系列
        const pnlSeries = chart.addHistogramSeries({
            color: '#26a69a', // 绿色
            priceFormat: {
                type: 'price',
                precision: 2,
                minMove: 0.01,
            },
            title: '日盈亏',
            priceScaleId: 'left', // 使用左侧价格轴
            // 调整柱状图宽度
            base: 0,
            // 调整透明度，使柱状图不要太突出
            scaleMargins: {
                top: 0.7,  // 将柱状图缩小到图表下方
                bottom: 0.0,
            },
        });

        // 设置柱状图颜色函数 - 符合中国市场习惯（红涨绿跌）
        const coloredPnlData = pnlData.map(item => ({
            ...item,
            color: item.value >= 0 ? 'rgba(255, 82, 82, 0.8)' : 'rgba(38, 166, 154, 0.8)' // 正值红色，负值绿色
        }));

        pnlSeries.setData(coloredPnlData);

        // 如果数据点少，调整柱状图宽度
        if (pnlData.length < 5) {
            // 调整柱状图宽度，使其更宽
            chart.applyOptions({
                timeScale: {
                    barSpacing: Math.min(50, 300 / pnlData.length)  // 根据数据点数量调整间距
                }
            });
        }
    }

    // 如果所有数据都为空，显示提示
    if (returnData.length === 0 && pnlData.length === 0) {
        // 添加一个提示
        chartContainer.insertAdjacentHTML('beforeend', '<div class="alert alert-warning mt-3">暂无收益率和日盈亏数据，显示空图表</div>');
    }

    // 如果有基准数据，添加基准线
    if (performanceData.benchmark_returns && performanceData.benchmark_returns.length > 0) {
        // 添加调试信息
        console.log('基准数据:', {
            benchmark_returns: performanceData.benchmark_returns,
            length: performanceData.benchmark_returns.length,
            datesLength: performanceData.dates ? performanceData.dates.length : 0
        });

        // 计算基准净值
        const benchmarkValues = [1.0]; // 初始值为1.0
        for (let i = 1; i < performanceData.benchmark_returns.length; i++) {
            const benchmarkReturn = performanceData.benchmark_returns[i];
            if (benchmarkReturn !== null && benchmarkReturn !== undefined && !isNaN(benchmarkReturn)) {
                benchmarkValues[i] = benchmarkValues[i-1] * (1 + benchmarkReturn);
            } else {
                benchmarkValues[i] = benchmarkValues[i-1];
            }
        }

        // 创建基准线系列 - 使用蓝色与红色的主线形成对比
        const benchmarkSeries = chart.addLineSeries({
            color: 'rgba(33, 150, 243, 1)', // 蓝色系
            lineWidth: 2,
            lineStyle: LightweightCharts.LineStyle.Dashed,
            title: '基准指数',
        });

        // 准备基准数据 - 使用过滤后的日期
        const benchmarkData = [];

        // 准备基准数据 - 处理数组长度不一致的情况
        for (let i = 0; i < filteredDates.length; i++) {
            const date = filteredDates[i];
            // 找到原始数据中的索引
            const originalIndex = performanceData.dates.indexOf(date);

            // 检查原始索引是否有效且在基准值数组范围内
            if (originalIndex !== -1 && originalIndex < benchmarkValues.length) {
                const value = benchmarkValues[originalIndex];
                if (value !== null && value !== undefined && !isNaN(value)) {
                    // 确保日期格式正确
                    let formattedDate = date;
                    try {
                        const dateObj = new Date(date);
                        if (!isNaN(dateObj.getTime())) {
                            formattedDate = dateObj.toISOString().split('T')[0];
                        }
                    } catch (e) {
                        console.warn(`格式化基准日期失败: ${date}`, e);
                    }

                    benchmarkData.push({
                        time: formattedDate,
                        value: value
                    });
                }
            } else {
                // 如果原始索引无效，记录调试信息
                console.debug(`日期 ${date} 在原始数据中未找到或超出基准值范围`);
            }
        }

        // 添加调试信息
        console.log('准备的基准数据:', {
            benchmarkData: benchmarkData,
            length: benchmarkData.length
        });

        // 设置基准数据
        if (benchmarkData.length > 0) {
            benchmarkSeries.setData(benchmarkData);
        } else {
            console.log('基准数据为空，不显示基准线');
        }
    }

    // 添加图表动画效果
    setTimeout(() => {
        // 使用fitContent()使所有数据点都可见
        chart.timeScale().fitContent();

        // 如果数据点少于5个，调整时间轴显示
        if ((returnData.length > 0 && returnData.length < 5) ||
            (pnlData.length > 0 && pnlData.length < 5)) {
            // 调整时间轴使数据居中显示
            const timeScale = chart.timeScale();
            const dataLength = Math.max(returnData.length, pnlData.length);

            // 设置可见范围为数据点数量的两倍，使数据居中
            timeScale.setVisibleLogicalRange({
                from: -dataLength,  // 左侧留出空白
                to: dataLength * 2   // 右侧留出空白
            });
        }
    }, 100);

    // 自适应大小
    window.addEventListener('resize', () => {
        chart.applyOptions({
            width: chartContainer.clientWidth,
        });
        // 在调整大小后重新适应内容
        setTimeout(() => {
            chart.timeScale().fitContent();
        }, 100);
    });

    // 保存图表实例
    performanceChart = chart;
}

// 注意: fetchPortfolioMetrics 函数已经被移除，因为我们现在直接使用 Portfolio 的 PerformanceMetrics 数据

// 获取用户订阅信息
async function getUserSubscription() {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法获取订阅信息');
        return null;
    }

    try {
        // 调用API获取用户订阅信息
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/subscription/my-subscription`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取订阅信息失败: ${response.status}`);
        }

        const data = await response.json();
        console.log('用户订阅信息:', data);

        if (data.code === 200 && data.data) {
            return data.data;
        }

        return null;
    } catch (error) {
        console.error('获取订阅信息失败:', error);
        return null;
    }
}

// 升级订阅
async function upgradeSubscription(tier) {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法升级订阅');
        return false;
    }

    try {
        // 跳转到个人中心的订阅页面
        window.location.href = '/profile?tab=subscription&upgrade=' + tier;
        return true;
    } catch (error) {
        console.error('升级订阅失败:', error);
        return false;
    }
}

// 检查订阅并显示相应模态框
async function checkSubscriptionAndShowModal() {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法创建投资组合');
        return;
    }

    // 显示加载状态
    const loadingToast = new bootstrap.Toast(document.getElementById('loadingToast'));
    document.getElementById('loadingToastMessage').textContent = '正在检查订阅状态...';
    loadingToast.show();

    try {
        // 获取用户订阅信息
        const subscriptionData = await getUserSubscription();

        // 隐藏加载提示
        loadingToast.hide();

        // 检查用户订阅级别
        if (subscriptionData && subscriptionData.subscription && subscriptionData.plan) {
            const tier = subscriptionData.subscription.tier;

            // 如果是入门版用户，显示升级提示
            if (tier === 'basic') {
                // 显示升级订阅模态框
                const upgradeModal = new bootstrap.Modal(document.getElementById('upgradeSubscriptionModal'));
                upgradeModal.show();
                return;
            }
        }

        // 如果是进阶版或专业版用户，或者无法获取订阅信息，显示创建投资组合模态框
        const createPortfolioModal = new bootstrap.Modal(document.getElementById('createPortfolioModal'));
        createPortfolioModal.show();

    } catch (error) {
        console.error('检查订阅状态失败:', error);

        // 隐藏加载提示
        loadingToast.hide();

        // 如果出错，默认显示创建投资组合模态框
        const createPortfolioModal = new bootstrap.Modal(document.getElementById('createPortfolioModal'));
        createPortfolioModal.show();
    }
}

// 检查订阅并显示策略设置模态框
async function checkSubscriptionAndShowStrategyModal(portfolioName) {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法设置策略');
        return;
    }

    // 显示加载状态
    const loadingToast = new bootstrap.Toast(document.getElementById('loadingToast'));
    document.getElementById('loadingToastMessage').textContent = '正在检查订阅状态...';
    loadingToast.show();

    try {
        // 获取用户订阅信息
        const subscriptionData = await getUserSubscription();

        // 隐藏加载提示
        loadingToast.hide();

        // 检查用户订阅级别
        if (subscriptionData && subscriptionData.subscription && subscriptionData.plan) {
            const tier = subscriptionData.subscription.tier;

            // 如果是入门版用户，显示升级提示
            if (tier === 'basic') {
                // 显示升级订阅模态框
                const upgradeModal = new bootstrap.Modal(document.getElementById('upgradeSubscriptionModal'));
                upgradeModal.show();
                return;
            }
        }

        // 如果是进阶版或专业版用户，或者无法获取订阅信息，显示策略设置模态框
        // 加载可用模型列表
        fetchAvailableModels();
        // 加载当前策略设置
        fetchStrategySettings(portfolioName);
        // 显示模态框
        const strategyModal = new bootstrap.Modal(document.getElementById('strategyModal'));
        strategyModal.show();

    } catch (error) {
        console.error('检查订阅状态失败:', error);

        // 隐藏加载提示
        loadingToast.hide();

        // 如果出错，默认显示策略设置模态框
        // 加载可用模型列表
        fetchAvailableModels();
        // 加载当前策略设置
        fetchStrategySettings(portfolioName);
        // 显示模态框
        const strategyModal = new bootstrap.Modal(document.getElementById('strategyModal'));
        strategyModal.show();
    }
}

// 创建投资组合
async function createPortfolio(portfolioName, description, initialCash) {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法创建投资组合');
        return;
    }

    // 显示加载状态
    const submitBtn = document.querySelector('#createPortfolioForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 创建中...';
    submitBtn.disabled = true;

    try {
        // 关闭创建投资组合模态框
        const createPortfolioModal = bootstrap.Modal.getInstance(document.getElementById('createPortfolioModal'));
        createPortfolioModal.hide();

        // 调用API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/portfolios`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                portfolio_name: portfolioName,
                description: description,
                initial_cash: parseFloat(initialCash),
                account_type: 'stock'  // 默认为股票账户
            })
        });

        if (!response.ok) {
            // 如果是403错误，说明权限不足，显示升级提示
            if (response.status === 403) {
                const upgradeModal = new bootstrap.Modal(document.getElementById('upgradeSubscriptionModal'));
                upgradeModal.show();
                return;
            }

            throw new Error(`创建投资组合失败: ${response.status}`);
        }

        await response.json(); // 解析响应但不使用返回值

        // 重新获取投资组合列表
        fetchPortfolios();

        // 直接查看新创建的投资组合
        setTimeout(() => {
            viewPortfolio(portfolioName);
        }, 500);

        // 显示成功提示
        alert('投资组合创建成功');

    } catch (error) {
        console.error('创建投资组合失败:', error);
        alert(`创建投资组合失败: ${error.message}`);

        // 重新打开创建投资组合模态框
        const createPortfolioModal = new bootstrap.Modal(document.getElementById('createPortfolioModal'));
        createPortfolioModal.show();
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 格式化日期
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化货币
function formatCurrency(value) {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY'
    }).format(value);
}

// 添加标的到自选股
async function addToWatchlist(symbol, name, accountType) {
    if (!isLoggedIn || !accessToken) {
        alert('请先登录再添加自选股');
        return false;
    }

    try {
        // 使用传入的账户类型，如果没有则根据标的代码判断
        let type = accountType;
        if (!type) {
            // 如果没有传入账户类型，则根据标的代码判断
            type = symbol.includes('.SF') ? 'future' : 'stock';
        }

        console.log(`添加自选股类型: ${type}, 标的: ${symbol}`);

        // 验证类型是否有效
        if (type !== 'stock' && type !== 'future') {
            console.warn(`无效的自选股类型: ${type}, 使用默认类型: stock`);
            type = 'stock';
        }

        // 使用API添加自选股
        console.log(`正在添加自选股: ${symbol}, 类型: ${type}, token: ${accessToken.substring(0, 10)}...`);
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/favorites`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                symbol: symbol,
                name: name,
                type: type
            })
        });

        // 如果是409冲突或400错误，说明已经添加过
        if (response.status === 409 || response.status === 400) {
            const errorData = await response.json();
            alert(errorData.detail || `${name}(${symbol}) 已经在自选股列表中`);
            return true; // 返回true，因为已经存在也算成功
        }

        if (!response.ok) {
            throw new Error(`朌务器响应错误: ${response.status}`);
        }

        // 模拟延迟，使用户体验更真实
        await new Promise(resolve => setTimeout(resolve, 500));

        // 添加成功
        console.log(`已成功将 ${name}(${symbol}) 添加到自选股`);

        // 触发自选股更新事件
        const favoriteUpdateEvent = new CustomEvent('favoriteUpdate', {
            detail: { symbol, name, type }
        });
        document.dispatchEvent(favoriteUpdateEvent);

        // 如果首页已经加载，尝试直接调用其加载函数
        if (window.loadFavoritesFromStorage) {
            try {
                window.loadFavoritesFromStorage();
            } catch (e) {
                console.warn('尝试直接调用首页加载函数失败:', e);
            }
        }

        return true;
    } catch (error) {
        console.error('添加自选股失败:', error);
        alert(`添加自选股失败: ${error.message}`);
        return false;
    }
}

// 获取可用模型列表
async function fetchAvailableModels() {
    try {
        // 调用API获取模型列表
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/model/models`);

        if (!response.ok) {
            throw new Error(`获取模型列表失败: ${response.status}`);
        }

        const data = await response.json();
        const modelSelect = document.getElementById('modelName');

        // 清空当前选项，保留第一个默认选项
        modelSelect.innerHTML = '<option value="">请选择模型</option>';

        // 添加模型选项
        if (data.available_models && data.available_models.length > 0) {
            data.available_models.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                modelSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('获取模型列表失败:', error);
        alert(`获取模型列表失败: ${error.message}`);
    }
}

// 获取策略设置
async function fetchStrategySettings(portfolioName) {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法获取策略设置');
        return;
    }

    try {
        // 调用API获取投资组合详情，其中包含策略信息
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/portfolios/${portfolioName}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取策略设置失败: ${response.status}`);
        }

        const data = await response.json();

        if (data.code === 200 && data.data) {
            const portfolio = data.data;

            // 设置策略状态
            const strategyStatus = document.getElementById('strategyStatus');
            strategyStatus.value = portfolio.run_state || '0';

            // 设置策略名称
            const strategyName = document.getElementById('strategyName');
            if (portfolio.strategy_name && portfolio.strategy_name !== 'null' && portfolio.strategy_name !== 'undefined') {
                strategyName.value = portfolio.strategy_name;
            } else {
                strategyName.value = '';
            }

            // 设置模型名称
            const modelName = document.getElementById('modelName');
            if (portfolio.model_name && portfolio.model_name !== 'null' && portfolio.model_name !== 'undefined') {
                // 检查是否有该选项，如果没有则添加
                if (!Array.from(modelName.options).some(option => option.value === portfolio.model_name)) {
                    const option = document.createElement('option');
                    option.value = portfolio.model_name;
                    option.textContent = portfolio.model_name;
                    modelName.appendChild(option);
                }
                modelName.value = portfolio.model_name;
            } else {
                modelName.value = '';
            }

            // 设置策略参数
            const strategyParams = document.getElementById('strategyParams');
            if (portfolio.return_trend && portfolio.return_trend !== 'null' && portfolio.return_trend !== 'undefined') {
                try {
                    const params = JSON.parse(portfolio.return_trend);
                    strategyParams.value = JSON.stringify(params, null, 2);
                } catch (e) {
                    console.error('解析策略参数失败:', e);
                    strategyParams.value = '{}';
                }
            } else {
                strategyParams.value = '{}';
            }

            // 设置策略描述
            const strategyDescription = document.getElementById('strategyDescription');
            strategyDescription.value = portfolio.return_hl || '';
        }
    } catch (error) {
        console.error('获取策略设置失败:', error);
        alert(`获取策略设置失败: ${error.message}`);
    }
}

// 保存策略设置
async function saveStrategySettings(portfolioName, strategyStatus, strategyName, modelName, strategyParams, strategyDescription) {
    if (!isLoggedIn || !accessToken) {
        console.error('未登录，无法保存策略设置');
        return;
    }

    // 显示加载状态
    const submitBtn = document.querySelector('#strategyForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 检查中...';
    submitBtn.disabled = true;

    try {
        // 获取用户订阅信息
        const subscriptionData = await getUserSubscription();

        // 检查用户订阅级别
        if (subscriptionData && subscriptionData.subscription && subscriptionData.plan) {
            const tier = subscriptionData.subscription.tier;

            // 如果是入门版用户，显示升级提示
            if (tier === 'basic') {
                // 关闭策略模态框
                const strategyModal = bootstrap.Modal.getInstance(document.getElementById('strategyModal'));
                strategyModal.hide();

                // 显示升级订阅模态框
                const upgradeModal = new bootstrap.Modal(document.getElementById('upgradeSubscriptionModal'));
                upgradeModal.show();

                // 恢复按钮状态
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;

                return;
            }
        }

        // 如果是进阶版或专业版用户，或者无法获取订阅信息，继续保存策略设置
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';

        // 调用API保存策略设置
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/portfolios/${portfolioName}/strategy`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                run_state: strategyStatus,
                strategy_name: strategyName,
                model_name: modelName,
                strategy_params: strategyParams,  // 已经解析的JSON对象
                strategy_description: strategyDescription
            })
        });

        if (!response.ok) {
            // 如果是403错误，说明权限不足，显示升级提示
            if (response.status === 403) {
                // 关闭策略模态框
                const strategyModal = bootstrap.Modal.getInstance(document.getElementById('strategyModal'));
                strategyModal.hide();

                // 显示升级订阅模态框
                const upgradeModal = new bootstrap.Modal(document.getElementById('upgradeSubscriptionModal'));
                upgradeModal.show();

                return;
            }

            throw new Error(`保存策略设置失败: ${response.status}`);
        }

        await response.json(); // 解析响应但不使用返回值

        // 关闭模态框
        const strategyModal = bootstrap.Modal.getInstance(document.getElementById('strategyModal'));
        strategyModal.hide();

        // 重新加载投资组合详情
        viewPortfolio(portfolioName);

        // 显示成功提示
        alert('策略设置保存成功');

    } catch (error) {
        console.error('保存策略设置失败:', error);
        alert(`保存策略设置失败: ${error.message}`);
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

