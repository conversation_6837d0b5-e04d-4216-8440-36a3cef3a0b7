"""
BarDataset2 RAG Retriever

This module provides a retrieval-augmented generation (RAG) system for BarDataset2 data.
It supports various embedding algorithms, indexing, retrieval, and persistence operations.
"""

import os
import numpy as np
import torch
from torch.utils.data import DataLoader
import faiss
from typing import Dict, Optional, Callable
from datetime import datetime

from pyqlab.data.dataset.dataset_bar2 import BarDataset2
from pyqlab.rag.embedded import TimeSeriesRNNEmbedder, TimeSeriesTransformerEmbedder, TimeSeriesCNNEmbedder


class BarDatasetRetriever:
    """
    A retriever for BarDataset2 data that supports vector similarity search.

    This class provides functionality to:
    - Build FAISS index from BarDataset2 data
    - Retrieve similar data points based on vector similarity
    - Save and load the index and related data
    - Update the index with new data

    Attributes:
        embedding_dim (int): Dimension of the embedding vectors
        n_neighbors (int): Number of neighbors to retrieve
        metric (str): Metric to use for similarity search ('l2' or 'ip' for inner product)
        index (faiss.Index): FAISS index for vector similarity search
        embeddings (np.ndarray): Stored embeddings
        metadata (List[Dict]): Metadata for each embedding
        embedder (Callable): Function to embed data
    """

    def __init__(self, embedding_dim: int, n_neighbors: int = 5, metric: str = 'l2'):
        """
        Initialize the BarDatasetRetriever.

        Args:
            embedding_dim (int): Dimension of the embedding vectors
            n_neighbors (int): Number of neighbors to retrieve
            metric (str): Metric to use for similarity search ('l2' or 'ip' for inner product)
        """
        self.embedding_dim = embedding_dim
        self.n_neighbors = n_neighbors
        self.metric = metric
        self.index = None
        self.embeddings = None
        self.metadata = []
        self.embedder = None

    def set_embedder(self, embedder: Callable):
        """
        Set the embedder function to use for embedding data.

        Args:
            embedder (Callable): Function that takes data and returns embeddings
        """
        self.embedder = embedder

    def _create_index(self):
        """Create a new FAISS index based on the specified metric."""
        if self.metric == 'l2':
            self.index = faiss.IndexFlatL2(self.embedding_dim)
        elif self.metric == 'ip':
            self.index = faiss.IndexFlatIP(self.embedding_dim)
        else:
            raise ValueError(f"Unsupported metric: {self.metric}. Use 'l2' or 'ip'.")

    def build_index(self, dataset: BarDataset2, embedder: Optional[Callable] = None):
        """
        Build the FAISS index from a BarDataset2 dataset.

        Args:
            dataset (BarDataset2): The dataset to build the index from
            embedder (Callable, optional): Function to embed data. If None, uses the previously set embedder.

        Returns:
            self: The retriever instance
        """
        if embedder:
            self.embedder = embedder

        if self.embedder is None:
            raise ValueError("No embedder set. Please set an embedder using set_embedder() or provide one to build_index().")

        # Create a new index
        self._create_index()

        # Process all data points in the dataset
        all_embeddings = []
        all_metadata = []

        data_loader = DataLoader(dataset, batch_size=32, shuffle=True)

        for i, data in enumerate(data_loader):
            # Get data from dataset
            code, bar, x_mark, _, _, ctx  = data

            embedding = self.embedder(code[:,0], bar, x_mark)

            # Ensure embedding is a numpy array
            if isinstance(embedding, torch.Tensor):
                embedding = embedding.detach().cpu().numpy()

            # 将批量处理的embedding展开后逐个加入list
            for emb in embedding:
                all_embeddings.append(emb)

            for item in ctx:
                # Create metadata
                metadata = {
                    'timestamp': datetime.now().isoformat(),
                    'code': code.numpy()[0,0],
                    'ctx': item.numpy()
                }
                all_metadata.append(metadata)

        # Combine all embeddings
        self.embeddings = np.vstack(all_embeddings).astype('float32')
        self.metadata = all_metadata

        # Add to index
        self.index.add(self.embeddings)

        return self

    def add_to_index(self, code, bar, x_mark, ctx):
        """
        Add new data to the existing index.

        Args:
            code: The code data
            bar: The bar data
            x_mark: The x_mark data
            metadata (Dict, optional): Metadata for the data
            embedder (Callable, optional): Function to embed data. If None, uses the previously set embedder.

        Returns:
            int: The index of the added data
        """
        if self.index is None:
            raise ValueError("Index not built. Call build_index() first.")

        if self.embedder is None:
            raise ValueError("No embedder set. Please set an embedder using set_embedder() or provide one to add_to_index().")

        # Create embedding
        embedding = self.embedder(code, bar, x_mark)

        # Add to index
        self.index.add(embedding)

        all_embeddings = []
        all_metadata = []
        # 将批量处理的embedding展开后逐个加入list
        for emb in embedding:
            all_embeddings.append(emb)

        for item in ctx:
            # Create metadata
            metadata = {
                'timestamp': datetime.now().isoformat(),
                'code': code.numpy()[0,0],
                'ctx': item.numpy()
            }
            all_metadata.append(metadata)

        all_embeddings = np.vstack(all_embeddings).astype('float32')
        # Add to index
        self.index.add(all_embeddings)

        self.embeddings = np.vstack([self.embeddings, all_embeddings])
        self.metadata = np.concatenate([self.metadata, all_metadata])

        return len(all_metadata) - 1

    def retrieve(self, code, bar, x_mark, k: Optional[int] = None, embedder: Optional[Callable] = None):
        """
        Retrieve the k most similar data points to the query.

        Args:
            code: The code data
            bar: The bar data
            x_mark: The x_mark data
            k (int, optional): Number of neighbors to retrieve. If None, uses self.n_neighbors.
            embedder (Callable, optional): Function to embed data. If None, uses the previously set embedder.

        Returns:
            Tuple[np.ndarray, np.ndarray, List[Dict]]: Distances, indices, and metadata of the retrieved data
        """
        if self.index is None:
            raise ValueError("Index not built. Call build_index() first.")

        if embedder:
            self.embedder = embedder

        if self.embedder is None:
            raise ValueError("No embedder set. Please set an embedder using set_embedder() or provide one to retrieve().")

        # Use default k if not provided
        if k is None:
            k = self.n_neighbors

        # Create embedding
        query_embedding = self.embedder(code, bar, x_mark)

        # Ensure embedding is a numpy array
        if isinstance(query_embedding, torch.Tensor):
            query_embedding = query_embedding.detach().cpu().numpy()

        # Ensure embedding is 2D
        if len(query_embedding.shape) == 1:
            query_embedding = query_embedding.reshape(1, -1)

        # Search index
        distances, indices = self.index.search(query_embedding, k)

        # Get metadata for retrieved indices
        retrieved_metadata = [self.metadata[i] for i in indices[0]]

        return distances, indices, retrieved_metadata

    def save(self, directory: str, prefix: str = "bar_dataset_retriever"):
        """
        Save the index and related data to disk.

        Args:
            directory (str): Directory to save the files to
            prefix (str, optional): Prefix for the filenames

        Returns:
            str: Path to the saved index
        """
        if self.index is None:
            raise ValueError("Index not built. Call build_index() first.")

        # Create directory if it doesn't exist
        os.makedirs(directory, exist_ok=True)

        # Save index
        index_path = os.path.join(directory, f"{prefix}_index.faiss")
        faiss.write_index(self.index, index_path)

        # Save embeddings
        embeddings_path = os.path.join(directory, f"{prefix}_embeddings.npy")
        np.save(embeddings_path, self.embeddings)

        # Save metadata
        metadata_path = os.path.join(directory, f"{prefix}_metadata.npy")
        np.save(metadata_path, self.metadata)

        # Save config
        config = {
            'embedding_dim': self.embedding_dim,
            'n_neighbors': self.n_neighbors,
            'metric': self.metric,
            'timestamp': datetime.now().isoformat()
        }
        config_path = os.path.join(directory, f"{prefix}_config.npy")
        np.save(config_path, config)

        return index_path

    def load(self, directory: str, prefix: str = "bar_dataset_retriever"):
        """
        Load the index and related data from disk.

        Args:
            directory (str): Directory to load the files from
            prefix (str, optional): Prefix for the filenames

        Returns:
            self: The retriever instance
        """
        # Load index
        index_path = os.path.join(directory, f"{prefix}_index.faiss")
        self.index = faiss.read_index(index_path)

        # Load embeddings
        embeddings_path = os.path.join(directory, f"{prefix}_embeddings.npy")
        self.embeddings = np.load(embeddings_path, allow_pickle=True)

        # Load metadata
        metadata_path = os.path.join(directory, f"{prefix}_metadata.npy")
        self.metadata = np.load(metadata_path, allow_pickle=True).tolist()

        # Load config
        config_path = os.path.join(directory, f"{prefix}_config.npy")
        config = np.load(config_path, allow_pickle=True).item()

        self.embedding_dim = config['embedding_dim']
        self.n_neighbors = config['n_neighbors']
        self.metric = config['metric']

        return self


# Helper function to create an embedder from a model
def create_embedder_from_model(model, device=None):
    """
    Create an embedder function from a PyTorch model.

    Args:
        model: PyTorch model that takes data and returns embeddings
        device: Device to run the model on

    Returns:
        Callable: Function that takes data and returns embeddings
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    model = model.to(device)
    model.eval()

    def embedder(code, bar, x_mark):
        with torch.no_grad():
            # Convert to tensor if not already
            # if not isinstance(code, torch.Tensor):
            #     code = torch.tensor(code, dtype=torch.float32)
            # if not isinstance(bar, torch.Tensor):
            #     bar = torch.tensor(bar, dtype=torch.float32)
            # if not isinstance(x_mark, torch.Tensor):
            #     x_mark = torch.tensor(x_mark, dtype=torch.float32)

            # Add batch dimension if needed
            # if len(code.shape) == 2:
            #     code = code.unsqueeze(0)
            # if len(bar.shape) == 2:
            #     bar = bar.unsqueeze(0)
            # if len(x_mark.shape) == 2:
            #     x_mark = x_mark.unsqueeze(0)

            # Move to device
            code = code.to(device)
            bar = bar.to(device)
            x_mark = x_mark.to(device)

            # 使用实际数据，但需要按照 TimeSeriesRNNEmbedder 的要求进行转换
            # 确保所有输入都有正确的维度
            # 1. 处理 symbol_indices（应为 1D 张量）
            # if len(code.shape) > 1:
            #     symbol_indices = code[:,0]
            # else:
            #     symbol_indices = code

            # 2. 处理 token_sequences（应为 2D 张量 [batch_size, seq_len]）
            # if len(bar.shape) == 3:
            #     # 如果是 [batch_size, seq_len, feature_dim]，取第一个特征
            #     token_sequences = bar[:,:,0].long()
            # else:
            #     token_sequences = bar.long()

            # 3. 处理 time_feature_vectors（应为 3D 张量 [batch_size, seq_len, time_feature_dim]）
            # if len(x_mark.shape) == 2:
            #     # 如果是 [batch_size, seq_len]，扩展为 [batch_size, seq_len, 1]
            #     time_feature_vectors = x_mark.unsqueeze(-1)
            # else:
            #     time_feature_vectors = x_mark

            # 确保 token_sequences 是整数类型
            # token_sequences = token_sequences.long()

            # 确保 time_feature_vectors 的维度与 token_sequences 匹配
            # if len(token_sequences.shape) == 2 and len(time_feature_vectors.shape) == 3:
            #     # 检查 seq_len 是否匹配
            #     if token_sequences.shape[1] != time_feature_vectors.shape[1]:
            #         # 如果不匹配，调整为相同的长度
            #         min_len = min(token_sequences.shape[1], time_feature_vectors.shape[1])
            #         token_sequences = token_sequences[:, :min_len]
            #         time_feature_vectors = time_feature_vectors[:, :min_len, :]

            embedding = model(code, bar, x_mark)

            # Return as numpy array
            return embedding.cpu().numpy()

    return embedder


# Factory function to create a retriever with a specific embedder
def create_bar_dataset_retriever(embedding_dim, embedder_type='rnn', n_neighbors=5, metric='l2', **kwargs):
    """
    Create a BarDatasetRetriever with a specific embedder.

    Args:
        embedding_dim (int): Dimension of the embedding vectors
        embedder_type (str): Type of embedder to use ('rnn', 'transformer', 'cnn')
        n_neighbors (int): Number of neighbors to retrieve
        metric (str): Metric to use for similarity search ('l2' or 'ip')
        **kwargs: Additional arguments to pass to the embedder

    Returns:
        BarDatasetRetriever: A retriever with the specified embedder
    """
    retriever = BarDatasetRetriever(embedding_dim, n_neighbors, metric)

    # Create embedder based on type
    if embedder_type == 'rnn':
        model = TimeSeriesRNNEmbedder(
            num_symbols=kwargs.get('num_symbols', 100),
            vocab_size=kwargs.get('vocab_size', 40002),
            seq_len=kwargs.get('seq_len', 30),
            symbol_emb_dim=kwargs.get('symbol_emb_dim', 32),
            token_emb_dim=kwargs.get('token_emb_dim', 128),
            time_feature_dim=kwargs.get('time_feature_dim', 8),
            hidden_dim=kwargs.get('hidden_dim', embedding_dim),
            num_layers=kwargs.get('num_layers', 2),
            final_emb_dim=embedding_dim,
            dropout=kwargs.get('dropout', 0.1)
        )
    elif embedder_type == 'transformer':
        model = TimeSeriesTransformerEmbedder(
            num_symbols=kwargs.get('num_symbols', 100),
            vocab_size=kwargs.get('vocab_size', 40002),
            seq_len=kwargs.get('seq_len', 30),
            symbol_emb_dim=kwargs.get('symbol_emb_dim', 32),
            token_emb_dim=kwargs.get('token_emb_dim', 128),
            time_feature_dim=kwargs.get('time_feature_dim', 8),
            d_model=kwargs.get('d_model', embedding_dim),
            nhead=kwargs.get('nhead', 8),
            num_encoder_layers=kwargs.get('num_encoder_layers', 3),
            dim_feedforward=kwargs.get('dim_feedforward', embedding_dim * 4),
            final_emb_dim=embedding_dim,
            dropout=kwargs.get('dropout', 0.1)
        )
    elif embedder_type == 'cnn':
        model = TimeSeriesCNNEmbedder(
            num_symbols=kwargs.get('num_symbols', 100),
            vocab_size=kwargs.get('vocab_size', 40002),
            seq_len=kwargs.get('seq_len', 30),
            symbol_emb_dim=kwargs.get('symbol_emb_dim', 32),
            token_emb_dim=kwargs.get('token_emb_dim', 128),
            time_feature_dim=kwargs.get('time_feature_dim', 8),
            num_filters=kwargs.get('num_filters', 128),
            kernel_sizes=kwargs.get('kernel_sizes', [3, 5, 7]),
            final_emb_dim=embedding_dim,
            dropout=kwargs.get('dropout', 0.1)
        )
    else:
        raise ValueError(f"Unsupported embedder type: {embedder_type}. Use 'rnn', 'transformer', or 'cnn'.")

    # Create embedder function
    embedder_fn = create_embedder_from_model(model, kwargs.get('device', None))

    # Set embedder
    retriever.set_embedder(embedder_fn)

    return retriever
