"""
Macro Analyst agent for the AI Hedge Fund module.

This agent analyzes macroeconomic trends and their impact on markets.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.macro_data import get_macro_indicators, analyze_macro_trends
from ..utils.llm import call_llm


class MacroSignal(BaseModel):
    """Signal generated by the Macro Analyst agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")


def macro_analyst_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes macroeconomic trends and their impact on markets.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with macroeconomic analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Define the macro indicators to analyze
    indicators = [
        "GDP",
        "CPI",
        "UNEMPLOYMENT",
        "INTEREST_RATE",
        "RETAIL_SALES",
    ]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    macro_analysis = {}
    
    # Update progress status if available
    if "progress" in state["metadata"]:
        for ticker in tickers:
            state["metadata"]["progress"].update_status("macro_analyst_agent", ticker, "Fetching macro data")
    
    # Fetch macro data
    macro_data = get_macro_indicators(indicators, start_date, end_date)
    
    # Analyze macro trends
    trend_analysis = analyze_macro_trends(macro_data)
    
    # Store analysis data
    analysis_data["macro_trends"] = trend_analysis
    
    for ticker in tickers:
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("macro_analyst_agent", ticker, "Generating macro analysis")
        
        # Generate detailed analysis using LLM
        macro_output = generate_macro_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        macro_analysis[ticker] = {
            "signal": macro_output.signal,
            "confidence": macro_output.confidence,
            "reasoning": macro_output.reasoning,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("macro_analyst_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(macro_analysis, "Macro Analyst Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["macro_analyst_agent"] = macro_analysis
    
    return state


def generate_macro_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> MacroSignal:
    """
    Generate detailed macroeconomic analysis using LLM.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        MacroSignal object with the macroeconomic analysis
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Macro Analyst AI agent. Analyze macroeconomic trends and their impact on specific stocks.

    Key principles for macroeconomic analysis:
    - Economic Growth: Consider GDP trends and their impact on different sectors
    - Inflation: Analyze CPI trends and their implications for pricing power and margins
    - Employment: Evaluate unemployment trends and their impact on consumer spending
    - Interest Rates: Consider the impact of interest rate trends on borrowing costs and valuations
    - Consumer Spending: Analyze retail sales trends and their implications for consumer-facing businesses
    - Sector Sensitivity: Different sectors have different sensitivities to macro factors
    - Leading vs. Lagging: Distinguish between leading and lagging economic indicators

    When providing your reasoning, be thorough and specific by:
    1. Summarizing the overall macroeconomic environment (expansionary, contractionary, or transitional)
    2. Highlighting the most significant macro trends that could impact the specific stock
    3. Explaining how the company's sector typically responds to the current macro environment
    4. Providing specific company factors that may enhance or mitigate macro impacts
    5. Using a professional, analytical tone in your explanation

    For example, if bullish: "The economy is showing strong growth with GDP increasing at 3.2% annually. This expansionary environment typically benefits cyclical stocks like [ticker]. With unemployment falling to 3.8%, consumer spending is likely to remain robust, supporting the company's revenue growth..."
    For example, if bearish: "Rising inflation at 4.5% and interest rates trending upward create a challenging environment for growth stocks like [ticker]. The company's high debt levels make it particularly vulnerable to rising borrowing costs, while its limited pricing power may compress margins..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following macroeconomic data, create a trading signal for {ticker}:

    Analysis Data:
    {json.dumps(analysis_data, indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string"
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_macro_signal():
        # Analyze the macro trends to determine a default signal
        macro_trends = analysis_data.get("macro_trends", {})
        
        # Count bullish and bearish indicators
        bullish_count = 0
        bearish_count = 0
        
        for indicator, analysis in macro_trends.items():
            if analysis.get("status") != "success":
                continue
            
            trend_direction = analysis.get("trend_direction")
            
            if indicator == "GDP" and trend_direction == "increasing":
                bullish_count += 1
            elif indicator == "GDP" and trend_direction == "decreasing":
                bearish_count += 1
            elif indicator == "CPI" and trend_direction == "increasing":
                bearish_count += 1  # Rising inflation is generally bearish
            elif indicator == "CPI" and trend_direction == "decreasing":
                bullish_count += 1  # Falling inflation is generally bullish
            elif indicator == "UNEMPLOYMENT" and trend_direction == "increasing":
                bearish_count += 1
            elif indicator == "UNEMPLOYMENT" and trend_direction == "decreasing":
                bullish_count += 1
            elif indicator == "INTEREST_RATE" and trend_direction == "increasing":
                bearish_count += 1  # Rising rates are generally bearish
            elif indicator == "INTEREST_RATE" and trend_direction == "decreasing":
                bullish_count += 1  # Falling rates are generally bullish
            elif indicator == "RETAIL_SALES" and trend_direction == "increasing":
                bullish_count += 1
            elif indicator == "RETAIL_SALES" and trend_direction == "decreasing":
                bearish_count += 1
        
        # Determine signal based on counts
        if bullish_count > bearish_count:
            signal = "bullish"
            confidence = min(50.0 + 10.0 * (bullish_count - bearish_count), 90.0)
        elif bearish_count > bullish_count:
            signal = "bearish"
            confidence = min(50.0 + 10.0 * (bearish_count - bullish_count), 90.0)
        else:
            signal = "neutral"
            confidence = 50.0
        
        # Generate a simple reasoning
        reasoning = f"Based on macroeconomic indicators: {bullish_count} bullish trends and {bearish_count} bearish trends."
        
        return MacroSignal(
            signal=signal,
            confidence=confidence,
            reasoning=reasoning
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=MacroSignal,
        agent_name="macro_analyst_agent",
        default_factory=create_default_macro_signal,
    )
