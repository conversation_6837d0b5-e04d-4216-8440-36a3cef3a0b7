<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时序检索 - 智能体投顾助手</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="../static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
</head>
<body>
    {% include 'components/navbar.html' %}

    <div class="container mt-4">
        <!-- 欢迎区域 -->
        <div class="row mb-4" id="welcomeSection">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title">时间序列检索与分析</h2>
                        <p class="card-text">本页面提供K线数据相似性检索功能，您可以输入一段K线数据，系统将从历史数据库中检索出最相似的K线序列，并以图表形式展示比较结果。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 输入区域 -->
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4>输入参数</h4>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="inputMethod" class="form-label">输入方式</label>
                            <select class="form-select" id="inputMethod">
                                <option value="ticker">股票代码</option>
                                <option value="manual">手动输入</option>
                                <option value="upload">上传CSV</option>
                            </select>
                        </div>

                        <!-- 股票代码输入 -->
                        <div id="tickerInputSection">
                            <div class="mb-3">
                                <label for="tickerInput" class="form-label">股票代码</label>
                                <input type="text" class="form-control" id="tickerInput" placeholder="例如: 600000" value="IF9999.SF">
                            </div>
                            <div class="mb-3">
                                <label for="klineInterval" class="form-label">K线周期</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <select class="form-select" id="klineInterval">
                                            <option value="1m">1分钟</option>
                                            <option value="5m">5分钟</option>
                                            <option value="15m">15分钟</option>
                                            <option value="30m">30分钟</option>
                                            <option value="60m">1小时</option>
                                            <option value="1d" selected>日线</option>
                                            <option value="1wk">周线</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" id="klinePeriod">
                                            <option value="1d">1天</option>
                                            <option value="1w">1周</option>
                                            <option value="1mo" selected>1个月</option>
                                            <option value="3mo">3个月</option>
                                            <option value="6mo">6个月</option>
                                            <option value="1y">1年</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="dateRange" class="form-label">日期范围</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="startDate">
                                    <span class="input-group-text">至</span>
                                    <input type="date" class="form-control" id="endDate">
                                </div>
                            </div>
                        </div>

                        <!-- 手动输入 -->
                        <div id="manualInputSection" style="display: none;">
                            <div class="mb-3">
                                <label for="manualInput" class="form-label">K线数据 (JSON格式)</label>
                                <textarea class="form-control" id="manualInput" rows="10" placeholder='[{"date": "2023-01-01", "open": 100, "high": 105, "low": 98, "close": 103, "volume": 10000}, ...]'></textarea>
                            </div>
                        </div>

                        <!-- 上传CSV -->
                        <div id="uploadSection" style="display: none;">
                            <div class="mb-3">
                                <label for="csvFile" class="form-label">上传CSV文件</label>
                                <input type="file" class="form-control" id="csvFile" accept=".csv">
                                <div class="form-text">CSV格式: date,open,high,low,close,volume</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="sequenceLength" class="form-label">序列长度</label>
                            <input type="number" class="form-control" id="sequenceLength" value="30" min="5" max="100">
                            <div class="form-text">检索的K线序列长度 (5-100)</div>
                        </div>

                        <div class="mb-3">
                            <label for="topK" class="form-label">返回结果数量</label>
                            <input type="number" class="form-control" id="topK" value="3" min="1" max="10">
                            <div class="form-text">检索返回的相似序列数量 (1-10)</div>
                        </div>

                        <div class="mb-3">
                            <label for="similarityMetric" class="form-label">相似度度量</label>
                            <select class="form-select" id="similarityMetric">
                                <option value="cosine">余弦相似度</option>
                                <option value="euclidean">欧氏距离</option>
                                <option value="dtw">动态时间规整 (DTW)</option>
                            </select>
                        </div>

                        <div class="d-grid">
                            <button class="btn btn-primary" id="searchBtn">开始检索</button>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h4>检索统计</h4>
                    </div>
                    <div class="card-body" id="searchStats">
                        <div class="text-center py-5">
                            <p class="text-muted">请先执行检索</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结果展示区域 -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4>输入序列</h4>
                    </div>
                    <div class="card-body">
                        <div id="inputChart" style="width: 100%; height: 300px;"></div>
                    </div>
                </div>

                <div id="resultsSection" style="display: none;">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4>检索结果</h4>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-secondary active" data-view="separate">分开显示</button>
                                <button class="btn btn-sm btn-outline-secondary" data-view="overlay">叠加显示</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="separateView">
                                <div id="resultCharts"></div>
                            </div>
                            <div id="overlayView" style="display: none;">
                                <div id="overlayChart" style="width: 100%; height: 400px;"></div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h4>相似度分析</h4>
                        </div>
                        <div class="card-body">
                            <div id="similarityAnalysis">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>序号</th>
                                                <th>股票代码</th>
                                                <th>日期范围</th>
                                                <th>相似度分数</th>
                                                <th>涨跌幅</th>
                                                <th>后续走势</th>
                                            </tr>
                                        </thead>
                                        <tbody id="similarityTable">
                                            <!-- 相似度数据将通过JavaScript动态填充 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">登录</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="mb-3">
                            <label for="registerUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="registerUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">电子邮箱</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerFullName" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="registerFullName">
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerConfirmPassword" class="form-label">确认密码</label>
                            <input type="password" class="form-control" id="registerConfirmPassword" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">注册</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 智能体投顾助手. 保留所有权利.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../static/js/navbar.js"></script>
    <script src="../static/js/tsrag.js"></script>
</body>
</html>
