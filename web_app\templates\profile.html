<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - 智能体投顾助手</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../static/css/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">智能体投顾助手</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/portfolio">投资组合</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="newsLink">新闻聚合</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/model">模型测试</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/tsrag">时序检索</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center user-dropdown" id="userDropdown">
                    <!-- 用户信息将通过JavaScript动态填充 -->
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="mb-0">个人资料</h3>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="user-avatar mx-auto" style="width: 100px; height: 100px; font-size: 2.5rem;" id="profileAvatar"></div>
                            <h4 class="mt-3" id="profileUsername"></h4>
                            <p class="text-muted" id="profileEmail"></p>
                        </div>
                        <form id="profileForm">
                            <div class="mb-3">
                                <label for="fullName" class="form-label">姓名</label>
                                <input type="text" class="form-control" id="fullName">
                            </div>
                            <div class="mb-3">
                                <label for="phoneNumber" class="form-label">手机号码</label>
                                <input type="tel" class="form-control" id="phoneNumber" placeholder="请输入手机号码">
                            </div>
                            <div class="mb-3">
                                <label for="address" class="form-label">地址</label>
                                <input type="text" class="form-control" id="address" placeholder="请输入您的地址">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">第三方账号绑定</label>
                                <div class="d-flex align-items-center mt-2">
                                    <div class="me-3">
                                        <button type="button" id="bindWechatBtn" class="btn btn-outline-success d-flex align-items-center">
                                            <i class="bi bi-wechat me-2"></i>
                                            <span id="wechatBindStatus">绑定微信</span>
                                        </button>
                                    </div>
                                    <div>
                                        <button type="button" id="bindAlipayBtn" class="btn btn-outline-primary d-flex align-items-center">
                                            <i class="bi bi-wallet2 me-2"></i>
                                            <span id="alipayBindStatus">绑定支付宝</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="d-grid mt-4">
                                <button type="submit" class="btn btn-primary">更新资料</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 当前订阅信息 -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">当前订阅</h3>
                    </div>
                    <div class="card-body" id="currentSubscription">
                        <!-- 当前订阅信息将通过JavaScript动态填充 -->
                        <div class="text-center py-3">
                            <div class="loading-spinner"></div>
                            <p class="mt-3">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <!-- 订阅计划 -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">订阅服务</h3>
                    </div>
                    <div class="card-body">
                        <div class="row" id="subscriptionPlans">
                            <!-- 订阅计划将通过JavaScript动态填充 -->
                            <div class="text-center py-3 col-12">
                                <div class="loading-spinner"></div>
                                <p class="mt-3">加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">API令牌管理</h3>
                        <button class="btn btn-sm btn-primary" id="createTokenBtn" data-bs-toggle="modal" data-bs-target="#createTokenModal">
                            <i class="bi bi-plus-circle me-1"></i> 创建新令牌
                        </button>
                    </div>
                    <div class="card-body">
                        <p>您可以使用API令牌访问我们的API服务。令牌具有不同的权限级别和访问范围。</p>

                        <div class="alert alert-info d-flex align-items-center" role="alert">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                注意：令牌在创建后只会显示一次完整内容。请妥善保管您的令牌，不要共享给他人。
                            </div>
                        </div>

                        <div class="table-responsive mt-3">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>令牌前缀</th>
                                        <th>权限级别</th>
                                        <th>状态</th>
                                        <th>创建日期</th>
                                        <th>过期时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="tokensList">
                                    <!-- 令牌列表将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 创建令牌模态框 -->
                <div class="modal fade" id="createTokenModal" tabindex="-1" aria-labelledby="createTokenModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="createTokenModalLabel">创建新的API令牌</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                            </div>
                            <div class="modal-body">
                                <form id="createTokenForm">
                                    <div class="mb-3">
                                        <label for="tokenName" class="form-label">令牌名称</label>
                                        <input type="text" class="form-control" id="tokenName" placeholder="例如：我的应用" required>
                                        <div class="form-text">为您的令牌起一个有意义的名称，便于识别</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="tokenExpiration" class="form-label">过期时间</label>
                                        <select class="form-select" id="tokenExpiration" required>
                                            <option value="30">一个月</option>
                                            <option value="90">三个月</option>
                                            <option value="180">六个月</option>
                                            <option value="365">一年</option>
                                            <option value="0">永不过期</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="tokenPermission" class="form-label">权限级别</label>
                                        <select class="form-select" id="tokenPermission" required>
                                            <option value="read">只读 - 只能访问数据，不能修改</option>
                                            <option value="write">读写 - 可以访问和修改数据</option>
                                            <option value="admin">管理员 - 完全访问权限（谨慎使用）</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">访问范围</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="market_data" id="scopeMarketData" checked>
                                            <label class="form-check-label" for="scopeMarketData">
                                                市场数据 - 股票和期货行情数据
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="portfolio" id="scopePortfolio">
                                            <label class="form-check-label" for="scopePortfolio">
                                                投资组合 - 管理您的投资组合
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="model" id="scopeModel">
                                            <label class="form-check-label" for="scopeModel">
                                                模型预测 - 访问预测模型和结果
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="user" id="scopeUser">
                                            <label class="form-check-label" for="scopeUser">
                                                用户信息 - 访问和管理用户信息
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="tokenDescription" class="form-label">描述（可选）</label>
                                        <textarea class="form-control" id="tokenDescription" rows="2" placeholder="请输入令牌用途描述"></textarea>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="generateTokenBtn">生成令牌</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 令牌详情模态框 -->
                <div class="modal fade" id="tokenDetailModal" tabindex="-1" aria-labelledby="tokenDetailModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="tokenDetailModalLabel">令牌详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                            </div>
                            <div class="modal-body" id="tokenDetailContent">
                                <!-- 令牌详情将通过JavaScript动态填充 -->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">收藏的股票</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>股票代码</th>
                                        <th>名称</th>
                                        <th>当前价格</th>
                                        <th>涨跌幅</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="favoritesList">
                                    <!-- 收藏列表将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">© 2025 智能体投顾助手. 保留所有权利.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../static/js/profile.js"></script>
</body>
</html>
