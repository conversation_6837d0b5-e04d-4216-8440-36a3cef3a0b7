"""
<PERSON> agent for the AI Hedge Fund module.

This agent analyzes stocks using <PERSON>'s investment principles,
particularly his "All Weather" approach and economic cycle analysis.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json
import numpy as np

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_financial_metrics, get_market_cap, search_line_items, get_price_data
from ..tools.macro_data import get_macro_indicators, analyze_macro_trends
from ..utils.llm import call_llm


class RayDalioSignal(BaseModel):
    """Signal generated by the Ray Dalio agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    economic_environment: str = Field(description="Current economic environment assessment")
    asset_class_recommendation: Dict[str, float] = Field(description="Recommended asset class allocation")


def ray_dalio_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using Ray Dalio's principles and LLM reasoning.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with Ray Dalio's analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Define the macro indicators to analyze
    indicators = [
        "GDP",
        "CPI",
        "UNEMPLOYMENT",
        "INTEREST_RATE",
    ]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    dalio_analysis = {}
    
    # Update progress status if available
    if "progress" in state["metadata"]:
        for ticker in tickers:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Fetching macro data")
    
    # Fetch macro data
    macro_data = get_macro_indicators(indicators, start_date, end_date)
    
    # Analyze macro trends
    macro_trend_analysis = analyze_macro_trends(macro_data)
    
    # Determine economic environment
    economic_environment = determine_economic_environment(macro_trend_analysis)
    
    # Store macro analysis
    analysis_data["macro_trends"] = macro_trend_analysis
    analysis_data["economic_environment"] = economic_environment
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Fetching financial metrics")
        
        # Fetch required data
        metrics = get_financial_metrics(ticker, end_date, period="ttm", limit=5)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Gathering financial line items")
        
        financial_line_items = search_line_items(
            ticker,
            [
                "revenue",
                "net_income",
                "earnings_per_share",
                "total_assets",
                "total_liabilities",
                "cash_and_cash_equivalents",
                "long_term_debt",
                "inventory",
                "accounts_receivable",
                "research_and_development",
            ],
            end_date,
            limit=5,  # Get 5 years of data for trend analysis
        )
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Getting price data")
        
        # Get price data
        price_data = get_price_data(ticker, start_date, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Getting market cap")
        
        # Get current market cap
        market_cap = get_market_cap(ticker, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Analyzing debt levels")
        
        # Analyze debt levels
        debt_analysis = analyze_debt(financial_line_items)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Analyzing cash flow stability")
        
        # Analyze cash flow stability
        cash_flow_analysis = analyze_cash_flow_stability(financial_line_items)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Analyzing economic cycle sensitivity")
        
        # Analyze economic cycle sensitivity
        cycle_sensitivity_analysis = analyze_economic_cycle_sensitivity(ticker, economic_environment)
        
        # Calculate total score
        total_score = (
            debt_analysis["score"] + 
            cash_flow_analysis["score"] + 
            cycle_sensitivity_analysis["score"]
        )
        
        max_possible_score = (
            debt_analysis["max_score"] + 
            cash_flow_analysis["max_score"] + 
            cycle_sensitivity_analysis["max_score"]
        )
        
        # Generate trading signal based on score and economic environment
        if total_score >= 0.7 * max_possible_score:
            signal = "bullish"
        elif total_score <= 0.3 * max_possible_score:
            signal = "bearish"
        else:
            signal = "neutral"
        
        # Combine all analysis results
        analysis_data[ticker] = {
            "signal": signal,
            "score": total_score,
            "max_score": max_possible_score,
            "debt_analysis": debt_analysis,
            "cash_flow_analysis": cash_flow_analysis,
            "cycle_sensitivity_analysis": cycle_sensitivity_analysis,
            "market_cap": market_cap,
            "economic_environment": economic_environment,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Generating Ray Dalio analysis")
        
        # Generate detailed analysis using LLM
        dalio_output = generate_ray_dalio_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        dalio_analysis[ticker] = {
            "signal": dalio_output.signal,
            "confidence": dalio_output.confidence,
            "reasoning": dalio_output.reasoning,
            "economic_environment": dalio_output.economic_environment,
            "asset_class_recommendation": dalio_output.asset_class_recommendation,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("ray_dalio_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(dalio_analysis, "Ray Dalio Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["ray_dalio_agent"] = dalio_analysis
    
    return state


def determine_economic_environment(macro_trend_analysis: Dict[str, Any]) -> str:
    """
    Determine the current economic environment based on macro trends.
    
    Args:
        macro_trend_analysis: Analysis of macro trends
        
    Returns:
        Economic environment category
    """
    # Extract trend directions
    gdp_trend = macro_trend_analysis.get("GDP", {}).get("trend_direction", "stable")
    inflation_trend = macro_trend_analysis.get("CPI", {}).get("trend_direction", "stable")
    
    # Determine economic environment
    if gdp_trend == "increasing":
        if inflation_trend == "increasing":
            return "rising_growth_rising_inflation"
        elif inflation_trend == "decreasing":
            return "rising_growth_falling_inflation"
        else:
            return "rising_growth_stable_inflation"
    elif gdp_trend == "decreasing":
        if inflation_trend == "increasing":
            return "falling_growth_rising_inflation"
        elif inflation_trend == "decreasing":
            return "falling_growth_falling_inflation"
        else:
            return "falling_growth_stable_inflation"
    else:  # stable
        if inflation_trend == "increasing":
            return "stable_growth_rising_inflation"
        elif inflation_trend == "decreasing":
            return "stable_growth_falling_inflation"
        else:
            return "stable_growth_stable_inflation"


def analyze_debt(financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze debt levels with Ray Dalio's perspective.
    
    Args:
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with debt analysis results
    """
    if not financial_line_items:
        return {"score": 0, "max_score": 3, "details": "Insufficient data for debt analysis"}
    
    score = 0
    max_score = 3
    reasoning = []
    
    latest_item = financial_line_items[0]
    
    # Check debt-to-assets ratio
    if (hasattr(latest_item, "total_assets") and latest_item.total_assets and
        hasattr(latest_item, "long_term_debt") and latest_item.long_term_debt):
        
        debt_to_assets = latest_item.long_term_debt / latest_item.total_assets
        
        if debt_to_assets < 0.2:  # Low debt
            score += 2
            reasoning.append(f"Low debt-to-assets ratio of {debt_to_assets:.1%}")
        elif debt_to_assets < 0.4:  # Moderate debt
            score += 1
            reasoning.append(f"Moderate debt-to-assets ratio of {debt_to_assets:.1%}")
        elif debt_to_assets > 0.6:  # High debt
            score -= 1
            reasoning.append(f"High debt-to-assets ratio of {debt_to_assets:.1%}")
        else:
            reasoning.append(f"Average debt-to-assets ratio of {debt_to_assets:.1%}")
    else:
        reasoning.append("Debt-to-assets ratio data not available")
    
    # Check debt trend
    if len(financial_line_items) >= 3:
        debt_values = []
        for item in financial_line_items[:3]:  # Look at last 3 years
            if hasattr(item, "long_term_debt") and item.long_term_debt is not None:
                debt_values.append(item.long_term_debt)
        
        if len(debt_values) >= 3:
            if debt_values[0] < debt_values[1] < debt_values[2]:  # Decreasing debt
                score += 1
                reasoning.append("Decreasing debt trend over the last 3 years")
            elif debt_values[0] > debt_values[1] > debt_values[2]:  # Increasing debt
                score -= 1
                reasoning.append("Increasing debt trend over the last 3 years")
            else:
                reasoning.append("Mixed or stable debt trend over the last 3 years")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_cash_flow_stability(financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze cash flow stability with Ray Dalio's perspective.
    
    Args:
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with cash flow stability analysis results
    """
    if not financial_line_items or len(financial_line_items) < 3:
        return {"score": 0, "max_score": 3, "details": "Insufficient data for cash flow stability analysis"}
    
    score = 0
    max_score = 3
    reasoning = []
    
    # Extract net income values
    net_income_values = []
    for item in financial_line_items:
        if hasattr(item, "net_income") and item.net_income is not None:
            net_income_values.append(item.net_income)
    
    # Calculate net income stability
    if len(net_income_values) >= 3:
        # Calculate coefficient of variation (lower is more stable)
        mean_income = np.mean(net_income_values)
        std_income = np.std(net_income_values)
        
        if mean_income != 0:
            cv = std_income / abs(mean_income)
            
            if cv < 0.1:  # Very stable
                score += 2
                reasoning.append(f"Very stable net income with coefficient of variation of {cv:.2f}")
            elif cv < 0.2:  # Moderately stable
                score += 1
                reasoning.append(f"Moderately stable net income with coefficient of variation of {cv:.2f}")
            elif cv > 0.5:  # Highly volatile
                score -= 1
                reasoning.append(f"Highly volatile net income with coefficient of variation of {cv:.2f}")
            else:
                reasoning.append(f"Average net income stability with coefficient of variation of {cv:.2f}")
    else:
        reasoning.append("Insufficient net income data for stability analysis")
    
    # Check for consistent profitability
    if len(net_income_values) >= 3:
        profitable_years = sum(1 for income in net_income_values if income > 0)
        
        if profitable_years == len(net_income_values):  # All years profitable
            score += 1
            reasoning.append("Consistently profitable over the analyzed period")
        elif profitable_years == 0:  # No profitable years
            score -= 1
            reasoning.append("No profitable years over the analyzed period")
        else:
            reasoning.append(f"Profitable in {profitable_years} out of {len(net_income_values)} years")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_economic_cycle_sensitivity(ticker: str, economic_environment: str) -> Dict[str, Any]:
    """
    Analyze sensitivity to economic cycles with Ray Dalio's perspective.
    
    Args:
        ticker: Stock ticker symbol
        economic_environment: Current economic environment
        
    Returns:
        Dictionary with economic cycle sensitivity analysis results
    """
    score = 0
    max_score = 4
    reasoning = []
    
    # Determine sector based on ticker (simplified)
    sector = determine_sector(ticker)
    
    # Analyze sector sensitivity to economic environment
    if economic_environment == "rising_growth_rising_inflation":
        # Commodities, cyclicals, and inflation-protected assets do well
        if sector in ["energy", "materials", "industrials", "financials"]:
            score += 2
            reasoning.append(f"{sector.title()} sector tends to perform well in rising growth, rising inflation environments")
        elif sector in ["consumer_staples", "utilities", "healthcare"]:
            score += 0
            reasoning.append(f"{sector.title()} sector tends to have neutral performance in rising growth, rising inflation environments")
        else:
            score -= 1
            reasoning.append(f"{sector.title()} sector may face headwinds in rising growth, rising inflation environments")
    
    elif economic_environment == "rising_growth_falling_inflation":
        # Equities and growth stocks do well
        if sector in ["technology", "consumer_discretionary", "communication_services"]:
            score += 2
            reasoning.append(f"{sector.title()} sector tends to perform well in rising growth, falling inflation environments")
        elif sector in ["industrials", "financials", "materials"]:
            score += 1
            reasoning.append(f"{sector.title()} sector tends to perform moderately well in rising growth, falling inflation environments")
        else:
            score += 0
            reasoning.append(f"{sector.title()} sector tends to have neutral performance in rising growth, falling inflation environments")
    
    elif economic_environment == "falling_growth_rising_inflation":
        # Challenging environment for most assets
        if sector in ["consumer_staples", "utilities", "energy"]:
            score += 1
            reasoning.append(f"{sector.title()} sector tends to be more resilient in falling growth, rising inflation environments")
        elif sector in ["technology", "consumer_discretionary", "communication_services"]:
            score -= 2
            reasoning.append(f"{sector.title()} sector tends to underperform in falling growth, rising inflation environments")
        else:
            score -= 1
            reasoning.append(f"{sector.title()} sector may face challenges in falling growth, rising inflation environments")
    
    elif economic_environment == "falling_growth_falling_inflation":
        # Bonds and defensive stocks do well
        if sector in ["utilities", "consumer_staples", "healthcare"]:
            score += 2
            reasoning.append(f"{sector.title()} sector tends to perform well in falling growth, falling inflation environments")
        elif sector in ["technology", "communication_services"]:
            score += 0
            reasoning.append(f"{sector.title()} sector tends to have mixed performance in falling growth, falling inflation environments")
        else:
            score -= 1
            reasoning.append(f"{sector.title()} sector may underperform in falling growth, falling inflation environments")
    
    else:  # Stable environments
        # Balanced approach
        score += 0
        reasoning.append(f"In a stable economic environment, {sector.title()} sector performance depends more on company-specific factors")
    
    # Add Ray Dalio's diversification principle
    reasoning.append("Ray Dalio emphasizes diversification across asset classes and economic environments")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
        "sector": sector,
    }


def determine_sector(ticker: str) -> str:
    """
    Determine the sector of a stock based on its ticker symbol.
    
    This is a simplified mapping and would be replaced with actual sector data in production.
    
    Args:
        ticker: Stock ticker symbol
        
    Returns:
        Sector name
    """
    # Simplified mapping of tickers to sectors
    sector_mapping = {
        # Technology
        "AAPL": "technology",
        "MSFT": "technology",
        "GOOGL": "technology",
        "GOOG": "technology",
        "META": "technology",
        "NVDA": "technology",
        "INTC": "technology",
        "AMD": "technology",
        "CSCO": "technology",
        "ORCL": "technology",
        "IBM": "technology",
        "ADBE": "technology",
        "CRM": "technology",
        
        # Consumer Discretionary
        "AMZN": "consumer_discretionary",
        "TSLA": "consumer_discretionary",
        "HD": "consumer_discretionary",
        "MCD": "consumer_discretionary",
        "NKE": "consumer_discretionary",
        "SBUX": "consumer_discretionary",
        "TGT": "consumer_discretionary",
        "LOW": "consumer_discretionary",
        
        # Communication Services
        "NFLX": "communication_services",
        "DIS": "communication_services",
        "CMCSA": "communication_services",
        "VZ": "communication_services",
        "T": "communication_services",
        
        # Healthcare
        "JNJ": "healthcare",
        "PFE": "healthcare",
        "UNH": "healthcare",
        "MRK": "healthcare",
        "ABT": "healthcare",
        "ABBV": "healthcare",
        "LLY": "healthcare",
        
        # Financials
        "JPM": "financials",
        "BAC": "financials",
        "WFC": "financials",
        "C": "financials",
        "GS": "financials",
        "MS": "financials",
        "V": "financials",
        "MA": "financials",
        
        # Consumer Staples
        "PG": "consumer_staples",
        "KO": "consumer_staples",
        "PEP": "consumer_staples",
        "WMT": "consumer_staples",
        "COST": "consumer_staples",
        
        # Industrials
        "GE": "industrials",
        "BA": "industrials",
        "CAT": "industrials",
        "MMM": "industrials",
        "HON": "industrials",
        "UPS": "industrials",
        "FDX": "industrials",
        
        # Energy
        "XOM": "energy",
        "CVX": "energy",
        "COP": "energy",
        "EOG": "energy",
        "SLB": "energy",
        
        # Materials
        "DD": "materials",
        "DOW": "materials",
        "FCX": "materials",
        "NEM": "materials",
        
        # Utilities
        "NEE": "utilities",
        "DUK": "utilities",
        "SO": "utilities",
        "D": "utilities",
    }
    
    # Return the sector if found, otherwise default to "unknown"
    return sector_mapping.get(ticker, "unknown")


def generate_ray_dalio_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> RayDalioSignal:
    """
    Get investment decision from LLM with Ray Dalio's principles.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        RayDalioSignal object with the investment decision
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Ray Dalio AI agent. Decide on investment signals based on Ray Dalio's principles:
    - Economic Cycles: Understand where we are in the economic cycle and how it affects different asset classes
    - All Weather Strategy: Balance portfolio across different economic environments
    - Risk Parity: Focus on risk allocation rather than capital allocation
    - Diversification: Seek uncorrelated return streams to reduce risk
    - Debt Cycles: Pay attention to long-term and short-term debt cycles
    - Cash Flow Analysis: Focus on sustainable, predictable cash flows
    - Principles-Based Approach: Apply systematic thinking to investment decisions

    When providing your reasoning, be thorough and specific by:
    1. Identifying the current economic environment and its implications
    2. Explaining how the company fits into Ray Dalio's framework
    3. Discussing debt levels and cash flow stability
    4. Providing a balanced view of risks and opportunities
    5. Recommending an asset allocation approach based on the All Weather strategy
    6. Using Ray Dalio's systematic, principles-based style in your explanation

    For example, if bullish: "In the current rising growth environment, this company's low debt levels and stable cash flows position it well. Following Dalio's principles, we should overweight equities in this phase of the economic cycle..."
    For example, if bearish: "With high debt levels during a period of rising interest rates, this company faces significant headwinds. Dalio's framework suggests reducing exposure to highly leveraged companies in this economic environment..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following data, create the investment signal as Ray Dalio would:

    Analysis Data for {ticker}:
    {json.dumps(analysis_data[ticker], indent=2)}

    Macro Environment:
    {json.dumps(analysis_data["economic_environment"], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string",
      "economic_environment": "string describing the current economic environment",
      "asset_class_recommendation": {{
        "stocks": float percentage (0-100),
        "bonds": float percentage (0-100),
        "commodities": float percentage (0-100),
        "cash": float percentage (0-100)
      }}
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_ray_dalio_signal():
        ticker_data = analysis_data.get(ticker, {})
        signal = ticker_data.get("signal", "neutral")
        score = ticker_data.get("score", 0)
        max_score = ticker_data.get("max_score", 1)
        confidence = (score / max_score * 100) if max_score > 0 else 50.0
        
        # Default asset allocation based on economic environment
        economic_env = analysis_data.get("economic_environment", "stable_growth_stable_inflation")
        
        if "rising_growth_rising_inflation" in economic_env:
            asset_allocation = {"stocks": 30, "bonds": 15, "commodities": 40, "cash": 15}
        elif "rising_growth_falling_inflation" in economic_env:
            asset_allocation = {"stocks": 50, "bonds": 30, "commodities": 10, "cash": 10}
        elif "falling_growth_rising_inflation" in economic_env:
            asset_allocation = {"stocks": 15, "bonds": 25, "commodities": 40, "cash": 20}
        elif "falling_growth_falling_inflation" in economic_env:
            asset_allocation = {"stocks": 20, "bonds": 50, "commodities": 10, "cash": 20}
        else:
            asset_allocation = {"stocks": 30, "bonds": 40, "commodities": 15, "cash": 15}
        
        return RayDalioSignal(
            signal=signal,
            confidence=confidence,
            reasoning="Analysis based on quantitative metrics only. Unable to generate detailed reasoning.",
            economic_environment=economic_env,
            asset_class_recommendation=asset_allocation
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=RayDalioSignal,
        agent_name="ray_dalio_agent",
        default_factory=create_default_ray_dalio_signal,
    )
