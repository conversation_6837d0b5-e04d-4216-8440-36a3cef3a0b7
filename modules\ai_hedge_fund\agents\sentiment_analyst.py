"""
Sentiment Analyst agent for the AI Hedge Fund module.

This agent analyzes market sentiment based on news and social media.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.news_sentiment import get_news_sentiment
from ..utils.llm import call_llm


class SentimentSignal(BaseModel):
    """Signal generated by the Sentiment Analyst agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")


def sentiment_analyst_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes market sentiment based on news and social media.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with sentiment analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    sentiment_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("sentiment_analyst_agent", ticker, "Fetching news data")
        
        # Get news sentiment
        news_sentiment = get_news_sentiment(ticker, start_date, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("sentiment_analyst_agent", ticker, "Analyzing sentiment")
        
        # Determine signal based on sentiment score
        sentiment_score = news_sentiment.get("sentiment_score", 0.0)
        positive_count = news_sentiment.get("positive_count", 0)
        negative_count = news_sentiment.get("negative_count", 0)
        
        # Generate signal based on sentiment score and article counts
        if sentiment_score > 0.2 and positive_count > negative_count:
            signal = "bullish"
            confidence = min(70.0 + sentiment_score * 30.0, 95.0)  # Max 95% confidence
        elif sentiment_score < -0.2 and negative_count > positive_count:
            signal = "bearish"
            confidence = min(70.0 + abs(sentiment_score) * 30.0, 95.0)  # Max 95% confidence
        else:
            signal = "neutral"
            confidence = 50.0 + abs(sentiment_score) * 20.0  # Higher confidence for stronger sentiment
        
        # Store analysis data
        analysis_data[ticker] = {
            "signal": signal,
            "confidence": confidence,
            "news_sentiment": news_sentiment,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("sentiment_analyst_agent", ticker, "Generating detailed analysis")
        
        # Generate detailed analysis using LLM
        sentiment_output = generate_sentiment_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        sentiment_analysis[ticker] = {
            "signal": sentiment_output.signal,
            "confidence": sentiment_output.confidence,
            "reasoning": sentiment_output.reasoning,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("sentiment_analyst_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(sentiment_analysis, "Sentiment Analyst Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["sentiment_analyst_agent"] = sentiment_analysis
    
    return state


def generate_sentiment_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> SentimentSignal:
    """
    Generate detailed sentiment analysis using LLM.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        SentimentSignal object with the sentiment analysis
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Sentiment Analyst AI agent. Analyze market sentiment based on news and social media data.

    Key principles for sentiment analysis:
    - News Sentiment: Evaluate the overall sentiment in news coverage
    - Headline Analysis: Pay special attention to recent headlines
    - Sentiment Trends: Consider if sentiment is improving or deteriorating
    - Contrarian Opportunities: Sometimes extreme sentiment in one direction can signal a reversal
    - Volume of Coverage: Unusually high news volume may indicate important developments
    - Source Credibility: Give more weight to reputable financial news sources

    When providing your reasoning, be thorough and specific by:
    1. Summarizing the overall sentiment landscape (positive, negative, or mixed)
    2. Highlighting specific headlines or news that significantly influenced your decision
    3. Explaining how the sentiment aligns with or contradicts price action
    4. Providing a balanced view that considers both positive and negative news
    5. Using a professional, analytical tone in your explanation

    For example, if bullish: "News sentiment is predominantly positive with [X] positive articles versus [Y] negative. Recent headlines about [specific news] suggest improving market perception..."
    For example, if bearish: "News coverage has taken a negative turn with [X] negative articles versus [Y] positive. Concerning headlines about [specific issue] are likely to weigh on investor sentiment..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following news sentiment data, create a trading signal for {ticker}:

    Analysis Data:
    {json.dumps(analysis_data[ticker], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string"
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_sentiment_signal():
        ticker_data = analysis_data.get(ticker, {})
        signal = ticker_data.get("signal", "neutral")
        confidence = ticker_data.get("confidence", 50.0)
        
        news_sentiment = ticker_data.get("news_sentiment", {})
        recent_headlines = news_sentiment.get("recent_headlines", [])
        headline_text = ""
        
        if recent_headlines:
            headline_text = f"Recent headlines include: {recent_headlines[0].get('title', '')}"
        
        return SentimentSignal(
            signal=signal,
            confidence=confidence,
            reasoning=f"Analysis based on news sentiment with a score of {news_sentiment.get('sentiment_score', 0):.2f}. {headline_text}"
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=SentimentSignal,
        agent_name="sentiment_analyst_agent",
        default_factory=create_default_sentiment_signal,
    )
