// 导航菜单控制脚本

// 当DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化导航菜单
    initNavbar();
});

// 初始化导航菜单
async function initNavbar() {
    // 设置当前页面的导航项为激活状态
    setActiveNavItem();

    // 检查用户登录状态
    const isLoggedIn = localStorage.getItem('accessToken') !== null;

    if (isLoggedIn) {
        // 如果用户已登录，检查订阅级别并控制菜单显示
        await checkSubscriptionAndUpdateMenu();
    } else {
        // 如果用户未登录，隐藏需要订阅的菜单项
        hideSubscriptionRequiredItems();
    }
}

// 设置当前页面的导航项为激活状态
function setActiveNavItem() {
    // 获取当前页面的路径
    const currentPath = window.location.pathname;

    // 移除所有导航项的激活状态
    document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 根据当前路径设置相应的导航项为激活状态
    if (currentPath === '/' || currentPath === '/index.html') {
        document.getElementById('nav-home').classList.add('active');
    } else if (currentPath.includes('/portfolio')) {
        document.getElementById('nav-portfolio').classList.add('active');
    } else if (currentPath.includes('/model')) {
        document.getElementById('nav-model').classList.add('active');
    } else if (currentPath.includes('/tsrag')) {
        document.getElementById('nav-tsrag').classList.add('active');
    }
}

// 检查用户订阅级别并更新菜单显示
async function checkSubscriptionAndUpdateMenu() {
    try {
        // 获取用户订阅信息
        const subscriptionData = await getUserSubscription();

        if (subscriptionData && subscriptionData.subscription && subscriptionData.plan) {
            const tier = subscriptionData.subscription.tier;

            // 处理需要特定订阅级别的菜单项
            document.querySelectorAll('.subscription-required').forEach(item => {
                const requiredTier = item.getAttribute('data-required-tier');

                // 根据订阅级别决定是否显示菜单项
                if (requiredTier === 'professional' && tier !== 'professional') {
                    item.style.display = 'none';
                } else if (requiredTier === 'advanced' && tier !== 'advanced' && tier !== 'professional') {
                    item.style.display = 'none';
                } else {
                    item.style.display = '';
                }
            });
        } else {
            // 如果无法获取订阅信息，隐藏需要订阅的菜单项
            hideSubscriptionRequiredItems();
        }
    } catch (error) {
        console.error('检查订阅级别失败:', error);
        // 出错时隐藏需要订阅的菜单项
        hideSubscriptionRequiredItems();
    }
}

// 隐藏需要订阅的菜单项
function hideSubscriptionRequiredItems() {
    document.querySelectorAll('.subscription-required').forEach(item => {
        item.style.display = 'none';
    });
}

// 获取用户订阅信息
async function getUserSubscription() {
    const accessToken = localStorage.getItem('accessToken');

    if (!accessToken) {
        console.warn('未登录，无法获取订阅信息');
        return null;
    }

    try {
        // 调用API获取用户订阅信息
        const response = await fetchWithAuth(`${API_BASE_URL}/${API_VERSION}/subscription/my-subscription`);

        if (!response.ok) {
            // 如果是401错误，说明用户未登录或令牌无效，静默处理
            if (response.status === 401) {
                console.warn('用户未登录或令牌无效，无法获取订阅信息');
                return null;
            }
            throw new Error(`获取订阅信息失败: ${response.status}`);
        }

        const data = await response.json();

        if (data.code === 200 && data.data) {
            return data.data;
        }

        return null;
    } catch (error) {
        console.warn('获取订阅信息失败:', error);
        return null;
    }
}
