"""
<PERSON> agent for the AI Hedge Fund module.

This agent analyzes stocks using <PERSON>'s investment principles.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json
import numpy as np

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_financial_metrics, get_market_cap, search_line_items, get_price_data
from ..utils.llm import call_llm


class PeterLynchSignal(BaseModel):
    """Signal generated by the <PERSON> agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")


def peter_lynch_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using <PERSON>'s principles and LLM reasoning.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with <PERSON>'s analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    lynch_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Fetching financial metrics")
        
        # Fetch required data
        metrics = get_financial_metrics(ticker, end_date, period="ttm", limit=5)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Gathering financial line items")
        
        financial_line_items = search_line_items(
            ticker,
            [
                "revenue",
                "net_income",
                "earnings_per_share",
                "total_assets",
                "total_liabilities",
                "cash_and_cash_equivalents",
                "long_term_debt",
                "inventory",
                "accounts_receivable",
                "research_and_development",
                "capital_expenditure",
            ],
            end_date,
            limit=5,  # Get 5 years of data for growth analysis
        )
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Getting price data")
        
        # Get price data
        price_data = get_price_data(ticker, start_date, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Getting market cap")
        
        # Get current market cap
        market_cap = get_market_cap(ticker, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Analyzing growth")
        
        # Analyze growth
        growth_analysis = analyze_growth(financial_line_items)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Analyzing valuation")
        
        # Analyze valuation
        valuation_analysis = analyze_valuation(metrics, market_cap, growth_analysis)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Analyzing business quality")
        
        # Analyze business quality
        business_quality_analysis = analyze_business_quality(financial_line_items)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Analyzing company story")
        
        # Analyze company story
        company_story_analysis = analyze_company_story(ticker, market_cap)
        
        # Calculate total score
        total_score = (
            growth_analysis["score"] + 
            valuation_analysis["score"] + 
            business_quality_analysis["score"] + 
            company_story_analysis["score"]
        )
        
        max_possible_score = (
            growth_analysis["max_score"] + 
            valuation_analysis["max_score"] + 
            business_quality_analysis["max_score"] + 
            company_story_analysis["max_score"]
        )
        
        # Generate trading signal based on score
        if total_score >= 0.7 * max_possible_score:
            signal = "bullish"
        elif total_score <= 0.3 * max_possible_score:
            signal = "bearish"
        else:
            signal = "neutral"
        
        # Combine all analysis results
        analysis_data[ticker] = {
            "signal": signal,
            "score": total_score,
            "max_score": max_possible_score,
            "growth_analysis": growth_analysis,
            "valuation_analysis": valuation_analysis,
            "business_quality_analysis": business_quality_analysis,
            "company_story_analysis": company_story_analysis,
            "market_cap": market_cap,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Generating Peter Lynch analysis")
        
        # Generate detailed analysis using LLM
        lynch_output = generate_peter_lynch_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        lynch_analysis[ticker] = {
            "signal": lynch_output.signal,
            "confidence": lynch_output.confidence,
            "reasoning": lynch_output.reasoning,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("peter_lynch_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(lynch_analysis, "Peter Lynch Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["peter_lynch_agent"] = lynch_analysis
    
    return state


def analyze_growth(financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze growth metrics with Peter Lynch's perspective.
    
    Args:
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with growth analysis results
    """
    if not financial_line_items or len(financial_line_items) < 2:
        return {"score": 0, "max_score": 5, "details": "Insufficient data for growth analysis"}
    
    score = 0
    max_score = 5
    reasoning = []
    
    # Calculate revenue growth
    revenue_growth = calculate_growth_rate(financial_line_items, "revenue")
    if revenue_growth is not None:
        if revenue_growth > 0.2:  # >20% annual growth
            score += 2
            reasoning.append(f"Excellent revenue growth of {revenue_growth:.1%} annually")
        elif revenue_growth > 0.1:  # >10% annual growth
            score += 1
            reasoning.append(f"Good revenue growth of {revenue_growth:.1%} annually")
        elif revenue_growth < 0:  # Negative growth
            score -= 1
            reasoning.append(f"Concerning revenue decline of {revenue_growth:.1%} annually")
        else:
            reasoning.append(f"Modest revenue growth of {revenue_growth:.1%} annually")
    else:
        reasoning.append("Revenue growth data not available")
    
    # Calculate earnings growth
    earnings_growth = calculate_growth_rate(financial_line_items, "net_income")
    if earnings_growth is not None:
        if earnings_growth > 0.2:  # >20% annual growth
            score += 2
            reasoning.append(f"Excellent earnings growth of {earnings_growth:.1%} annually")
        elif earnings_growth > 0.1:  # >10% annual growth
            score += 1
            reasoning.append(f"Good earnings growth of {earnings_growth:.1%} annually")
        elif earnings_growth < 0:  # Negative growth
            score -= 1
            reasoning.append(f"Concerning earnings decline of {earnings_growth:.1%} annually")
        else:
            reasoning.append(f"Modest earnings growth of {earnings_growth:.1%} annually")
    else:
        reasoning.append("Earnings growth data not available")
    
    # Check for earnings growth > revenue growth (operating leverage)
    if earnings_growth is not None and revenue_growth is not None:
        if earnings_growth > revenue_growth + 0.05:  # Earnings growing faster than revenue
            score += 1
            reasoning.append(f"Positive operating leverage with earnings growing faster than revenue")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
        "revenue_growth": revenue_growth,
        "earnings_growth": earnings_growth,
    }


def analyze_valuation(
    metrics: List[Any],
    market_cap: Optional[float],
    growth_analysis: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Analyze valuation metrics with Peter Lynch's perspective.
    
    Args:
        metrics: List of financial metrics
        market_cap: Current market cap
        growth_analysis: Growth analysis results
        
    Returns:
        Dictionary with valuation analysis results
    """
    if not metrics or market_cap is None:
        return {"score": 0, "max_score": 5, "details": "Insufficient data for valuation analysis"}
    
    score = 0
    max_score = 5
    reasoning = []
    
    # Check P/E ratio
    if hasattr(metrics[0], "pe_ratio") and metrics[0].pe_ratio is not None:
        pe_ratio = metrics[0].pe_ratio
        
        # Calculate PEG ratio if growth data is available
        earnings_growth = growth_analysis.get("earnings_growth")
        peg_ratio = None
        
        if earnings_growth is not None and earnings_growth > 0:
            peg_ratio = pe_ratio / (earnings_growth * 100)  # Convert growth to percentage
            
            if peg_ratio < 1.0:  # PEG < 1 is excellent (Lynch's favorite)
                score += 3
                reasoning.append(f"Excellent PEG ratio of {peg_ratio:.2f} (P/E: {pe_ratio:.1f}, Growth: {earnings_growth:.1%})")
            elif peg_ratio < 1.5:  # PEG < 1.5 is good
                score += 2
                reasoning.append(f"Good PEG ratio of {peg_ratio:.2f} (P/E: {pe_ratio:.1f}, Growth: {earnings_growth:.1%})")
            elif peg_ratio < 2.0:  # PEG < 2 is acceptable
                score += 1
                reasoning.append(f"Acceptable PEG ratio of {peg_ratio:.2f} (P/E: {pe_ratio:.1f}, Growth: {earnings_growth:.1%})")
            else:
                reasoning.append(f"High PEG ratio of {peg_ratio:.2f} (P/E: {pe_ratio:.1f}, Growth: {earnings_growth:.1%})")
        else:
            # If growth data is not available, just check P/E
            if pe_ratio < 15:
                score += 1
                reasoning.append(f"Low P/E ratio of {pe_ratio:.1f}")
            elif pe_ratio > 30:
                score -= 1
                reasoning.append(f"High P/E ratio of {pe_ratio:.1f}")
            else:
                reasoning.append(f"Average P/E ratio of {pe_ratio:.1f}")
    else:
        reasoning.append("P/E ratio data not available")
    
    # Check price-to-sales ratio
    if hasattr(metrics[0], "price_to_sales") and metrics[0].price_to_sales is not None:
        ps_ratio = metrics[0].price_to_sales
        
        if ps_ratio < 1.0:  # Very low P/S
            score += 2
            reasoning.append(f"Very low price-to-sales ratio of {ps_ratio:.1f}")
        elif ps_ratio < 3.0:  # Reasonable P/S
            score += 1
            reasoning.append(f"Reasonable price-to-sales ratio of {ps_ratio:.1f}")
        elif ps_ratio > 10.0:  # Very high P/S
            score -= 1
            reasoning.append(f"Very high price-to-sales ratio of {ps_ratio:.1f}")
        else:
            reasoning.append(f"Average price-to-sales ratio of {ps_ratio:.1f}")
    else:
        reasoning.append("Price-to-sales ratio data not available")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_business_quality(financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze business quality with Peter Lynch's perspective.
    
    Args:
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with business quality analysis results
    """
    if not financial_line_items:
        return {"score": 0, "max_score": 4, "details": "Insufficient data for business quality analysis"}
    
    score = 0
    max_score = 4
    reasoning = []
    
    latest_item = financial_line_items[0]
    
    # Check debt levels
    if (hasattr(latest_item, "total_assets") and latest_item.total_assets and
        hasattr(latest_item, "long_term_debt") and latest_item.long_term_debt):
        
        debt_to_assets = latest_item.long_term_debt / latest_item.total_assets
        
        if debt_to_assets < 0.2:  # Low debt
            score += 2
            reasoning.append(f"Low debt-to-assets ratio of {debt_to_assets:.1%}")
        elif debt_to_assets < 0.4:  # Moderate debt
            score += 1
            reasoning.append(f"Moderate debt-to-assets ratio of {debt_to_assets:.1%}")
        elif debt_to_assets > 0.6:  # High debt
            score -= 1
            reasoning.append(f"High debt-to-assets ratio of {debt_to_assets:.1%}")
        else:
            reasoning.append(f"Average debt-to-assets ratio of {debt_to_assets:.1%}")
    else:
        reasoning.append("Debt-to-assets ratio data not available")
    
    # Check cash position
    if (hasattr(latest_item, "total_assets") and latest_item.total_assets and
        hasattr(latest_item, "cash_and_cash_equivalents") and latest_item.cash_and_cash_equivalents):
        
        cash_ratio = latest_item.cash_and_cash_equivalents / latest_item.total_assets
        
        if cash_ratio > 0.2:  # Strong cash position
            score += 2
            reasoning.append(f"Strong cash position at {cash_ratio:.1%} of total assets")
        elif cash_ratio > 0.1:  # Adequate cash position
            score += 1
            reasoning.append(f"Adequate cash position at {cash_ratio:.1%} of total assets")
        else:
            reasoning.append(f"Cash position at {cash_ratio:.1%} of total assets")
    else:
        reasoning.append("Cash ratio data not available")
    
    # Check R&D investment (if applicable)
    if (hasattr(latest_item, "revenue") and latest_item.revenue and
        hasattr(latest_item, "research_and_development") and latest_item.research_and_development):
        
        rd_ratio = latest_item.research_and_development / latest_item.revenue
        
        if rd_ratio > 0.1:  # High R&D investment
            score += 1
            reasoning.append(f"Strong R&D investment at {rd_ratio:.1%} of revenue")
        elif rd_ratio > 0.05:  # Moderate R&D investment
            score += 0.5
            reasoning.append(f"Moderate R&D investment at {rd_ratio:.1%} of revenue")
        else:
            reasoning.append(f"R&D investment at {rd_ratio:.1%} of revenue")
    
    # Check inventory turnover (if applicable)
    if len(financial_line_items) >= 2:
        current = financial_line_items[0]
        previous = financial_line_items[1]
        
        if (hasattr(current, "inventory") and current.inventory and
            hasattr(previous, "inventory") and previous.inventory and
            hasattr(current, "revenue") and current.revenue):
            
            avg_inventory = (current.inventory + previous.inventory) / 2
            inventory_turnover = current.revenue / avg_inventory
            
            if inventory_turnover > 6:  # High turnover
                score += 1
                reasoning.append(f"Efficient inventory management with turnover of {inventory_turnover:.1f}x")
            elif inventory_turnover < 3:  # Low turnover
                score -= 1
                reasoning.append(f"Potential inventory issues with turnover of {inventory_turnover:.1f}x")
            else:
                reasoning.append(f"Average inventory turnover of {inventory_turnover:.1f}x")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_company_story(ticker: str, market_cap: Optional[float]) -> Dict[str, Any]:
    """
    Analyze company story with Peter Lynch's perspective.
    
    Args:
        ticker: Stock ticker symbol
        market_cap: Current market cap
        
    Returns:
        Dictionary with company story analysis results
    """
    score = 0
    max_score = 3
    reasoning = []
    
    # Check company size (Lynch liked small to mid-sized companies)
    if market_cap is not None:
        if market_cap < 2e9:  # Small cap (< $2B)
            score += 2
            reasoning.append(f"Small-cap company (${market_cap/1e9:.1f}B) with potential for discovery")
        elif market_cap < 10e9:  # Mid cap (< $10B)
            score += 1
            reasoning.append(f"Mid-cap company (${market_cap/1e9:.1f}B) with room to grow")
        else:
            reasoning.append(f"Large-cap company (${market_cap/1e9:.1f}B)")
    else:
        reasoning.append("Market cap data not available")
    
    # Placeholder for industry analysis
    # In a real implementation, this would analyze the industry and sector
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def calculate_growth_rate(financial_line_items: List[Any], field_name: str) -> Optional[float]:
    """
    Calculate compound annual growth rate for a financial metric.
    
    Args:
        financial_line_items: List of financial line items
        field_name: Name of the field to calculate growth for
        
    Returns:
        Compound annual growth rate, or None if data is insufficient
    """
    if len(financial_line_items) < 2:
        return None
    
    # Get the most recent and oldest values
    recent_value = getattr(financial_line_items[0], field_name, None)
    oldest_value = getattr(financial_line_items[-1], field_name, None)
    
    if recent_value is None or oldest_value is None or oldest_value == 0:
        return None
    
    # Calculate years between data points
    years = len(financial_line_items) - 1
    
    # Calculate CAGR
    cagr = (recent_value / oldest_value) ** (1 / years) - 1
    
    return cagr


def generate_peter_lynch_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> PeterLynchSignal:
    """
    Get investment decision from LLM with Peter Lynch's principles.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        PeterLynchSignal object with the investment decision
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Peter Lynch AI agent. Decide on investment signals based on Peter Lynch's principles:
    - Growth at a Reasonable Price: Focus on companies with strong growth but reasonable valuations
    - PEG Ratio: Prefer companies with PEG ratios below 1.0
    - "Buy What You Know": Favor companies with understandable business models
    - Company Categories: Classify companies as slow growers, stalwarts, fast growers, cyclicals, turnarounds, or asset plays
    - Debt Levels: Prefer companies with low debt
    - Insider Ownership: Look for companies where management has skin in the game
    - Niche Advantages: Seek companies with unique market positions or advantages
    - Simple Stories: Favor companies whose investment thesis can be explained simply

    When providing your reasoning, be thorough and specific by:
    1. Explaining the key growth metrics and how they compare to the valuation
    2. Classifying the company into one of Lynch's categories
    3. Highlighting the company's competitive advantages or concerns
    4. Providing a simple "story" for why this is or isn't a good investment
    5. Using Peter Lynch's conversational, straightforward style in your explanation

    For example, if bullish: "This is a classic fast-grower with 25% annual earnings growth but trading at a PEG of only 0.8. The company has a strong balance sheet with minimal debt, and they're expanding into new markets that could drive growth for years..."
    For example, if bearish: "While growth looks good on the surface, the company is taking on too much debt to fuel that growth. The PEG ratio of 2.5 is too high for the risks involved, and inventory is piling up faster than sales..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following data, create the investment signal as Peter Lynch would:

    Analysis Data for {ticker}:
    {json.dumps(analysis_data[ticker], indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string"
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_peter_lynch_signal():
        ticker_data = analysis_data.get(ticker, {})
        signal = ticker_data.get("signal", "neutral")
        score = ticker_data.get("score", 0)
        max_score = ticker_data.get("max_score", 1)
        confidence = (score / max_score * 100) if max_score > 0 else 50.0
        
        return PeterLynchSignal(
            signal=signal,
            confidence=confidence,
            reasoning="Analysis based on quantitative metrics only. Unable to generate detailed reasoning."
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=PeterLynchSignal,
        agent_name="peter_lynch_agent",
        default_factory=create_default_peter_lynch_signal,
    )
