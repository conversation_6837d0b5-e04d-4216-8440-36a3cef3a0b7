# GEMINI.md - AI Agent Context

## 1. Project Overview

This project is a sophisticated, **multi-agent collaborative quantitative investment platform**. It has evolved from a stock data analysis tool into a virtual investment research team, designed to automate complex financial analysis and decision-making.

### Architecture

The system is designed as an **event-driven microservices architecture** built around a central **Orchestration Engine**.

- **Core Engine**: A central **Orchestration Engine** (`LangGraph`) acts as the "Portfolio Manager" agent, directing workflows and coordinating a team of specialized AI agents.
- **AI Expert Agents**: Each investment expert (e.g., Macro Strategist, Fundamental Analyst, Quant Analyst) is an independent microservice that communicates via an event bus.
- **Event Bus**: `Apache Kafka` serves as the central nervous system, allowing agents to communicate asynchronously by publishing and subscribing to events (e.g., "Macro Report Published").
- **Unified Data Backend**: A single **PostgreSQL** database serves as the ultimate source of truth, managing:
    - **Relational Data**: Standard financial data, user profiles, etc.
    - **Document Data**: Semi-structured data like news articles and reports, using the `JSONB` type.
    - **Vector Data**: Embeddings for semantic search, using the `pgvector` extension.
- **API Layer**: A `FastAPI` application provides the primary REST API for the frontend and external clients.
- **Frontend**: A user-facing Web Application (`web_app/`) serves as the Command & Control center for the human Portfolio Manager.

### Key Technologies

- **Backend**: Python
- **AI & Orchestration**: LangGraph, LangChain
- **API**: FastAPI
- **Database**: PostgreSQL with `pgvector` and `JSONB`
- **Messaging**: Apache Kafka
- **Deployment**: Docker, Kubernetes

## 2. Building and Running

### Running the Main Application

The main application entry point appears to be `app.py` in the root directory.

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Start the application
python app.py
```

### Running the Web Application

The self-contained web application has its own startup procedure.

```bash
# 1. Navigate to the web app directory
cd web_app

# 2. Install dependencies (if it has its own requirements.txt)
pip install -r requirements.txt

# 3. Run the web server
python app.py
```

### Running Tests

The project uses `pytest` for testing. Tests are located in the `tests/` directory.

```bash
# Run all tests
pytest

# Run tests with more verbose output
pytest -v
```

## 3. Development Conventions

- **Event-Driven Mindset**: New functionality, especially analytical capabilities, should be implemented as new, independent **Agent Services**. These services must not call each other directly; they must communicate via the Kafka event bus.

- **Unified Database Principle**: All data storage should default to the central PostgreSQL database. Leverage the power of `pgvector` and `JSONB` to perform powerful, single-query filtering across relational, document, and vector data types. Avoid adding new database technologies unless absolutely necessary.

- **Orchestration via LangGraph**: Complex, multi-step workflows that require the coordination of multiple agents should be defined within the central **Orchestration Engine** using LangGraph. This keeps business logic centralized and observable.

- **API Development**: The `rest_api` directory contains the main FastAPI application. New user-facing endpoints should be added here, following the existing structure.

- **Observability is Key**: Given the complexity of the multi-agent system, `LangSmith` should be used to trace the entire lifecycle of a request as it flows through the orchestration engine and various agents. This is critical for debugging and performance analysis.
