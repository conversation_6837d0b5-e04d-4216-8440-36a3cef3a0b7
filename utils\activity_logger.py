#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户活动日志记录工具

该模块提供用户活动日志记录功能，支持可配置的日志级别和类型。
"""

import os
from datetime import datetime
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv
from fastapi import Request
from sqlalchemy.orm import Session

from db.models.user_activity_log import UserActivityLog
from utils.logger import logger

# 加载环境变量
load_dotenv()

# 日志级别常量
LOG_LEVEL_HIGH = "HIGH"      # 高级别：重要操作，必须记录
LOG_LEVEL_MEDIUM = "MEDIUM"  # 中级别：一般操作，建议记录
LOG_LEVEL_LOW = "LOW"        # 低级别：次要操作，可选记录

# 活动类型常量
ACTIVITY_LOGIN = "LOGIN"                 # 登录
ACTIVITY_LOGOUT = "LOGOUT"               # 登出
ACTIVITY_REGISTER = "REGISTER"           # 注册
ACTIVITY_PASSWORD_CHANGE = "PWD_CHANGE"  # 密码修改
ACTIVITY_PASSWORD_RESET = "PWD_RESET"    # 密码重置
ACTIVITY_PROFILE_UPDATE = "PROFILE_UPD"  # 个人资料更新
ACTIVITY_API_TOKEN_GEN = "TOKEN_GEN"     # 生成API令牌
ACTIVITY_API_TOKEN_REVOKE = "TOKEN_REV"  # 撤销API令牌
ACTIVITY_API_CALL = "API_CALL"           # API调用
ACTIVITY_ADMIN_ACTION = "ADMIN_ACTION"   # 管理员操作
ACTIVITY_DATA_ACCESS = "DATA_ACCESS"     # 数据访问
ACTIVITY_DATA_MODIFY = "DATA_MODIFY"     # 数据修改
ACTIVITY_FAILED_LOGIN = "FAILED_LOGIN"   # 登录失败
ACTIVITY_PERMISSION_CHANGE = "PERM_CHG"  # 权限变更

# 默认日志级别配置
DEFAULT_LOG_LEVELS = {
    ACTIVITY_LOGIN: LOG_LEVEL_MEDIUM,
    ACTIVITY_LOGOUT: LOG_LEVEL_LOW,
    ACTIVITY_REGISTER: LOG_LEVEL_HIGH,
    ACTIVITY_PASSWORD_CHANGE: LOG_LEVEL_HIGH,
    ACTIVITY_PASSWORD_RESET: LOG_LEVEL_HIGH,
    ACTIVITY_PROFILE_UPDATE: LOG_LEVEL_MEDIUM,
    ACTIVITY_API_TOKEN_GEN: LOG_LEVEL_HIGH,
    ACTIVITY_API_TOKEN_REVOKE: LOG_LEVEL_HIGH,
    ACTIVITY_API_CALL: LOG_LEVEL_LOW,
    ACTIVITY_ADMIN_ACTION: LOG_LEVEL_HIGH,
    ACTIVITY_DATA_ACCESS: LOG_LEVEL_LOW,
    ACTIVITY_DATA_MODIFY: LOG_LEVEL_MEDIUM,
    ACTIVITY_FAILED_LOGIN: LOG_LEVEL_MEDIUM,
    ACTIVITY_PERMISSION_CHANGE: LOG_LEVEL_HIGH
}

class ActivityLogger:
    """用户活动日志记录器"""
    
    def __init__(self):
        # 从环境变量获取日志记录级别，默认为MEDIUM
        self.min_log_level = os.getenv("ACTIVITY_LOG_MIN_LEVEL", "MEDIUM")
        
        # 从环境变量获取是否启用日志记录，默认为True
        self.enabled = os.getenv("ACTIVITY_LOG_ENABLED", "true").lower() == "true"
        
        # 从环境变量获取要排除的活动类型，默认为空
        excluded_types = os.getenv("ACTIVITY_LOG_EXCLUDED_TYPES", "")
        self.excluded_types = [t.strip() for t in excluded_types.split(",")] if excluded_types else []
        
        # 日志级别优先级
        self.level_priority = {
            LOG_LEVEL_HIGH: 3,
            LOG_LEVEL_MEDIUM: 2,
            LOG_LEVEL_LOW: 1
        }
        
        logger.info(f"活动日志记录器初始化: 最低级别={self.min_log_level}, 已启用={self.enabled}, 排除类型={self.excluded_types}")
    
    def should_log(self, activity_type: str) -> bool:
        """
        判断是否应该记录指定类型的活动
        
        Args:
            activity_type: 活动类型
            
        Returns:
            bool: 是否应该记录
        """
        # 如果日志记录被禁用，直接返回False
        if not self.enabled:
            return False
        
        # 如果活动类型在排除列表中，不记录
        if activity_type in self.excluded_types:
            return False
        
        # 获取活动类型的日志级别
        activity_level = DEFAULT_LOG_LEVELS.get(activity_type, LOG_LEVEL_MEDIUM)
        
        # 判断活动级别是否达到最低记录级别
        return self.level_priority.get(activity_level, 0) >= self.level_priority.get(self.min_log_level, 0)
    
    def log_activity(self, 
                    db_session: Session, 
                    activity_type: str, 
                    activity_detail: str = None, 
                    user_id: int = None,
                    username: str = None,
                    request: Request = None) -> Optional[UserActivityLog]:
        """
        记录用户活动
        
        Args:
            db_session: 数据库会话
            activity_type: 活动类型
            activity_detail: 活动详情
            user_id: 用户ID
            username: 用户名
            request: HTTP请求对象
            
        Returns:
            Optional[UserActivityLog]: 如果记录成功，返回日志对象；否则返回None
        """
        # 判断是否应该记录
        if not self.should_log(activity_type):
            return None
        
        try:
            # 获取活动级别
            activity_level = DEFAULT_LOG_LEVELS.get(activity_type, LOG_LEVEL_MEDIUM)
            
            # 获取IP地址和用户代理
            ip_address = None
            user_agent = None
            if request:
                ip_address = request.client.host
                user_agent = request.headers.get("user-agent", "")
            
            # 创建日志记录
            log_entry = UserActivityLog(
                user_id=user_id,
                username=username,
                activity_type=activity_type,
                activity_level=activity_level,
                activity_detail=activity_detail,
                ip_address=ip_address,
                user_agent=user_agent,
                timestamp=datetime.now()
            )
            
            # 添加到数据库
            db_session.add(log_entry)
            db_session.commit()
            
            return log_entry
        except Exception as e:
            logger.error(f"记录用户活动失败: {e}")
            db_session.rollback()
            return None

# 创建全局活动日志记录器实例
activity_logger = ActivityLogger()
