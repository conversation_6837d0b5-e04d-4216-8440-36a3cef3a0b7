"""
Unit tests for the BarDatasetRetriever class.
"""

import os
import unittest
import numpy as np
import torch
import tempfile
import shutil
from unittest.mock import MagicMock, patch

from bar_dataset_rag import BarDatasetRetriever, create_bar_dataset_retriever


class TestBarDatasetRetriever(unittest.TestCase):
    """Test cases for BarDatasetRetriever."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.embedding_dim = 64
        self.n_neighbors = 3
        self.retriever = BarDatasetRetriever(self.embedding_dim, self.n_neighbors)
        
        # Create a mock embedder function
        def mock_embedder(data):
            # Simply return a random embedding of the correct shape
            if isinstance(data, torch.Tensor):
                data = data.numpy()
            if len(data.shape) == 1:
                data = data.reshape(1, -1)
            return np.random.random((data.shape[0], self.embedding_dim)).astype('float32')
        
        self.mock_embedder = mock_embedder
        self.retriever.set_embedder(self.mock_embedder)
        
        # Create a temporary directory for saving/loading tests
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Remove temporary directory
        shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """Test initialization of BarDatasetRetriever."""
        self.assertEqual(self.retriever.embedding_dim, self.embedding_dim)
        self.assertEqual(self.retriever.n_neighbors, self.n_neighbors)
        self.assertEqual(self.retriever.metric, 'l2')
        self.assertIsNone(self.retriever.index)
        self.assertIsNone(self.retriever.embeddings)
        self.assertEqual(self.retriever.metadata, [])
        self.assertIsNotNone(self.retriever.embedder)
    
    def test_create_index(self):
        """Test creating a FAISS index."""
        # Test L2 index
        self.retriever._create_index()
        self.assertIsNotNone(self.retriever.index)
        
        # Test IP index
        self.retriever.metric = 'ip'
        self.retriever._create_index()
        self.assertIsNotNone(self.retriever.index)
        
        # Test invalid metric
        self.retriever.metric = 'invalid'
        with self.assertRaises(ValueError):
            self.retriever._create_index()
    
    def test_build_index_with_mock_dataset(self):
        """Test building an index with a mock dataset."""
        # Create a mock dataset
        mock_dataset = MagicMock()
        mock_dataset.__len__.return_value = 10
        
        # Mock the __getitem__ method to return data and target
        def getitem(idx):
            data = np.random.random((1, 100)).astype('float32')
            target = np.random.random((1,)).astype('float32')
            return data, target
        
        mock_dataset.__getitem__.side_effect = getitem
        
        # Build index
        self.retriever.build_index(mock_dataset)
        
        # Check that the index was built
        self.assertIsNotNone(self.retriever.index)
        self.assertIsNotNone(self.retriever.embeddings)
        self.assertEqual(len(self.retriever.metadata), 10)
        
        # Check that the embeddings have the correct shape
        self.assertEqual(self.retriever.embeddings.shape, (10, self.embedding_dim))
    
    def test_add_to_index(self):
        """Test adding data to an existing index."""
        # First build an index
        mock_dataset = MagicMock()
        mock_dataset.__len__.return_value = 5
        
        def getitem(idx):
            data = np.random.random((1, 100)).astype('float32')
            target = np.random.random((1,)).astype('float32')
            return data, target
        
        mock_dataset.__getitem__.side_effect = getitem
        
        self.retriever.build_index(mock_dataset)
        
        # Add new data
        new_data = np.random.random((1, 100)).astype('float32')
        new_metadata = {'test': 'metadata'}
        
        idx = self.retriever.add_to_index(new_data, new_metadata)
        
        # Check that the data was added
        self.assertEqual(idx, 5)  # Should be the 6th item (index 5)
        self.assertEqual(len(self.retriever.metadata), 6)
        self.assertEqual(self.retriever.embeddings.shape, (6, self.embedding_dim))
        self.assertEqual(self.retriever.metadata[5]['test'], 'metadata')
    
    def test_update_index(self):
        """Test updating data in an existing index."""
        # First build an index
        mock_dataset = MagicMock()
        mock_dataset.__len__.return_value = 5
        
        def getitem(idx):
            data = np.random.random((1, 100)).astype('float32')
            target = np.random.random((1,)).astype('float32')
            return data, target
        
        mock_dataset.__getitem__.side_effect = getitem
        
        self.retriever.build_index(mock_dataset)
        
        # Update data
        updated_data = np.random.random((1, 100)).astype('float32')
        updated_metadata = {'updated': True}
        
        self.retriever.update_index(0, updated_data, updated_metadata)
        
        # Check that the data was updated
        self.assertEqual(len(self.retriever.metadata), 5)  # Should still have 5 items
        self.assertEqual(self.retriever.metadata[0]['updated'], True)
    
    def test_retrieve(self):
        """Test retrieving similar data points."""
        # First build an index
        mock_dataset = MagicMock()
        mock_dataset.__len__.return_value = 10
        
        def getitem(idx):
            data = np.random.random((1, 100)).astype('float32')
            target = np.random.random((1,)).astype('float32')
            return data, target
        
        mock_dataset.__getitem__.side_effect = getitem
        
        self.retriever.build_index(mock_dataset)
        
        # Retrieve similar data points
        query_data = np.random.random((1, 100)).astype('float32')
        
        distances, indices, metadata = self.retriever.retrieve(query_data)
        
        # Check the results
        self.assertEqual(distances.shape, (1, self.n_neighbors))
        self.assertEqual(indices.shape, (1, self.n_neighbors))
        self.assertEqual(len(metadata), self.n_neighbors)
    
    def test_save_and_load(self):
        """Test saving and loading the index."""
        # First build an index
        mock_dataset = MagicMock()
        mock_dataset.__len__.return_value = 5
        
        def getitem(idx):
            data = np.random.random((1, 100)).astype('float32')
            target = np.random.random((1,)).astype('float32')
            return data, target
        
        mock_dataset.__getitem__.side_effect = getitem
        
        self.retriever.build_index(mock_dataset)
        
        # Save the index
        index_path = self.retriever.save(self.temp_dir)
        
        # Check that the files were created
        self.assertTrue(os.path.exists(index_path))
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "bar_dataset_retriever_embeddings.npy")))
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "bar_dataset_retriever_metadata.npy")))
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "bar_dataset_retriever_config.npy")))
        
        # Load the index
        new_retriever = BarDatasetRetriever(self.embedding_dim)
        new_retriever.load(self.temp_dir)
        
        # Check that the index was loaded correctly
        self.assertEqual(new_retriever.embedding_dim, self.embedding_dim)
        self.assertEqual(new_retriever.n_neighbors, self.n_neighbors)
        self.assertEqual(new_retriever.metric, 'l2')
        self.assertIsNotNone(new_retriever.index)
        self.assertIsNotNone(new_retriever.embeddings)
        self.assertEqual(len(new_retriever.metadata), 5)
    
    def test_factory_function(self):
        """Test the factory function for creating a retriever."""
        # Create a retriever using the factory function
        with patch('bar_dataset_rag.create_embedder_from_model') as mock_create_embedder:
            # Mock the create_embedder_from_model function
            mock_create_embedder.return_value = self.mock_embedder
            
            retriever = create_bar_dataset_retriever(
                embedding_dim=self.embedding_dim,
                embedder_type='rnn',
                n_neighbors=self.n_neighbors,
                metric='l2'
            )
            
            # Check that the retriever was created correctly
            self.assertEqual(retriever.embedding_dim, self.embedding_dim)
            self.assertEqual(retriever.n_neighbors, self.n_neighbors)
            self.assertEqual(retriever.metric, 'l2')
            self.assertIsNone(retriever.index)
            self.assertIsNone(retriever.embeddings)
            self.assertEqual(retriever.metadata, [])
            self.assertIsNotNone(retriever.embedder)
            
            # Check that the create_embedder_from_model function was called
            mock_create_embedder.assert_called_once()


if __name__ == '__main__':
    unittest.main()
