#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试自选股获取功能
"""

from db.db_factory import DBFactory
from utils.logger import logger

def get_all_favorite_tickers():
    """
    从数据库获取所有用户的自选股列表

    返回:
        list: 所有用户自选股的股票代码列表
    """
    try:
        # 获取数据库客户端
        db_client = DBFactory.get_db_client()

        # 使用fetch_query方法执行查询
        query = "SELECT DISTINCT symbol FROM user_favorites"
        results, _ = db_client.fetch_query(query)

        # 提取股票代码
        tickers = [row[0] for row in results]

        logger.info(f"从数据库获取到 {len(tickers)} 个自选股代码")
        return tickers
    except Exception as e:
        logger.error(f"获取自选股列表失败: {e}")
        return []
    finally:
        if 'db_client' in locals():
            db_client.close()

def get_all_favorite_ticker_names():
    """
    从数据库获取所有用户的自选股列表，包含代码和名称

    返回:
        list: 所有用户自选股的股票代码和名称列表，格式为[(symbol, name), ...]
    """
    try:
        # 获取数据库客户端
        db_client = DBFactory.get_db_client()

        # 使用fetch_query方法执行查询
        query = "SELECT DISTINCT symbol, name FROM user_favorites"
        results, _ = db_client.fetch_query(query)

        # 提取股票代码和名称
        ticker_names = [(row[0], row[1]) for row in results]

        logger.info(f"从数据库获取到 {len(ticker_names)} 个自选股代码和名称")
        return ticker_names
    except Exception as e:
        logger.error(f"获取自选股代码和名称失败: {e}")
        return []
    finally:
        if 'db_client' in locals():
            db_client.close()

def simulate_news_search_terms():
    """
    模拟生成新闻搜索关键词
    """
    # 配置中的股票代码
    config_tickers = ["AAPL", "MSFT", "GOOG", "AMZN", "TSLA", "NVDA"]

    # 获取用户自选股代码和名称
    favorite_ticker_names = get_all_favorite_ticker_names()

    # 为新闻抽取准备搜索关键词
    # 对于配置中的股票，直接使用代码
    news_search_terms = config_tickers.copy()

    # 对于自选股，使用名称进行搜索
    for symbol, name in favorite_ticker_names:
        # 如果是中国股票或期货（包含点号），使用名称进行搜索
        if '.' in symbol:
            news_search_terms.append(f"{name}")
        else:
            # 如果是美股，使用代码进行搜索
            news_search_terms.append(symbol)

    return news_search_terms

if __name__ == "__main__":
    # 测试获取自选股列表
    favorites = get_all_favorite_tickers()
    print(f"自选股列表: {favorites}")

    # 测试获取自选股代码和名称
    favorite_names = get_all_favorite_ticker_names()
    print(f"自选股代码和名称: {favorite_names}")

    # 测试生成新闻搜索关键词
    news_terms = simulate_news_search_terms()
    print(f"新闻搜索关键词: {news_terms}")
