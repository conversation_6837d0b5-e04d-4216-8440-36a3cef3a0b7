#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自选股数据迁移脚本

将localStorage中的自选股数据迁移到数据库中
"""

import argparse
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).resolve().parent.parent))

from rest_api.models import get_session, User, UserFavorite
from utils.logger import logger

def migrate_favorites(json_file):
    """
    从JSON文件迁移自选股数据到数据库
    
    Args:
        json_file: JSON文件路径，包含用户名和自选股数据
    """
    try:
        # 读取JSON文件
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 获取数据库会话
        db = get_session()
        
        # 统计结果
        result = {
            "total_users": 0,
            "total_favorites": 0,
            "success_users": 0,
            "failed_users": 0,
            "success_favorites": 0,
            "failed_favorites": 0
        }
        
        try:
            # 遍历用户数据
            for username, favorites_data in data.items():
                result["total_users"] += 1
                
                try:
                    # 查询用户
                    user = db.query(User).filter(User.username == username).first()
                    
                    if not user:
                        logger.warning(f"用户 {username} 不存在，跳过")
                        result["failed_users"] += 1
                        continue
                    
                    # 处理股票自选
                    stock_favorites = favorites_data.get("stockFavorites", [])
                    for favorite in stock_favorites:
                        result["total_favorites"] += 1
                        
                        try:
                            # 检查是否已存在
                            existing = db.query(UserFavorite).filter(
                                UserFavorite.user_id == user.id,
                                UserFavorite.symbol == favorite["symbol"]
                            ).first()
                            
                            if existing:
                                logger.info(f"用户 {username} 的自选股 {favorite['symbol']} 已存在，跳过")
                                result["success_favorites"] += 1
                                continue
                            
                            # 创建自选股记录
                            new_favorite = UserFavorite(
                                user_id=user.id,
                                symbol=favorite["symbol"],
                                name=favorite["name"],
                                type="stock"
                            )
                            
                            # 添加到数据库
                            db.add(new_favorite)
                            db.flush()
                            
                            result["success_favorites"] += 1
                            logger.info(f"成功添加用户 {username} 的股票自选 {favorite['symbol']}")
                        except Exception as e:
                            result["failed_favorites"] += 1
                            logger.error(f"添加用户 {username} 的股票自选 {favorite['symbol']} 失败: {e}")
                    
                    # 处理期货自选
                    future_favorites = favorites_data.get("futureFavorites", [])
                    for favorite in future_favorites:
                        result["total_favorites"] += 1
                        
                        try:
                            # 检查是否已存在
                            existing = db.query(UserFavorite).filter(
                                UserFavorite.user_id == user.id,
                                UserFavorite.symbol == favorite["symbol"]
                            ).first()
                            
                            if existing:
                                logger.info(f"用户 {username} 的自选期货 {favorite['symbol']} 已存在，跳过")
                                result["success_favorites"] += 1
                                continue
                            
                            # 创建自选股记录
                            new_favorite = UserFavorite(
                                user_id=user.id,
                                symbol=favorite["symbol"],
                                name=favorite["name"],
                                type="future"
                            )
                            
                            # 添加到数据库
                            db.add(new_favorite)
                            db.flush()
                            
                            result["success_favorites"] += 1
                            logger.info(f"成功添加用户 {username} 的期货自选 {favorite['symbol']}")
                        except Exception as e:
                            result["failed_favorites"] += 1
                            logger.error(f"添加用户 {username} 的期货自选 {favorite['symbol']} 失败: {e}")
                    
                    # 提交事务
                    db.commit()
                    result["success_users"] += 1
                    logger.info(f"成功迁移用户 {username} 的自选股数据")
                except Exception as e:
                    db.rollback()
                    result["failed_users"] += 1
                    logger.error(f"迁移用户 {username} 的自选股数据失败: {e}")
            
            # 打印统计结果
            logger.info("迁移完成，统计结果:")
            logger.info(f"总用户数: {result['total_users']}")
            logger.info(f"成功用户数: {result['success_users']}")
            logger.info(f"失败用户数: {result['failed_users']}")
            logger.info(f"总自选股数: {result['total_favorites']}")
            logger.info(f"成功自选股数: {result['success_favorites']}")
            logger.info(f"失败自选股数: {result['failed_favorites']}")
            
            return result
        finally:
            db.close()
    except Exception as e:
        logger.error(f"迁移自选股数据失败: {e}")
        raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="自选股数据迁移工具")
    parser.add_argument("json_file", help="包含自选股数据的JSON文件路径")
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.json_file):
        logger.error(f"文件不存在: {args.json_file}")
        return 1
    
    try:
        # 执行迁移
        result = migrate_favorites(args.json_file)
        
        # 检查结果
        if result["failed_users"] > 0 or result["failed_favorites"] > 0:
            logger.warning("迁移过程中存在失败项，请检查日志")
            return 2
        
        logger.info("迁移成功完成")
        return 0
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
