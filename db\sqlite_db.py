#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SQLite数据库客户端模块

该模块提供SQLite数据库操作功能，接口与PostgresDBClient保持一致。
"""

import sqlite3
from sqlite3 import Error
from utils.logger import logger
import os

class SQLiteDBClient:
    """SQLite数据库客户端类"""
    
    _instance = None  # 单例实例
    
    def __new__(cls, *args, **kwargs):
        """实现单例模式"""
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, db_path):
        """
        初始化SQLite数据库客户端
        
        Args:
            db_path: SQLite数据库文件路径
        """
        if not hasattr(self, "_initialized"):
            self.db_path = db_path
            self.connection = None
            
            # 确保数据库目录存在
            db_dir = os.path.dirname(db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir)
                
            self._initialized = True
            logger.info(f"SQLite数据库客户端初始化: {db_path}")
    
    def connect(self):
        """建立数据库连接"""
        if not self.connection:
            try:
                self.connection = sqlite3.connect(self.db_path)
                # 启用外键约束
                self.connection.execute("PRAGMA foreign_keys = ON")
                # 配置连接以返回字典形式的结果
                self.connection.row_factory = sqlite3.Row
                logger.info("SQLite数据库连接已建立")
            except Error as e:
                logger.error(f"连接SQLite数据库失败: {e}")
                raise
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("SQLite数据库连接已关闭")
    
    def execute_query(self, query, params=None):
        """
        执行非查询SQL语句（INSERT, UPDATE, DELETE）
        
        Args:
            query: SQL查询语句
            params: 查询参数
        """
        try:
            self.connect()
            cursor = self.connection.cursor()
            cursor.execute(query, params or ())
            self.connection.commit()
            cursor.close()
        except Error as e:
            logger.error(f"执行SQLite查询失败: {e}")
            self.connection.rollback()
            raise
    
    def fetch_query(self, query, params=None):
        """
        执行查询SQL语句并获取结果
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            tuple: (results, columns)，查询结果和列名
        """
        try:
            self.connect()
            cursor = self.connection.cursor()
            cursor.execute(query, params or ())
            results = cursor.fetchall()
            
            # 获取列名
            columns = [description[0] for description in cursor.description] if cursor.description else []
            
            # 将Row对象转换为元组
            results = [tuple(row) for row in results]
            
            cursor.close()
            return results, columns
        except Error as e:
            logger.error(f"执行SQLite查询失败: {e}")
            raise
    
    # CRUD方法
    def create(self, table, data):
        """
        向表中插入一行数据
        
        Args:
            table: 表名
            data: 要插入的数据字典
        """
        try:
            columns = list(data.keys())
            placeholders = ", ".join(["?"] * len(columns))
            column_str = ", ".join(columns)
            values = tuple(data.values())
            
            query = f"INSERT INTO {table} ({column_str}) VALUES ({placeholders})"
            self.execute_query(query, values)
        except Exception as e:
            logger.error(f"SQLite CREATE操作失败: {e}")
            raise
    
    def read(self, table, conditions=None):
        """
        从表中读取数据
        
        Args:
            table: 表名
            conditions: 条件字典
            
        Returns:
            tuple: (results, columns)，查询结果和列名
        """
        try:
            query = f"SELECT * FROM {table}"
            
            if conditions:
                condition_str = " AND ".join([f"{key} = ?" for key in conditions.keys()])
                query += f" WHERE {condition_str}"
                params = tuple(conditions.values())
            else:
                params = None
                
            return self.fetch_query(query, params)
        except Exception as e:
            logger.error(f"SQLite READ操作失败: {e}")
            raise
    
    def update(self, table, data, conditions):
        """
        更新表中的数据
        
        Args:
            table: 表名
            data: 要更新的数据字典
            conditions: 条件字典
        """
        try:
            set_clause = ", ".join([f"{key} = ?" for key in data.keys()])
            condition_clause = " AND ".join([f"{key} = ?" for key in conditions.keys()])
            
            query = f"UPDATE {table} SET {set_clause} WHERE {condition_clause}"
            params = tuple(list(data.values()) + list(conditions.values()))
            
            self.execute_query(query, params)
        except Exception as e:
            logger.error(f"SQLite UPDATE操作失败: {e}")
            raise
    
    def delete(self, table, conditions):
        """
        从表中删除数据
        
        Args:
            table: 表名
            conditions: 条件字典
        """
        try:
            condition_clause = " AND ".join([f"{key} = ?" for key in conditions.keys()])
            query = f"DELETE FROM {table} WHERE {condition_clause}"
            params = tuple(conditions.values())
            
            self.execute_query(query, params)
        except Exception as e:
            logger.error(f"SQLite DELETE操作失败: {e}")
            raise
