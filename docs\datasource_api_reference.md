# DataSource API 参考文档

本文档提供了 `qtunnel` 模块中 `DataSource` 类的详细API参考。`DataSource` 类是获取金融市场数据的主要接口，支持获取股票、期货、指数等多种金融工具的历史数据和实时数据。

## 目录

1. [枚举类型](#枚举类型)
2. [DataSource 类](#datasource-类)
3. [基础信息方法](#基础信息方法)
4. [证券代码和名称方法](#证券代码和名称方法)
5. [数据获取方法](#数据获取方法)
6. [板块和指数方法](#板块和指数方法)
7. [数据提供者管理方法](#数据提供者管理方法)
8. [代码示例](#代码示例)

## 枚举类型

### BarData

定义了K线数据中可获取的字段类型。

| 枚举值 | 描述 |
|-------|------|
| `datetime` | 日期时间 |
| `open` | 开盘价 |
| `high` | 最高价 |
| `low` | 最低价 |
| `close` | 收盘价 |
| `volume` | 成交量 |
| `amount` | 成交额 |
| `typical` | 典型价格 (高+低+收)/3 |
| `preclose` | 前收盘价 |

### BarSize

定义了K线的时间周期。

| 枚举值 | 描述 |
|-------|------|
| `tick` | 逐笔 |
| `min1` | 1分钟 |
| `min5` | 5分钟 |
| `min15` | 15分钟 |
| `min30` | 30分钟 |
| `min60` | 60分钟 |
| `mrange` | 分钟区间 |
| `day` | 日线 |
| `drange` | 日区间 |
| `week` | 周线 |
| `month` | 月线 |
| `quarter` | 季线 |
| `year` | 年线 |

### DoRight

定义了复权方式。

| 枚举值 | 描述 |
|-------|------|
| `none` | 不复权 |
| `forward` | 前复权 |
| `backward` | 后复权 |

### RunMode

定义了数据源的运行模式。

| 枚举值 | 描述 |
|-------|------|
| `active` | 主动模式，从实时行情获取数据 |
| `passive` | 被动模式，从本地通达信数据获取数据 |
| `simulation` | 模拟模式，用于回测 |

## DataSource 类

`DataSource` 类是获取金融市场数据的主要接口。

### 构造函数

```python
DataSource(run_mode)
```

**参数:**
- `run_mode` (RunMode): 数据源的运行模式

**示例:**
```python
from qtunnel import DataSource, RunMode

# 初始化DataSource，使用被动模式
ds = DataSource(RunMode.passive)
```

## 基础信息方法

### get_run_dir

获取运行目录。

```python
get_run_dir()
```

**返回:**
- 运行目录路径

**示例:**
```python
run_dir = ds.get_run_dir()
print(f"运行目录: {run_dir}")
```

### get_sec_num

获取证券数量。

```python
get_sec_num()
```

**返回:**
- 证券数量

**示例:**
```python
sec_num = ds.get_sec_num()
print(f"证券数量: {sec_num}")
```

### get_trading_dates

获取交易日期。

```python
get_trading_dates(start_date, end_date)
```

**参数:**
- `start_date` (str): 开始日期，格式为 'YYYY-MM-DD'
- `end_date` (str): 结束日期，格式为 'YYYY-MM-DD'

**返回:**
- 交易日期列表

**示例:**
```python
trading_dates = ds.get_trading_dates("2023-01-01", "2023-01-31")
print(f"交易日期: {trading_dates}")
```

## 证券代码和名称方法

### get_sec_name

获取证券名称。

```python
get_sec_name(code)
```

**参数:**
- `code` (str): 证券代码

**返回:**
- 证券名称

**示例:**
```python
stock_name = ds.get_sec_name("600000.SH")
print(f"股票名称: {stock_name}")
```

### get_all_fut_names

获取所有期货名称。

```python
get_all_fut_names()
```

**返回:**
- 期货名称列表

**示例:**
```python
fut_names = ds.get_all_fut_names()
print(f"期货名称: {fut_names}")
```

### get_all_fut_codes

获取所有期货代码。

```python
get_all_fut_codes()
```

**返回:**
- 期货代码列表

**示例:**
```python
fut_codes = ds.get_all_fut_codes()
print(f"期货代码: {fut_codes}")
```

### get_fut_name_by_code

根据代码获取期货名称。

```python
get_fut_name_by_code(code)
```

**参数:**
- `code` (str): 期货代码

**返回:**
- 期货名称

**示例:**
```python
fut_name = ds.get_fut_name_by_code("IF9999.SF")
print(f"期货名称: {fut_name}")
```

### get_fut_code_by_name

根据名称获取期货代码。

```python
get_fut_code_by_name(name)
```

**参数:**
- `name` (str): 期货名称

**返回:**
- 期货代码

**示例:**
```python
fut_code = ds.get_fut_code_by_name("沪深300")
print(f"期货代码: {fut_code}")
```

### get_fut_lx_label

获取期货类型标签。

```python
get_fut_lx_label()
```

**返回:**
- 期货类型标签

**示例:**
```python
fut_lx_label = ds.get_fut_lx_label()
print(f"期货类型标签: {fut_lx_label}")
```

## 数据获取方法

### price

获取最新价格。

```python
price(code)
```

**参数:**
- `code` (str): 证券代码

**返回:**
- 最新价格

**示例:**
```python
price = ds.price("600000.SH")
print(f"最新价格: {price}")
```

### get_history_data

获取历史数据（按数量）。

```python
get_history_data(code, length, bar_data, bar_size=BarSize.day, do_right=DoRight.forward)
```

**参数:**
- `code` (str): 证券代码
- `length` (int): 获取的数据条数，0表示获取所有数据
- `bar_data` (list): 需要获取的数据字段列表，如 [BarData.datetime, BarData.close]
- `bar_size` (BarSize, 可选): K线周期，默认为日线
- `do_right` (DoRight, 可选): 复权方式，默认为前复权

**返回:**
- 历史数据列表

**示例:**
```python
bar_data = [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close, BarData.volume]
hist_data = ds.get_history_data("600000.SH", 10, bar_data, BarSize.day, DoRight.forward)
print(f"历史数据: {hist_data}")
```

### get_history_data2

获取历史数据（按日期范围）。

```python
get_history_data2(code, begin_date, end_date, bar_data, bar_size=BarSize.day, do_right=DoRight.forward)
```

**参数:**
- `code` (str): 证券代码
- `begin_date` (str): 开始日期，格式为 'YYYY-MM-DD'
- `end_date` (str): 结束日期，格式为 'YYYY-MM-DD'
- `bar_data` (list): 需要获取的数据字段列表，如 [BarData.datetime, BarData.close]
- `bar_size` (BarSize, 可选): K线周期，默认为日线
- `do_right` (DoRight, 可选): 复权方式，默认为前复权

**返回:**
- 历史数据列表

**示例:**
```python
bar_data = [BarData.datetime, BarData.close]
hist_data = ds.get_history_data2("600000.SH", "2023-01-01", "2023-01-31", bar_data, BarSize.day, DoRight.forward)
print(f"历史数据: {hist_data}")
```

### get_bar_series

获取K线序列。

```python
get_bar_series(code, bar_data, bar_size=BarSize.day)
```

**参数:**
- `code` (str): 证券代码
- `bar_data` (list): 需要获取的数据字段列表，如 [BarData.close]
- `bar_size` (BarSize, 可选): K线周期，默认为日线

**返回:**
- K线序列

**示例:**
```python
bar_series = ds.get_bar_series("600000.SH", [BarData.close], BarSize.day)
print(f"K线序列: {bar_series}")
```

### get_rangebar_atr

获取区间K线ATR。

```python
get_rangebar_atr(code, bar_size)
```

**参数:**
- `code` (str): 证券代码
- `bar_size` (BarSize): K线周期

**返回:**
- 区间K线ATR值

**示例:**
```python
rangebar_atr = ds.get_rangebar_atr("600000.SH", BarSize.day)
print(f"区间K线ATR: {rangebar_atr}")
```

### get_default_rangebar_atr

获取默认区间K线ATR。

```python
get_default_rangebar_atr(code, bar_size, is_last=True)
```

**参数:**
- `code` (str): 证券代码
- `bar_size` (BarSize): K线周期
- `is_last` (bool, 可选): 是否获取最新值，默认为True

**返回:**
- 默认区间K线ATR值

**示例:**
```python
default_atr = ds.get_default_rangebar_atr("600000.SH", BarSize.day, True)
print(f"默认区间K线ATR: {default_atr}")
```

### get_tick_data

获取逐笔数据。

```python
get_tick_data(code, day_num)
```

**参数:**
- `code` (str): 证券代码
- `day_num` (int): 获取的天数

**返回:**
- 逐笔数据列表

**示例:**
```python
tick_data = ds.get_tick_data("600000.SH", 1)
print(f"逐笔数据: {tick_data}")
```

## 板块和指数方法

### get_block_data

获取板块数据。

```python
get_block_data(block_name)
```

**参数:**
- `block_name` (str): 板块名称，如 "沪深300"、"中证500"、"ZLLX"（期货主力合约）

**返回:**
- 板块成分股列表

**示例:**
```python
block_data = ds.get_block_data("沪深300")
print(f"板块数据: {block_data}")
```

## 数据提供者管理方法

### start_providers

启动数据提供者。

```python
start_providers()
```

**示例:**
```python
ds.start_providers()
print("数据提供者已启动")
```

### stop_providers

停止数据提供者。

```python
stop_providers()
```

**示例:**
```python
ds.stop_providers()
print("数据提供者已停止")
```

## 代码索引方法

### get_stk_code_index

获取股票代码索引。

```python
get_stk_code_index(code)
```

**参数:**
- `code` (str): 股票代码

**返回:**
- 股票代码索引

**示例:**
```python
stk_index = ds.get_stk_code_index("600000.SH")
print(f"股票代码索引: {stk_index}")
```

### get_fut_code_index

获取期货代码索引。

```python
get_fut_code_index(code)
```

**参数:**
- `code` (str): 期货代码

**返回:**
- 期货代码索引

**示例:**
```python
fut_index = ds.get_fut_code_index("IF9999.SF")
print(f"期货代码索引: {fut_index}")
```

## 代码示例

### 基本用法

```python
from qtunnel import DataSource, BarData, BarSize, DoRight, RunMode
import pandas as pd

# 初始化DataSource
ds = DataSource(RunMode.passive)

# 获取股票历史数据
stock_code = "600000.SH"
bar_data = [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close, BarData.volume]
hist_data = ds.get_history_data(stock_code, 10, bar_data, BarSize.day, DoRight.forward)

# 转换为DataFrame
df = pd.DataFrame(hist_data, columns=["datetime", "open", "high", "low", "close", "volume"])
print(df)

# 获取板块数据
block_data = ds.get_block_data("沪深300")
print(f"沪深300成分股: {block_data[:5]}")

# 停止数据提供者
ds.stop_providers()
```

### 获取不同周期的数据

```python
from qtunnel import DataSource, BarData, BarSize, DoRight, RunMode
import pandas as pd

# 初始化DataSource
ds = DataSource(RunMode.passive)

# 获取股票代码
stock_code = "600000.SH"
bar_data = [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close, BarData.volume]

# 获取日K线
day_data = ds.get_history_data(stock_code, 10, bar_data, BarSize.day, DoRight.forward)
df_day = pd.DataFrame(day_data, columns=["datetime", "open", "high", "low", "close", "volume"])
print("日K线数据:")
print(df_day)

# 获取周K线
week_data = ds.get_history_data(stock_code, 10, bar_data, BarSize.week, DoRight.forward)
df_week = pd.DataFrame(week_data, columns=["datetime", "open", "high", "low", "close", "volume"])
print("\n周K线数据:")
print(df_week)

# 获取5分钟K线
min5_data = ds.get_history_data(stock_code, 10, bar_data, BarSize.min5, DoRight.forward)
df_min5 = pd.DataFrame(min5_data, columns=["datetime", "open", "high", "low", "close", "volume"])
print("\n5分钟K线数据:")
print(df_min5)

# 停止数据提供者
ds.stop_providers()
```

### 使用不同的复权方式

```python
from qtunnel import DataSource, BarData, BarSize, DoRight, RunMode
import pandas as pd

# 初始化DataSource
ds = DataSource(RunMode.passive)

# 获取股票代码
stock_code = "600000.SH"
bar_data = [BarData.datetime, BarData.close]

# 不复权
no_right_data = ds.get_history_data(stock_code, 10, bar_data, BarSize.day, DoRight.none)
df_no_right = pd.DataFrame(no_right_data, columns=["datetime", "close"])
print("不复权数据:")
print(df_no_right)

# 前复权
forward_data = ds.get_history_data(stock_code, 10, bar_data, BarSize.day, DoRight.forward)
df_forward = pd.DataFrame(forward_data, columns=["datetime", "close"])
print("\n前复权数据:")
print(df_forward)

# 后复权
backward_data = ds.get_history_data(stock_code, 10, bar_data, BarSize.day, DoRight.backward)
df_backward = pd.DataFrame(backward_data, columns=["datetime", "close"])
print("\n后复权数据:")
print(df_backward)

# 停止数据提供者
ds.stop_providers()
```
