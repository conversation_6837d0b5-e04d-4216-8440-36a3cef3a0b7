"""
测试model_routes模块的单元测试
"""

import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from unittest.mock import MagicMock

# 导入需要测试的模块
from rest_api.models.api_models import PredictionInput, PredictionResponse

# 创建测试客户端
from fastapi import FastAPI, HTTPException

# 创建模拟的app
app = FastAPI()

# 模拟 ModelService
mock_service = MagicMock()

# 导入API版本管理模块
from rest_api.core.api_version import API_V1

# 版本化的API路径
API_PATH = {
    API_V1: {
        "predict": "predict",
        "models": "models",
        "model_info": "models/{model_name}/info"
    }
}

# 模拟路由
@app.post(f"/api/{API_V1}/model/{API_PATH[API_V1]['predict']}")
async def predict_v1(input_data: PredictionInput):
    return mock_service.predict(
        model_name=input_data.model_name,
        ticker=input_data.ticker,
        candlestick_data=input_data.candlestick_data,
        temperature=input_data.temperature,
        top_k=input_data.top_k,
        pre_close=input_data.pre_close,
        atr=input_data.atr,
        atr_mult=input_data.atr_mult,
        scale=input_data.scale,
        forecast_steps=input_data.forecast_steps
    )

@app.get(f"/api/{API_V1}/model/{API_PATH[API_V1]['models']}")
async def list_models_v1():
    return {"available_models": list(mock_service.model_paths.keys())}

@app.get(f"/api/{API_V1}/model/{API_PATH[API_V1]['model_info']}")
async def get_model_info_v1(model_name: str):
    if model_name not in mock_service.model_paths:
        raise HTTPException(status_code=404, detail=f"模型 {model_name} 不存在")

    # 如果模型未加载，先加载模型
    if model_name not in mock_service.models:
        mock_service.load_model(model_name)

    model_info = mock_service.models[model_name]
    session = model_info['session']

    # 获取输入和输出信息
    inputs = []
    for input_meta in session.get_inputs():
        inputs.append({
            "name": input_meta.name,
            "shape": input_meta.shape,
            "type": str(input_meta.type)
        })

    outputs = []
    for output_meta in session.get_outputs():
        outputs.append({
            "name": output_meta.name,
            "shape": output_meta.shape,
            "type": str(output_meta.type)
        })

    return {
        "model_name": model_name,
        "model_path": mock_service.model_paths[model_name],
        "inputs": inputs,
        "outputs": outputs
    }

# 为了向后兼容，添加当前版本的别名
predict = predict_v1
list_models = list_models_v1
get_model_info = get_model_info_v1

@app.get(f"/api/{API_V1}/model")
async def model_api_info():
    """获取模型API信息"""
    return {
        "name": "模型预测API",
        "version": API_V1,
        "endpoints": {
            "predict": f"/api/{API_V1}/model/{API_PATH[API_V1]['predict']}",
            "models": f"/api/{API_V1}/model/{API_PATH[API_V1]['models']}",
            "model_info": f"/api/{API_V1}/model/{API_PATH[API_V1]['model_info'].replace('{model_name}', '<model_name>')}"
        },
        "available_versions": [API_V1]
    }

# 创建测试客户端
client = TestClient(app)


class TestModelRoutes:
    """测试model_routes模块的API端点"""

    def setup_method(self):
        """每个测试方法执行前的设置"""
        # 重置模拟对象
        mock_service.reset_mock()

        # 设置模拟的model_paths
        mock_service.model_paths = {
            "GPT4_v1": "./saved_models/model1.onnx",
            "GPT4_v2": "./saved_models/model2.onnx"
        }

        # 设置模拟的models
        mock_service.models = {
            "GPT4_v1": {
                "session": MagicMock()
            }
        }

        # 配置模拟的session
        mock_session = mock_service.models["GPT4_v1"]["session"]
        mock_input_meta = MagicMock()
        mock_input_meta.name = "input.1"
        mock_input_meta.shape = [1, 30]
        mock_input_meta.type = "tensor(float)"

        mock_output_meta = MagicMock()
        mock_output_meta.name = "output.1"
        mock_output_meta.shape = [1, 1]
        mock_output_meta.type = "tensor(float)"

        mock_session.get_inputs.return_value = [mock_input_meta]
        mock_session.get_outputs.return_value = [mock_output_meta]

        # 配置模拟的predict方法
        mock_service.predict.return_value = PredictionResponse(
            timestamp=datetime.now().isoformat(),
            candlesticks=[
                {
                    "datetime": (datetime.now() + timedelta(minutes=5)).isoformat(),
                    "open": 3530.0,
                    "high": 3560.0,
                    "low": 3520.0,
                    "close": 3550.0,
                    "volume": 12000.0
                }
            ]
        )

    def test_api_info(self):
        """测试获取API信息接口"""
        # 发送请求
        response = client.get(f"/api/{API_V1}/model")

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "模型预测API"
        assert data["version"] == API_V1
        assert "endpoints" in data
        assert "available_versions" in data

    def test_list_models(self):
        """测试获取模型列表接口"""
        # 发送请求
        response = client.get(f"/api/{API_V1}/model/{API_PATH[API_V1]['models']}")

        # 验证响应
        assert response.status_code == 200
        assert response.json() == {"available_models": ["GPT4_v1", "GPT4_v2"]}

    def test_get_model_info_success(self):
        """测试获取模型信息接口 - 成功情况"""
        # 发送请求
        model_info_path = API_PATH[API_V1]['model_info'].replace('{model_name}', 'GPT4_v1')
        response = client.get(f"/api/{API_V1}/model/{model_info_path}")

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["model_name"] == "GPT4_v1"
        assert data["model_path"] == "./saved_models/model1.onnx"
        assert len(data["inputs"]) == 1
        assert len(data["outputs"]) == 1

    def test_get_model_info_not_found(self):
        """测试获取模型信息接口 - 模型不存在"""
        # 发送请求
        model_info_path = API_PATH[API_V1]['model_info'].replace('{model_name}', 'NonExistentModel')
        response = client.get(f"/api/{API_V1}/model/{model_info_path}")

        # 验证响应
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]

    def test_predict(self):
        """测试预测接口"""
        # 准备测试数据
        now = datetime.now()
        test_data = {
            "model_name": "GPT4_v1",
            "ticker": "IF",
            "candlestick_data": [
                {
                    "datetime": (now - timedelta(minutes=10)).isoformat(),
                    "open": 3500.0,
                    "high": 3550.0,
                    "low": 3480.0,
                    "close": 3520.0,
                    "volume": 10000.0
                },
                {
                    "datetime": (now - timedelta(minutes=5)).isoformat(),
                    "open": 3520.0,
                    "high": 3580.0,
                    "low": 3510.0,
                    "close": 3560.0,
                    "volume": 12000.0
                }
            ],
            "pre_close": 3500.0,
            "atr": 50.0,
            "temperature": 0.1,
            "top_k": 1,
            "atr_mult": 0.88,
            "scale": 10,
            "forecast_steps": 1
        }

        # 发送请求
        response = client.post(f"/api/{API_V1}/model/{API_PATH[API_V1]['predict']}", json=test_data)

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "timestamp" in data
        assert "candlesticks" in data
        assert len(data["candlesticks"]) == 1

        # 验证模拟对象被正确调用
        mock_service.predict.assert_called_once()
        call_args = mock_service.predict.call_args[1]
        assert call_args["model_name"] == "GPT4_v1"
        assert call_args["ticker"] == "IF"
        assert len(call_args["candlestick_data"]) == 2
        assert call_args["pre_close"] == 3500.0
        assert call_args["atr"] == 50.0
        assert call_args["forecast_steps"] == 1

    def test_predict_with_multiple_forecasts(self):
        """测试预测接口 - 多步预测"""
        # 准备测试数据
        now = datetime.now()
        test_data = {
            "model_name": "GPT4_v1",
            "ticker": "IF",
            "candlestick_data": [
                {
                    "datetime": (now - timedelta(minutes=10)).isoformat(),
                    "open": 3500.0,
                    "high": 3550.0,
                    "low": 3480.0,
                    "close": 3520.0,
                    "volume": 10000.0
                },
                {
                    "datetime": (now - timedelta(minutes=5)).isoformat(),
                    "open": 3520.0,
                    "high": 3580.0,
                    "low": 3510.0,
                    "close": 3560.0,
                    "volume": 12000.0
                }
            ],
            "pre_close": 3500.0,
            "atr": 50.0,
            "temperature": 0.1,
            "top_k": 1,
            "atr_mult": 0.88,
            "scale": 10,
            "forecast_steps": 5
        }

        # 配置模拟的predict方法返回多步预测结果
        mock_service.predict.return_value = PredictionResponse(
            timestamp=datetime.now().isoformat(),
            candlesticks=[
                {
                    "datetime": (now + timedelta(minutes=i*5)).isoformat(),
                    "open": 3530.0 + i*10,
                    "high": 3560.0 + i*10,
                    "low": 3520.0 + i*10,
                    "close": 3550.0 + i*10,
                    "volume": 12000.0
                } for i in range(5)
            ]
        )

        # 发送请求
        response = client.post(f"/api/{API_V1}/model/{API_PATH[API_V1]['predict']}", json=test_data)

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "timestamp" in data
        assert "candlesticks" in data
        assert len(data["candlesticks"]) == 5

        # 验证模拟对象被正确调用
        mock_service.predict.assert_called_once()
        call_args = mock_service.predict.call_args[1]
        assert call_args["forecast_steps"] == 5


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
