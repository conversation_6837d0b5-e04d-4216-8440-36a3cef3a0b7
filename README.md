   <a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="Creative Commons License" style="border-width:0" src="https://i.creativecommons.org/l/by-nc-sa/4.0/88x31.png" /></a><br />This work is licensed under a <a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/">Creative Commons Attribution-NonCommercial-ShareAlike 4.0 International License</a>.


# 股票数据洞察应用

本项目展示了使用代理增强生成（RAG）工作流从特定公司和更广泛的股票市场的新闻和财务数据中提取见解的方法。它利用了大型语言模型（LLMs），ChromaDB作为向量数据库，LangChain，LangChain表达语言（LCEL）和LangGraph，以提供全面的分析。

## 特征

- **股票表现可视化**: 展示选定股票的历史表现图表和图表。
- **特定属性数据检索**: 检索特定股票特定属性的详细信息。
- **新闻聚合**: 提供与特定股票或公司相关的通用新闻或特定主题文章。
- **Web应用界面**: 提供用户友好的界面来访问和可视化股票数据和相关新闻。
- **用户认证系统**: 支持用户注册、登录和个人资料管理。

## 高层次架构
![高层次设计](documentation/high_level_design.png)

## 方法

### 异步爬取

1. **新闻数据**: 异步爬取预定义股票的新闻数据，定期存储在MongoDB中。将信息同步到ChromaDB，使LLMs能够执行语义搜索，从而检索与特定股票或公司相关的特定信息。
2. **财务数据**: 异步爬取选定股票的财务数据，在开发环境中存储在SQLite数据库中，在生产环境中存储在PostgreSQL数据库中。

### 数据库配置

本项目支持两种数据库配置：

1. **开发环境**: 使用SQLite数据库，轻量级，无需额外服务，适合开发和测试。
2. **生产环境**: 使用PostgreSQL数据库，提供更好的性能和可扩展性，适合生产部署。

数据库类型可以通过环境变量或配置文件进行配置，详见`.env.example`文件。

### LangGraph 工作流

#### 新闻数据 RAG 图
一个代理RAG图，用于搜索新闻数据，无论是从向量数据库（同步的MongoDB文档）还是执行网络搜索（如果相关文档未找到）。

![News RAG Graph](images/news-rag-graph.png)

该图包括以下节点：

- **从DB中检索新闻 (`retrieve_news`)**：利用LLMs、LangChain和检索器工具在向量数据库中对特定股票主题相关的文档进行语义搜索。
- **评分文档 (`grade_documents`)**：对前一步骤中检索到的文档进行质量评估，分配分数以确定它们的相关性。条件边缘决定是否生成结果或如果文档不相关，则进行额外的网络搜索。
- **网络搜索 (`web_search`)**：使用与LangChain和LLM调用集成的TavilySearch工具进行网络搜索。
- **生成结果 (`generate_results`)**：根据用户查询和前一步骤中检索到的文档生成结果。

#### 股票数据RAG图

![股票数据RAG图](images/stock-data-rag-graph.png)

一个代理RAG图，用于在SQL数据库（PostgreSQL）中搜索特定股票的财务数据。

该图包括以下节点：

- **生成SQL (`generate_sql`)**：使用LLMs和LangChain根据用户输入生成SQL查询。
- **执行SQL (`execute_sql`)**：执行前一步骤中生成的SQL查询，以从数据库中获取数据。
- **生成结果 (`generate_results`)**：利用LLMs根据用户查询和前一步骤中获取的数据生成结果。

#### 股票数据图表RAG图

![股票数据图表RAG图](images/stock-charts-rag-graph.png)

一个代理RAG图，用于从SQL数据库（PostgreSQL）中检索特定股票的财务数据，并生成视觉图表。

该图包括以下节点：

- **生成SQL (`generate_sql`)**：使用LLMs和LangChain根据用户输入创建SQL查询。
- **执行SQL (`execute_sql`)**：运行前一步骤中生成的SQL查询，以从数据库中获取数据。

## API
详细的API规范，请参阅附加的`openapi.json`文件。

### 价格统计（GET `/stock/{ticker}/price-stats`）

获取特定股票的股票价格统计。

参数：
    ticker (str): 股票代码符号。
    operation (str): 要执行的操作（例如，'最高', '最低', '平均'）。
    price_type (str): 价格类型（例如，'开盘', '收盘', '低', '高'）。
    duration (int): 天数

返回：
    dict: 包含请求统计的股票数据。

#### 参数：
- `ticker`: str - 股票代码符号
- `operation`: str - 要执行的操作: '最高', '最低', '平均'
- `price_type`: str - 价格类型: '开盘', '收盘', '低', '高'
- `duration`: str - 持续时间（天）: '1', '7', '14', '30'

### 图表（GET `/stock/{ticker}/chart`）

获取特定股票的股票价格统计，并返回特定股票的直方图/图表。

参数：
    ticker (str): 股票代码符号。
    price_type (str): 价格类型（例如，'开盘', '收盘', '低', '高'）。
    duration (int): 天数

返回：
    dict: 包含请求统计的股票数据。

#### 参数：
- `ticker`: str - 股票代码符号
- `price_type`: str - 价格类型: '开盘', '收盘', '低', '高'
- `duration`: str - 持续时间（天）: '1', '7', '14', '30'

### 特定主题新闻（GET `/news/{ticker}`）

获取特定股票的特定主题新闻。

参数：
    ticker (str): 股票代码符号。
    topic (str): 要获取新闻的特定主题。

返回：
    dict: 特定股票的相关新闻。

#### 参数：
- `ticker`: str - 股票代码符号
- `topic`: str - 主题

### 根目录（GET `/`）

应用程序的根目录/首页

#### 参数：
无参数

## 类图

![类图](images/classes_stock_proj.png)
## 图像

对于视觉表示，请参阅`images/`目录中的图像。

## 测试框架
项目使用pytest框架进行自动化测试。这确保了所有模块都经过了彻底的测试，以维护应用程序的可靠性和健壮性。测试设置的关键特点包括：

全面的测试用例：为每个模块编写了测试用例，确保应用程序的完整覆盖。
易于使用：只需运行以下命令来执行所有测试：
```bash
pytest
```
测试报告：框架为每次测试运行生成详细的报告，突出成功和失败。
这测试设置确保了应用程序在添加新功能或更新现有功能时保持稳定和功能。

## 可观察性和追踪
为了监控应用程序的性能和调试LLM相关过程，项目集成了LangSmith追踪。这使得可以详细追踪所有LLM调用，提供了对应用程序执行流的见解。

关键特点：
LLM调用追踪：跟踪所有与大型语言模型的交互，包括输入、输出和执行时间。
调试助手：帮助识别LLM工作流中的瓶颈或错误。
LangSmith仪表盘：提供了一个用户友好的界面来可视化和分析追踪。

如何工作：
LangSmith追踪无缝集成到应用程序中。所有RAG工作流，包括新闻RAG图、股票数据RAG图和股票数据图表RAG图，都利用LangSmith提供了有价值的可观察性见解。

## Web应用

项目包含一个完整的Web应用界面，提供了用户友好的方式来访问和可视化股票数据和相关新闻。

### 功能特点

- **股票数据可视化**：展示股票价格走势图表，支持不同时间周期（日、周、月、季、年）
- **股票详情展示**：显示股票的关键指标和财务数据
- **新闻聚合**：展示与特定股票相关的新闻，支持按主题筛选
- **高级图表**：使用lightweight-charts库实现日内走势图和历史走势图
- **技术指标分析**：提供移动平均线、RSI、MACD、布林带等技术指标
- **扩展技术指标**：提供成交量、OBV、ATR、ADX等高级技术指标
- **股票比较**：支持多只股票的历史表现对比
- **股票搜索自动完成**：提供智能的股票代码和名称搜索功能
- **用户认证**：支持用户注册和登录功能
- **个人资料管理**：用户可以更新个人信息
- **API令牌管理**：用户可以生成和撤销API访问令牌
- **股票收藏**：用户可以收藏和管理自己感兴趣的股票

### 运行Web应用

```bash
cd web_app
python app.py
```

在浏览器中访问：`http://localhost:8080`

更多详细信息，请参阅 [Web应用README](web_app/README.md)。

## 参考

- **LangGraph**: 一个用于构建有状态的多actor应用程序的库，使用LLMs，促进了代理和多代理工作流的创建。
- **LangChain表达语言（LCEL）**: 一个声明式的方法来组合链，允许复杂工作流的无缝集成和优化。
- **FastAPI**: 一个现代、快速的Web框架，用于构建API。
- **Chart.js**: 一个简单而灵活的JavaScript图表库。

本项目展示了如何将先进的AI工作流集成到一起，以提供对财务和新闻数据的有见解的分析，向用户提供了一个全面的工具来评估股票市场。
