from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from pathlib import Path
import api_routes

# 创建FastAPI应用
app = FastAPI(title="智能体投顾助手")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源，实际生产环境应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 获取当前文件所在目录
BASE_DIR = Path(__file__).resolve().parent

# 挂载静态文件
app.mount("/static", StaticFiles(directory=str(BASE_DIR / "static")), name="static")

# 设置模板目录
templates = Jinja2Templates(directory=str(BASE_DIR / "templates"))

# 主页路由
@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

# 用户页面路由
@app.get("/profile", response_class=HTMLResponse)
async def profile(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

# 股票详情页面路由
@app.get("/stock/{ticker}", response_class=HTMLResponse)
async def stock_detail(request: Request, ticker: str):
    return templates.TemplateResponse("index.html", {"request": request})

# 模型页面路由
@app.get("/model", response_class=HTMLResponse)
async def model(request: Request):
    return templates.TemplateResponse("model.html", {"request": request})

# 时序检索页面路由
@app.get("/tsrag", response_class=HTMLResponse)
async def tsrag(request: Request):
    return templates.TemplateResponse("tsrag.html", {"request": request})

# 添加API路由
app.include_router(api_routes.router, prefix="/api")

# 启动应用
if __name__ == "__main__":
    uvicorn.run("app:app", host="0.0.0.0", port=8080, reload=True)
