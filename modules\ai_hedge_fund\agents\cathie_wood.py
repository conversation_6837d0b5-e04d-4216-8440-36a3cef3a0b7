"""
<PERSON><PERSON><PERSON> agent for the AI Hedge Fund module.

This agent analyzes stocks using <PERSON><PERSON><PERSON>'s growth-focused investment principles.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_financial_metrics, get_market_cap, search_line_items
from ..utils.llm import call_llm


class CathieWoodSignal(BaseModel):
    """Signal generated by the Cathie Wood agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")


def cathie_wood_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using <PERSON><PERSON><PERSON> Wood's principles and LLM reasoning.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with <PERSON><PERSON><PERSON>'s analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    cathie_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("cathie_wood_agent", ticker, "Fetching financial metrics")
        
        # Fetch required data
        metrics = get_financial_metrics(ticker, end_date, period="ttm", limit=5)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("cathie_wood_agent", ticker, "Gathering financial line items")
        
        financial_line_items = search_line_items(
            ticker,
            [
                "revenue",
                "net_income",
                "research_and_development",
                "capital_expenditure",
                "cash_and_cash_equivalents",
                "total_assets",
                "total_liabilities",
            ],
            end_date,
        )
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("cathie_wood_agent", ticker, "Getting market cap")
        
        # Get current market cap
        market_cap = get_market_cap(ticker, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("cathie_wood_agent", ticker, "Analyzing growth potential")
        
        # Analyze growth potential
        growth_analysis = analyze_growth_potential(metrics, financial_line_items)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("cathie_wood_agent", ticker, "Analyzing innovation")
        
        innovation_analysis = analyze_innovation(financial_line_items)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("cathie_wood_agent", ticker, "Analyzing disruption potential")
        
        disruption_analysis = analyze_disruption_potential(metrics)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("cathie_wood_agent", ticker, "Analyzing financial health")
        
        financial_health_analysis = analyze_financial_health(metrics, financial_line_items)
        
        # Calculate total score
        total_score = (
            growth_analysis["score"] + 
            innovation_analysis["score"] + 
            disruption_analysis["score"] + 
            financial_health_analysis["score"]
        )
        
        max_possible_score = (
            growth_analysis["max_score"] + 
            innovation_analysis["max_score"] + 
            disruption_analysis["max_score"] + 
            financial_health_analysis["max_score"]
        )
        
        # Generate trading signal based on score
        if total_score >= 0.7 * max_possible_score:
            signal = "bullish"
        elif total_score <= 0.3 * max_possible_score:
            signal = "bearish"
        else:
            signal = "neutral"
        
        # Combine all analysis results
        analysis_data[ticker] = {
            "signal": signal,
            "score": total_score,
            "max_score": max_possible_score,
            "growth_analysis": growth_analysis,
            "innovation_analysis": innovation_analysis,
            "disruption_analysis": disruption_analysis,
            "financial_health_analysis": financial_health_analysis,
            "market_cap": market_cap,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("cathie_wood_agent", ticker, "Generating Cathie Wood analysis")
        
        # Generate detailed analysis using LLM
        cathie_output = generate_cathie_wood_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        cathie_analysis[ticker] = {
            "signal": cathie_output.signal,
            "confidence": cathie_output.confidence,
            "reasoning": cathie_output.reasoning,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("cathie_wood_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(cathie_analysis, "Cathie Wood Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["cathie_wood_agent"] = cathie_analysis
    
    return state


def analyze_growth_potential(metrics: List[Any], financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze growth potential based on revenue and earnings growth.
    
    Args:
        metrics: List of financial metrics
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with growth potential analysis results
    """
    if not metrics or not financial_line_items:
        return {"score": 0, "max_score": 5, "details": "Insufficient data for growth analysis"}
    
    score = 0
    max_score = 5
    reasoning = []
    
    # Check revenue growth
    revenue_values = []
    for item in financial_line_items:
        if hasattr(item, "revenue") and item.revenue is not None:
            revenue_values.append(item.revenue)
    
    if len(revenue_values) >= 2:
        # Calculate revenue growth rate
        revenue_growth_rate = (revenue_values[0] - revenue_values[-1]) / abs(revenue_values[-1]) if revenue_values[-1] != 0 else 0
        
        # Annualize growth rate (assuming quarterly data)
        annualized_growth_rate = (1 + revenue_growth_rate) ** (4 / len(revenue_values)) - 1
        
        if annualized_growth_rate >= 0.3:  # 30% or higher annual growth
            score += 2
            reasoning.append(f"Exceptional revenue growth rate of {annualized_growth_rate:.1%} annually")
        elif annualized_growth_rate >= 0.15:  # 15-30% annual growth
            score += 1
            reasoning.append(f"Strong revenue growth rate of {annualized_growth_rate:.1%} annually")
        else:
            reasoning.append(f"Moderate revenue growth rate of {annualized_growth_rate:.1%} annually")
    else:
        reasoning.append("Insufficient revenue data for growth analysis")
    
    # Check earnings growth
    earnings_values = []
    for item in financial_line_items:
        if hasattr(item, "net_income") and item.net_income is not None:
            earnings_values.append(item.net_income)
    
    if len(earnings_values) >= 2:
        # Calculate earnings growth rate
        earnings_growth_rate = (earnings_values[0] - earnings_values[-1]) / abs(earnings_values[-1]) if earnings_values[-1] != 0 else 0
        
        # Annualize growth rate (assuming quarterly data)
        annualized_earnings_growth = (1 + earnings_growth_rate) ** (4 / len(earnings_values)) - 1
        
        if annualized_earnings_growth >= 0.3:  # 30% or higher annual growth
            score += 2
            reasoning.append(f"Exceptional earnings growth rate of {annualized_earnings_growth:.1%} annually")
        elif annualized_earnings_growth >= 0.15:  # 15-30% annual growth
            score += 1
            reasoning.append(f"Strong earnings growth rate of {annualized_earnings_growth:.1%} annually")
        else:
            reasoning.append(f"Moderate earnings growth rate of {annualized_earnings_growth:.1%} annually")
    else:
        reasoning.append("Insufficient earnings data for growth analysis")
    
    # Check forward-looking growth potential (using PE ratio as a proxy)
    if metrics and hasattr(metrics[0], "pe_ratio") and metrics[0].pe_ratio is not None:
        pe_ratio = metrics[0].pe_ratio
        if pe_ratio > 50:  # High PE ratio suggests high growth expectations
            score += 1
            reasoning.append(f"High PE ratio of {pe_ratio:.1f} suggests strong growth expectations")
        elif pe_ratio > 25:  # Moderate PE ratio
            score += 0.5
            reasoning.append(f"Moderate PE ratio of {pe_ratio:.1f} suggests reasonable growth expectations")
        else:
            reasoning.append(f"Low PE ratio of {pe_ratio:.1f} suggests limited growth expectations")
    else:
        reasoning.append("PE ratio data not available")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_innovation(financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze innovation based on R&D spending.
    
    Args:
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with innovation analysis results
    """
    if not financial_line_items:
        return {"score": 0, "max_score": 3, "details": "Insufficient data for innovation analysis"}
    
    score = 0
    max_score = 3
    reasoning = []
    
    # Check R&D spending
    rd_values = []
    revenue_values = []
    
    for item in financial_line_items:
        if hasattr(item, "research_and_development") and item.research_and_development is not None:
            rd_values.append(item.research_and_development)
        if hasattr(item, "revenue") and item.revenue is not None:
            revenue_values.append(item.revenue)
    
    if len(rd_values) > 0 and len(revenue_values) > 0:
        # Calculate R&D as percentage of revenue
        rd_percentage = abs(rd_values[0]) / revenue_values[0] if revenue_values[0] != 0 else 0
        
        if rd_percentage >= 0.15:  # 15% or more of revenue on R&D
            score += 2
            reasoning.append(f"Exceptional R&D investment at {rd_percentage:.1%} of revenue")
        elif rd_percentage >= 0.08:  # 8-15% of revenue on R&D
            score += 1
            reasoning.append(f"Strong R&D investment at {rd_percentage:.1%} of revenue")
        else:
            reasoning.append(f"Moderate R&D investment at {rd_percentage:.1%} of revenue")
        
        # Check R&D growth
        if len(rd_values) >= 2:
            rd_growth = (abs(rd_values[0]) - abs(rd_values[-1])) / abs(rd_values[-1]) if abs(rd_values[-1]) != 0 else 0
            
            if rd_growth >= 0.2:  # 20% or higher R&D growth
                score += 1
                reasoning.append(f"Increasing focus on innovation with {rd_growth:.1%} R&D growth")
            else:
                reasoning.append(f"Stable R&D investment with {rd_growth:.1%} growth")
        else:
            reasoning.append("Insufficient historical R&D data")
    else:
        reasoning.append("R&D or revenue data not available")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_disruption_potential(metrics: List[Any]) -> Dict[str, Any]:
    """
    Analyze disruption potential based on margins and valuation.
    
    Args:
        metrics: List of financial metrics
        
    Returns:
        Dictionary with disruption potential analysis results
    """
    if not metrics:
        return {"score": 0, "max_score": 3, "details": "Insufficient data for disruption analysis"}
    
    score = 0
    max_score = 3
    reasoning = []
    
    # Check gross margin (high margins suggest pricing power and disruption potential)
    if hasattr(metrics[0], "gross_margin") and metrics[0].gross_margin is not None:
        gross_margin = metrics[0].gross_margin
        
        if gross_margin >= 0.6:  # 60% or higher gross margin
            score += 1
            reasoning.append(f"Exceptional gross margin of {gross_margin:.1%} suggests strong pricing power")
        elif gross_margin >= 0.4:  # 40-60% gross margin
            score += 0.5
            reasoning.append(f"Strong gross margin of {gross_margin:.1%} suggests good pricing power")
        else:
            reasoning.append(f"Moderate gross margin of {gross_margin:.1%}")
    else:
        reasoning.append("Gross margin data not available")
    
    # Check price-to-sales ratio (high P/S can indicate disruptive potential)
    if hasattr(metrics[0], "price_to_sales") and metrics[0].price_to_sales is not None:
        ps_ratio = metrics[0].price_to_sales
        
        if ps_ratio >= 15:  # Very high P/S ratio
            score += 1
            reasoning.append(f"Exceptional price-to-sales ratio of {ps_ratio:.1f} suggests high disruption potential")
        elif ps_ratio >= 8:  # High P/S ratio
            score += 0.5
            reasoning.append(f"Strong price-to-sales ratio of {ps_ratio:.1f} suggests good disruption potential")
        else:
            reasoning.append(f"Moderate price-to-sales ratio of {ps_ratio:.1f}")
    else:
        reasoning.append("Price-to-sales ratio data not available")
    
    # Check operating margin trend (improving margins suggest scaling potential)
    operating_margins = []
    for metric in metrics:
        if hasattr(metric, "operating_margin") and metric.operating_margin is not None:
            operating_margins.append(metric.operating_margin)
    
    if len(operating_margins) >= 2:
        margin_trend = operating_margins[0] - operating_margins[-1]
        
        if margin_trend > 0.05:  # Significant improvement in operating margin
            score += 1
            reasoning.append(f"Improving operating margins by {margin_trend:.1%} suggests scaling potential")
        elif margin_trend > 0:  # Modest improvement
            score += 0.5
            reasoning.append(f"Slightly improving operating margins by {margin_trend:.1%}")
        else:
            reasoning.append(f"Declining operating margins by {margin_trend:.1%}")
    else:
        reasoning.append("Insufficient operating margin data for trend analysis")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_financial_health(metrics: List[Any], financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze financial health based on cash position and debt levels.
    
    Args:
        metrics: List of financial metrics
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with financial health analysis results
    """
    if not metrics or not financial_line_items:
        return {"score": 0, "max_score": 4, "details": "Insufficient data for financial health analysis"}
    
    score = 0
    max_score = 4
    reasoning = []
    
    # Check cash position
    cash_value = None
    total_assets_value = None
    
    for item in financial_line_items:
        if hasattr(item, "cash_and_cash_equivalents") and item.cash_and_cash_equivalents is not None:
            cash_value = item.cash_and_cash_equivalents
        if hasattr(item, "total_assets") and item.total_assets is not None:
            total_assets_value = item.total_assets
    
    if cash_value is not None and total_assets_value is not None:
        # Calculate cash as percentage of total assets
        cash_percentage = cash_value / total_assets_value if total_assets_value != 0 else 0
        
        if cash_percentage >= 0.25:  # 25% or more of assets in cash
            score += 2
            reasoning.append(f"Strong cash position at {cash_percentage:.1%} of total assets")
        elif cash_percentage >= 0.15:  # 15-25% of assets in cash
            score += 1
            reasoning.append(f"Good cash position at {cash_percentage:.1%} of total assets")
        else:
            reasoning.append(f"Moderate cash position at {cash_percentage:.1%} of total assets")
    else:
        reasoning.append("Cash or total assets data not available")
    
    # Check debt levels
    if hasattr(metrics[0], "debt_to_equity") and metrics[0].debt_to_equity is not None:
        debt_to_equity = metrics[0].debt_to_equity
        
        if debt_to_equity <= 0.3:  # Very low debt
            score += 2
            reasoning.append(f"Minimal debt with debt-to-equity ratio of {debt_to_equity:.1f}")
        elif debt_to_equity <= 0.6:  # Low to moderate debt
            score += 1
            reasoning.append(f"Manageable debt with debt-to-equity ratio of {debt_to_equity:.1f}")
        else:
            reasoning.append(f"High debt with debt-to-equity ratio of {debt_to_equity:.1f}")
    else:
        reasoning.append("Debt-to-equity ratio data not available")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def generate_cathie_wood_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> CathieWoodSignal:
    """
    Get investment decision from LLM with Cathie Wood's principles.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        CathieWoodSignal object with the investment decision
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Cathie Wood AI agent. Decide on investment signals based on Cathie Wood's principles:
    - Disruptive Innovation: Focus on companies driving technological breakthroughs
    - Exponential Growth: Seek companies with potential for exponential rather than linear growth
    - Platform Businesses: Prefer companies building platforms that can scale across industries
    - Long-term Horizon: Invest with a 5+ year time horizon, ignoring short-term volatility
    - Convergence of Technologies: Look for companies benefiting from multiple innovation platforms
    - Research-Driven: Base decisions on deep research into technological trends and capabilities

    When providing your reasoning, be thorough and specific by:
    1. Explaining the key factors that influenced your decision the most (both positive and negative)
    2. Highlighting how the company aligns with or violates specific Cathie Wood principles
    3. Providing quantitative evidence where relevant (e.g., growth rates, R&D investment)
    4. Concluding with a Cathie Wood-style assessment of the investment opportunity
    5. Using Cathie Wood's voice and conversational style in your explanation

    For example, if bullish: "This company is at the convergence of [specific technologies], with exceptional growth in [key metric]. Their R&D investment of [X%] shows commitment to innovation..."
    For example, if bearish: "Despite operating in [innovative sector], they lack the exponential growth characteristics we look for, with only [X%] revenue growth and minimal investment in future technologies..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following data, create the investment signal as Cathie Wood would:

    Analysis Data for {ticker}:
    {json.dumps(analysis_data, indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string"
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_cathie_wood_signal():
        ticker_data = analysis_data.get(ticker, {})
        signal = ticker_data.get("signal", "neutral")
        score = ticker_data.get("score", 0)
        max_score = ticker_data.get("max_score", 1)
        confidence = (score / max_score * 100) if max_score > 0 else 50.0
        
        return CathieWoodSignal(
            signal=signal,
            confidence=confidence,
            reasoning="Analysis based on quantitative metrics only. Unable to generate detailed reasoning."
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=CathieWoodSignal,
        agent_name="cathie_wood_agent",
        default_factory=create_default_cathie_wood_signal,
    )
