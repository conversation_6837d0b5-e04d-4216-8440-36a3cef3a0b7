// 全局变量
let isLoggedIn = false;
let currentUser = null;
let accessToken = null;

// API基础URL
const API_BASE_URL = 'http://localhost:8080/api';
const API_VERSION = 'v1';

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化事件监听器
    initEventListeners();

    // 检查登录状态
    checkLoginStatus();
});

// 初始化事件监听器
function initEventListeners() {
    // 登录按钮点击事件
    document.getElementById('loginBtn').addEventListener('click', function() {
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    });

    // 注册按钮点击事件
    document.getElementById('registerBtn').addEventListener('click', function() {
        const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
        registerModal.show();
    });

    // 登录表单提交事件
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;

        login(username, password);
    });

    // 生成随机用户名按钮点击事件
    document.getElementById('generateUsernameBtn').addEventListener('click', function() {
        generateRandomUsername();
    });

    // 注册表单提交事件
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('registerUsername').value;
        const email = document.getElementById('registerEmail').value;
        const fullName = document.getElementById('registerFullName').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('registerConfirmPassword').value;
        const registerError = document.getElementById('registerError');
        const registerErrorMessage = document.getElementById('registerErrorMessage');

        // 隐藏之前的错误信息
        registerError.style.display = 'none';

        // 验证用户名不能为空
        if (!username.trim()) {
            registerError.style.display = 'block';
            registerErrorMessage.textContent = '用户名不能为空';
            return;
        }

        // 验证密码长度
        if (password.length < 6) {
            registerError.style.display = 'block';
            registerErrorMessage.textContent = '密码至少需要 6 位字符';
            return;
        }

        // 验证两次密码是否一致
        if (password !== confirmPassword) {
            registerError.style.display = 'block';
            registerErrorMessage.textContent = '两次输入的密码不一致';
            return;
        }

        register(username, email, fullName, password);
    });

    // 刷新用户列表按钮点击事件
    document.getElementById('refreshUserList').addEventListener('click', function() {
        fetchUserList();
    });

    // 用户操作表单提交事件
    document.getElementById('userActionForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('actionUsername').value;
        const isAdmin = document.getElementById('userAdminStatus').checked;
        const isDisabled = document.getElementById('userDisabledStatus').checked;

        updateUserStatus(username, isAdmin, isDisabled);
    });

    // 系统设置表单提交事件
    document.getElementById('systemSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();

        // 这里可以添加保存系统设置的逻辑
        alert('系统设置已保存');
    });

    // 刷新日志按钮点击事件
    document.getElementById('refreshLogs').addEventListener('click', function() {
        refreshLogs();
    });

    // 日志级别下拉菜单变化事件
    document.getElementById('logLevel').addEventListener('change', function() {
        refreshLogs();
    });

    // 活动类型下拉菜单变化事件
    document.getElementById('activityType').addEventListener('change', function() {
        refreshLogs();
    });

    // 开始日期变化事件
    document.getElementById('startDate').addEventListener('change', function() {
        refreshLogs();
    });

    // 结束日期变化事件
    document.getElementById('endDate').addEventListener('change', function() {
        refreshLogs();
    });

    // 刷新日志函数
    function refreshLogs() {
        const logLevel = document.getElementById('logLevel').value;
        const activityType = document.getElementById('activityType').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        fetchSystemLogs(logLevel, activityType, startDate, endDate);
    }
}

// 检查登录状态
function checkLoginStatus() {
    // 从本地存储中获取登录信息
    const storedToken = localStorage.getItem('accessToken');
    const storedUser = localStorage.getItem('user');

    if (storedToken && storedUser) {
        try {
            accessToken = storedToken;
            currentUser = JSON.parse(storedUser);
            isLoggedIn = true;

            // 更新UI显示已登录状态
            updateLoginUI();

            // 检查是否有管理员权限
            checkAdminPermission();
        } catch (e) {
            console.error('解析存储的用户信息失败:', e);
            // 清除无效的存储数据
            localStorage.removeItem('user');
            localStorage.removeItem('accessToken');

            // 显示未登录提示
            document.getElementById('loginAlert').style.display = 'block';
        }
    } else {
        // 显示未登录提示
        document.getElementById('loginAlert').style.display = 'block';
    }
}

// 更新登录UI
function updateLoginUI() {
    const loginBtn = document.getElementById('loginBtn');
    const registerBtn = document.getElementById('registerBtn');

    if (isLoggedIn) {
        // 创建用户下拉菜单
        const userDropdownContainer = document.createElement('div');
        userDropdownContainer.className = 'dropdown';

        // 创建下拉菜单按钮
        userDropdownContainer.innerHTML = `
            <div class="d-flex align-items-center user-dropdown" data-bs-toggle="dropdown" aria-expanded="false" role="button">
                <div class="user-avatar">${currentUser.username.charAt(0).toUpperCase()}</div>
                <span class="ms-2">${currentUser.username}</span>
                <i class="bi bi-chevron-down ms-1"></i>
            </div>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="/profile"><i class="bi bi-person me-2"></i>个人资料</a></li>
                <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>账号设置</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
            </ul>
        `;

        // 替换登录和注册按钮
        loginBtn.parentNode.replaceChild(userDropdownContainer, loginBtn);
        registerBtn.style.display = 'none';

        // 添加退出登录按钮事件
        setTimeout(() => {
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('确定要退出登录吗？')) {
                        logout();
                    }
                });
            }
        }, 100);
    } else {
        // 恢复登录和注册按钮
        loginBtn.style.display = '';
        registerBtn.style.display = '';
    }
}

// 检查管理员权限
async function checkAdminPermission() {
    if (!isLoggedIn || !accessToken) {
        document.getElementById('loginAlert').style.display = 'block';
        document.getElementById('adminConsole').style.display = 'none';
        return;
    }

    try {
        // 获取用户信息
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/me`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取用户信息失败: ${response.status}`);
        }

        const userData = await response.json();

        // 检查是否有管理员权限
        if (userData.data && userData.data.is_admin) {
            // 显示管理员控制台
            document.getElementById('loginAlert').style.display = 'none';
            document.getElementById('adminAlert').style.display = 'none';
            document.getElementById('adminConsole').style.display = 'block';

            // 加载用户列表
            fetchUserList();

            // 加载活动类型
            fetchActivityTypes();

            // 加载系统日志
            fetchSystemLogs('all', 'all', null, null);
        } else {
            // 显示无权限提示
            document.getElementById('loginAlert').style.display = 'none';
            document.getElementById('adminAlert').style.display = 'block';
            document.getElementById('adminConsole').style.display = 'none';
        }
    } catch (error) {
        console.error('检查管理员权限失败:', error);
        document.getElementById('loginAlert').style.display = 'none';
        document.getElementById('adminAlert').style.display = 'block';
        document.getElementById('adminConsole').style.display = 'none';
    }
}

// 获取用户列表
async function fetchUserList() {
    if (!isLoggedIn || !accessToken) {
        return;
    }

    try {
        // 显示加载状态
        document.getElementById('userTableBody').innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                    <span class="ms-2">加载中...</span>
                </td>
            </tr>
        `;

        // 调用API获取用户列表
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/admin/users`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取用户列表失败: ${response.status}`);
        }

        const data = await response.json();

        if (data.data && data.data.users) {
            // 清空表格
            document.getElementById('userTableBody').innerHTML = '';

            // 填充用户数据
            data.data.users.forEach(user => {
                const row = document.createElement('tr');

                // 设置状态和权限标签样式
                const statusClass = user.disabled ? 'danger' : 'success';
                const statusText = user.disabled ? '已禁用' : '正常';

                const roleClass = user.is_admin ? 'primary' : 'secondary';
                const roleText = user.is_admin ? '管理员' : '普通用户';

                // 模拟注册时间和最后登录时间（实际应用中应从后端获取）
                const registerTime = '2025-01-15';
                const lastLoginTime = '2025-11-20';

                row.innerHTML = `
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td>${user.full_name || '-'}</td>
                    <td>${registerTime}</td>
                    <td>${lastLoginTime}</td>
                    <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                    <td><span class="badge bg-${roleClass}">${roleText}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary edit-user" data-username="${user.username}">
                            <i class="bi bi-pencil-square"></i>
                        </button>
                    </td>
                `;

                document.getElementById('userTableBody').appendChild(row);
            });

            // 添加编辑用户按钮事件
            document.querySelectorAll('.edit-user').forEach(button => {
                button.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    openUserActionModal(username, data.data.users);
                });
            });
        } else {
            document.getElementById('userTableBody').innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">没有找到用户数据</td>
                </tr>
            `;
        }
    } catch (error) {
        console.error('获取用户列表失败:', error);
        document.getElementById('userTableBody').innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-danger">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    获取用户列表失败: ${error.message}
                </td>
            </tr>
        `;
    }
}

// 打开用户操作模态框
async function openUserActionModal(username, users) {
    // 查找用户数据
    const user = users.find(u => u.username === username);

    if (!user) {
        alert('未找到用户数据');
        return;
    }

    // 填充基本信息模块
    document.getElementById('actionUsername').value = user.username;
    document.getElementById('displayUsername').textContent = user.username;
    document.getElementById('displayEmail').textContent = user.email || '-';
    document.getElementById('displayFullName').textContent = user.full_name || '-';
    document.getElementById('userAdminStatus').checked = user.is_admin;
    document.getElementById('userDisabledStatus').checked = user.disabled;

    // 填充模拟数据（在实际应用中应从后端获取）
    document.getElementById('displayRegisterTime').textContent = '模拟数据: 2025-01-15 10:30:45';
    document.getElementById('displayLastLogin').textContent = '模拟数据: 2025-11-20 15:22:18';
    document.getElementById('displayLastIP').textContent = '模拟数据: *************';
    document.getElementById('displayPasswordChangeTime').textContent = '模拟数据: 2023-10-05 09:15:30';
    document.getElementById('displayTokenCount').textContent = '模拟数据: 2';

    // 填充活动记录模块
    const activityTableBody = document.getElementById('userActivityTableBody');
    activityTableBody.innerHTML = `
        <tr>
            <td colspan="4" class="text-center">
                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                <span class="ms-2">加载中...</span>
            </td>
        </tr>
    `;

    // 获取用户活动日志
    try {
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/logs/user-activities?username=${user.username}&page=1&page_size=10`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取用户活动日志失败: ${response.status}`);
        }

        const data = await response.json();
        activityTableBody.innerHTML = '';

        if (data.data && data.data.logs && data.data.logs.length > 0) {
            data.data.logs.forEach(activity => {
                const row = document.createElement('tr');

                // 设置活动类型样式
                let typeClass = 'text-primary';
                if (activity.activity_type.includes('FAILED')) {
                    typeClass = 'text-danger';
                } else if (activity.activity_type.includes('CHANGE') || activity.activity_type.includes('RESET')) {
                    typeClass = 'text-warning';
                }

                row.innerHTML = `
                    <td>${activity.timestamp}</td>
                    <td><span class="${typeClass}">${activity.activity_type}</span></td>
                    <td>${activity.ip_address || '-'}</td>
                    <td>${activity.activity_detail || '-'}</td>
                `;

                activityTableBody.appendChild(row);
            });
        } else {
            activityTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-muted">没有找到活动记录</td>
                </tr>
            `;
        }
    } catch (error) {
        console.error('获取用户活动日志失败:', error);
        activityTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-danger">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    获取活动记录失败: ${error.message}
                </td>
            </tr>
        `;
    }

    // 填充投资组合模块
    const portfolioTableBody = document.getElementById('userPortfolioTableBody');
    portfolioTableBody.innerHTML = '';

    // 模拟投资组合数据
    const portfolios = [
        { name: '成长型组合', createTime: '2023-05-15', value: '125,000.00', pnl: '+15.2%' },
        { name: '价值型组合', createTime: '2023-08-22', value: '85,500.00', pnl: '****%' },
        { name: '稳健型组合', createTime: '2023-10-10', value: '50,200.00', pnl: '****%' }
    ];

    portfolios.forEach(portfolio => {
        const row = document.createElement('tr');

        // 设置盈亏率样式
        const pnlClass = portfolio.pnl.startsWith('+') ? 'text-success' : 'text-danger';

        row.innerHTML = `
            <td>${portfolio.name}</td>
            <td>${portfolio.createTime}</td>
            <td>$${portfolio.value}</td>
            <td class="${pnlClass}">${portfolio.pnl}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary view-portfolio" data-portfolio="${portfolio.name}">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;

        portfolioTableBody.appendChild(row);
    });

    // 添加按钮事件
    document.getElementById('resetPasswordBtn').addEventListener('click', function() {
        if (confirm(`确定要重置用户 ${user.username} 的密码吗？`)) {
            alert('密码重置功能尚未实现，这只是一个演示。');
        }
    });

    document.getElementById('manageTokensBtn').addEventListener('click', function() {
        alert('API令牌管理功能尚未实现，这只是一个演示。');
    });

    document.querySelectorAll('.view-portfolio').forEach(button => {
        button.addEventListener('click', function() {
            const portfolioName = this.getAttribute('data-portfolio');
            alert(`查看投资组合功能尚未实现，这只是一个演示。选中的组合: ${portfolioName}`);
        });
    });

    // 打开模态框
    const userActionModal = new bootstrap.Modal(document.getElementById('userActionModal'));
    userActionModal.show();
}

// 更新用户状态
async function updateUserStatus(username, isAdmin, isDisabled) {
    if (!isLoggedIn || !accessToken) {
        return;
    }

    try {
        // 更新管理员权限
        const permissionResponse = await fetch(`${API_BASE_URL}/${API_VERSION}/user/admin/set-permission`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                username: username,
                is_admin: isAdmin
            })
        });

        if (!permissionResponse.ok) {
            throw new Error(`更新管理员权限失败: ${permissionResponse.status}`);
        }

        // 更新用户状态
        const statusResponse = await fetch(`${API_BASE_URL}/${API_VERSION}/user/admin/set-user-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                username: username,
                disabled: isDisabled
            })
        });

        if (!statusResponse.ok) {
            throw new Error(`更新用户状态失败: ${statusResponse.status}`);
        }

        // 关闭模态框
        const userActionModal = bootstrap.Modal.getInstance(document.getElementById('userActionModal'));
        userActionModal.hide();

        // 刷新用户列表
        fetchUserList();

        // 显示成功消息
        alert('用户信息更新成功');
    } catch (error) {
        console.error('更新用户状态失败:', error);
        alert(`更新用户状态失败: ${error.message}`);
    }
}

// 获取活动类型
async function fetchActivityTypes() {
    if (!isLoggedIn || !accessToken) {
        return;
    }

    try {
        // 调用API获取活动类型
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/logs/activity-types`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取活动类型失败: ${response.status}`);
        }

        const data = await response.json();

        if (data.data && data.data.activity_types) {
            const activityTypeSelect = document.getElementById('activityType');

            // 清空现有选项，保留第一个“所有类型”选项
            while (activityTypeSelect.options.length > 1) {
                activityTypeSelect.remove(1);
            }

            // 添加新选项
            data.data.activity_types.forEach(type => {
                const option = document.createElement('option');
                option.value = type.code;
                option.textContent = type.name;
                activityTypeSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('获取活动类型失败:', error);
    }
}

// 获取系统日志
async function fetchSystemLogs(level, activityType, startDate, endDate) {
    if (!isLoggedIn || !accessToken) {
        return;
    }

    const logContent = document.getElementById('logContent');

    // 显示加载状态
    logContent.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm text-light" role="status"></div> 加载中...</div>';

    try {
        // 构建查询参数
        let queryParams = new URLSearchParams();

        // 添加筛选条件
        if (level !== 'all') {
            queryParams.append('activity_level', level);
        }

        if (activityType && activityType !== 'all') {
            queryParams.append('activity_type', activityType);
        }

        // 添加日期范围筛选
        if (startDate) {
            queryParams.append('start_date', startDate);
        }

        if (endDate) {
            queryParams.append('end_date', endDate);
        }

        // 设置页码和每页数量
        queryParams.append('page', '1');
        queryParams.append('page_size', '50');

        // 调用API获取用户活动日志
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/logs/user-activities?${queryParams.toString()}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        if (!response.ok) {
            throw new Error(`获取日志失败: ${response.status}`);
        }

        const data = await response.json();

        // 清空日志容器
        logContent.innerHTML = '';

        // 添加日志条目
        if (data.data && data.data.logs && data.data.logs.length > 0) {
            data.data.logs.forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry mb-2';

                // 设置日志级别样式
                let levelClass = '';
                switch (log.activity_level) {
                    case 'HIGH':
                        levelClass = 'text-danger';
                        break;
                    case 'MEDIUM':
                        levelClass = 'text-warning';
                        break;
                    case 'LOW':
                        levelClass = 'text-info';
                        break;
                }

                // 根据活动类型设置图标
                let activityIcon = '';
                switch (log.activity_type) {
                    case 'LOGIN':
                        activityIcon = '<i class="bi bi-box-arrow-in-right me-1"></i>';
                        break;
                    case 'LOGOUT':
                        activityIcon = '<i class="bi bi-box-arrow-left me-1"></i>';
                        break;
                    case 'REGISTER':
                        activityIcon = '<i class="bi bi-person-plus me-1"></i>';
                        break;
                    case 'PWD_CHANGE':
                    case 'PWD_RESET':
                        activityIcon = '<i class="bi bi-key me-1"></i>';
                        break;
                    case 'TOKEN_GEN':
                    case 'TOKEN_REV':
                        activityIcon = '<i class="bi bi-shield-lock me-1"></i>';
                        break;
                    case 'FAILED_LOGIN':
                        activityIcon = '<i class="bi bi-exclamation-triangle me-1"></i>';
                        break;
                    case 'PERM_CHG':
                        activityIcon = '<i class="bi bi-person-gear me-1"></i>';
                        break;
                    default:
                        activityIcon = '<i class="bi bi-activity me-1"></i>';
                }

                logEntry.innerHTML = `
                    <span class="text-muted">[${log.timestamp}]</span>
                    <span class="${levelClass}">[${log.activity_level}]</span>
                    <span>${activityIcon}${log.activity_type}</span>
                    <span class="ms-2">${log.activity_detail || ''}</span>
                    <span class="text-muted ms-2 small">IP: ${log.ip_address || 'N/A'}</span>
                `;

                logContent.appendChild(logEntry);
            });

            // 添加分页信息
            if (data.data.total > data.data.page_size) {
                const paginationInfo = document.createElement('div');
                paginationInfo.className = 'text-center text-muted mt-3';
                paginationInfo.innerHTML = `显示 ${data.data.logs.length} 条记录，共 ${data.data.total} 条`;
                logContent.appendChild(paginationInfo);
            }
        } else {
            logContent.innerHTML = '<div class="text-center text-muted">没有找到匹配的日志</div>';
        }
    } catch (error) {
        console.error('获取日志失败:', error);
        logContent.innerHTML = `
            <div class="text-center text-danger">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                获取日志失败: ${error.message}
            </div>
        `;
    }
}

// 登录
async function login(username, password) {
    // 显示加载状态
    const submitBtn = document.querySelector('#loginForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 登录中...';
    submitBtn.disabled = true;

    try {
        // 调用真实API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                'username': username,
                'password': password
            })
        });

        if (!response.ok) {
            throw new Error(`登录失败: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.access_token) {
            // 获取用户信息
            const userResponse = await fetch(`${API_BASE_URL}/${API_VERSION}/user/me`, {
                headers: {
                    'Authorization': `Bearer ${data.access_token}`
                }
            });

            if (!userResponse.ok) {
                throw new Error(`获取用户信息失败: ${userResponse.status}`);
            }

            const userData = await userResponse.json();

            // 从响应中提取用户数据
            const userInfo = userData.data || {
                username: data.username || username,
                email: data.email || '',
                full_name: data.full_name || '',
                is_admin: data.is_admin || false
            };

            // 保存登录信息
            accessToken = data.access_token;
            currentUser = userInfo;
            isLoggedIn = true;

            // 保存到本地存储
            localStorage.setItem('accessToken', accessToken);
            localStorage.setItem('user', JSON.stringify(currentUser));

            // 更新UI
            updateLoginUI();

            // 检查管理员权限
            checkAdminPermission();

            // 关闭模态框
            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            loginModal.hide();

            // 重置表单
            document.getElementById('loginForm').reset();

            // 显示成功消息
            alert('登录成功！');
        } else {
            throw new Error('登录响应中缺少访问令牌');
        }
    } catch (error) {
        console.error('登录失败:', error);
        alert(`登录失败: ${error.message}`);
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 注册
async function register(username, email, fullName, password) {
    // 显示加载状态
    const submitBtn = document.querySelector('#registerForm button[type="submit"]');
    const originalBtnText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 注册中...';
    submitBtn.disabled = true;

    // 隐藏错误信息
    const registerError = document.getElementById('registerError');
    registerError.style.display = 'none';

    try {
        // 调用真实API
        const response = await fetch(`${API_BASE_URL}/${API_VERSION}/user/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                email: email,
                full_name: fullName,
                password: password  // 密码将在服务器端进行哈希
            })
        });

        // 尝试解析响应数据，即使响应不成功
        let responseData;
        try {
            responseData = await response.json();
        } catch (e) {
            // 如果无法解析JSON，使用默认错误消息
            responseData = { detail: `注册失败: ${response.status}` };
        }

        if (!response.ok) {
            // 如果响应不成功，显示错误信息
            let errorMessage = `注册失败: ${response.status}`;

            // 如果响应中包含详细错误信息，使用详细信息
            if (responseData && responseData.detail) {
                errorMessage = responseData.detail;
            }

            // 处理各种特定错误
            if (errorMessage.includes('用户名已存在')) {
                // 如果是用户名已存在错误，提供生成新用户名的选项
                if (confirm('用户名已存在，是否生成新的用户名？')) {
                    generateRandomUsername();
                    // 显示错误信息，但不抛出异常，允许用户重试
                    registerError.style.display = 'block';
                    document.getElementById('registerErrorMessage').textContent = '已生成新用户名，请重新提交';
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                    return;
                }
            } else if (errorMessage.includes('密码长度不能少于')) {
                // 如果是密码长度错误，直接在表单中显示错误
                registerError.style.display = 'block';
                document.getElementById('registerErrorMessage').textContent = errorMessage;
                // 聚焦到密码输入框
                document.getElementById('registerPassword').focus();
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
                return;
            }

            throw new Error(errorMessage);
        }

        // 关闭模态框
        const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
        registerModal.hide();

        // 重置表单
        document.getElementById('registerForm').reset();

        // 显示成功消息
        alert('注册成功！请登录您的账号。');

        // 打开登录模态框
        const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();
    } catch (error) {
        console.error('注册失败:', error);

        // 显示错误信息
        registerError.style.display = 'block';
        document.getElementById('registerErrorMessage').textContent = error.message;
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
}

// 生成随机用户名
function generateRandomUsername() {
    // 生成一个随机数字字符串
    const randomDigits = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

    // 生成一个随机字母字符串
    const randomChars = Array(4).fill(0).map(() =>
        String.fromCharCode(97 + Math.floor(Math.random() * 26))
    ).join('');

    // 组合成带有指定前缀的用户名
    const username = `HUA-${randomChars}${randomDigits}`;

    // 设置到输入框
    document.getElementById('registerUsername').value = username;

    return username;
}

// 退出登录
function logout() {
    // 清除登录信息
    accessToken = null;
    currentUser = null;
    isLoggedIn = false;

    // 清除本地存储
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');

    // 刷新页面
    location.reload();
}
