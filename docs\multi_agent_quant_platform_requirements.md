# 多智能体协作量化投资平台 - 产品需求文档 (PRD)

## 1. 产品愿景 (Product Vision)

构建一个由多个高度专业化的AI投资专家智能体组成的“数字投研团队”。该团队能够自主协作，进行深度市场研究、生成交易策略、执行交易并进行风险管理，最终为“人类投资组合经理”（即用户）提供强大、透明且可控的决策支持与自动化执行能力。

## 2. 核心原则 (Core Principles)

- **角色扮演 (Role-Playing):** 每个Agent都有明确的专家角色、职责和信息获取范围，模仿真实投研团队的运作模式。
- **协作与通讯 (Collaboration & Communication):** Agent之间通过标准化的“投研报告”或“指令”进行异步通信，形成完整的决策链。
- **人类在环 (Human-in-the-Loop):** 用户作为最终决策者，拥有最高权限，可以审查、否决或批准Agent团队的最终投资建议。
- **可观测与可追溯 (Observability & Traceability):** 所有的决策过程、数据来源、分析逻辑都必须被记录，确保透明度和事后复盘。

## 3. 核心用户故事 (Epics)

- **作为投资经理, 我希望** 能设定一个宏观投资目标（如“在未来一个季度，增持半导体行业的价值型股票”），**以便** 我的AI专家团队能围绕此目标自主展开工作。
- **作为投资经理, 我希望** 能实时看到每个AI专家正在研究什么、得出了什么初步结论，**以便** 我能随时掌握团队的进展和思路。
- **作为投资经理, 我希望** 能收到一份由AI团队协作完成的、包含“投资建议”、“置信度”、“潜在风险”和“支撑论据”的综合投资提案，**以便** 我能进行最终决策。
- **作为投资经理, 我希望** 在我批准提案后，系统能自动完成交易执行、仓位管理和止损设置，**以便** 提高效率并遵守纪律。

## 4. AI专家角色定义 (Agent Roles)

| 专家角色 (Agent Role) | 核心使命 (Mission) | 主要数据源 (Data Sources) | 核心工具/技能 (Tools/Skills) | 产出物 (Deliverable) |
| :--- | :--- | :--- | :--- | :--- |
| **首席经济学家 (Macro Strategist)** | 洞察宏观经济与政策，判断市场周期与风险偏好。 | 央行公告, 经济数据(CPI/PMI), 政策文件, 财经新闻 | `web_fetch`, `news_rag_graph` | 宏观经济展望报告 (牛/熊/震荡), 板块轮动建议 |
| **行业分析师 (Sector Analyst)** | 深入研究特定行业的产业链、竞争格局和未来趋势。 | 行业研报, 上市公司财报, 供应链数据, 另类数据 | `web_fetch`, `stock_data_rag_graph` | 行业评级报告 (增持/中性/减持), 龙头公司列表 |
| **基本面研究员 (Fundamental Analyst)** | 精读个股财报，评估公司内在价值，挖掘护城河。 | 公司年报/季报, 盈利预测, 管理层电话会议 | `stock_data_rag_graph`, SQL生成, 财务比率计算 | 个股深度分析报告 (DCF估值, 质量评分, 风险点) |
| **量化分析师 (Quant Analyst)** | 从海量数据中寻找规律，构建数学模型，生成交易信号。 | 股票行情数据(OHLCV), 因子库, 社交媒体情绪数据 | `time_rag`, `tdx_dc` (K线), Python(Pandas/TA-Lib) | Alpha因子, 择时信号, 波动率预测模型 |
| **风险管理师 (Risk Manager)** | 监控投资组合的各项风险敞口，进行压力测试。 | 投资组合持仓, 市场波动率指数(VIX), 相关性矩阵 | VaR计算, 压力测试脚本, Beta计算 | 风险敞口报告, 流动性预警, 对冲建议 |
| **投资组合经理 (Portfolio Manager)** | **(核心协调者)** 接收各分析师报告，根据宏观目标和风险约束，做出最终的投资组合决策。 | 所有分析师的报告, 用户设定的投资目标 | 投资组合优化算法 (如均值-方差), `LangGraph`编排 | **综合投资提案 (Investment Proposal)** |
| **交易执行官 (Execution Trader)** | 在收到指令后，以最优成本完成交易。 | 实时行情(Level-2), 交易所API | 交易执行算法 (VWAP/TWAP), `tdx_dc` (交易接口) | 交易执行记录, 滑点成本分析 |
