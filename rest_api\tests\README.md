# DataHub 单元测试

本目录包含对 DataHub 类的单元测试，用于验证各种数据获取接口的正确性。

## 测试文件说明

- `test_data_hub.py`: 包含对 DataHub 类所有方法的完整测试
- `test_single_method.py`: 用于测试单个方法，方便调试
- `run_tests.py`: 运行所有测试的脚本

## 运行测试

### 运行所有测试

```bash
python run_tests.py
```

### 运行单个方法测试

```bash
python test_single_method.py
```

## 注意事项

1. 测试前请确保已正确设置数据路径（`data_path`）
2. 部分测试可能会失败，这取决于数据源是否提供相应的数据
3. 测试中使用的股票代码、指数代码等可能需要根据实际情况进行调整
4. 测试需要连接到数据源，请确保网络连接正常

## 测试覆盖的方法

- 基础数据获取方法
  - `get_history_data` - 获取历史K线数据
  - `get_tick_data` - 获取逐笔成交数据
  - `get_kline_data` - 获取K线数据
  - `get_real_time_data` - 获取实时行情数据
  - `get_stock_info` - 获取股票基本信息

- 指数和行业数据方法
  - `get_index_data` - 获取指数数据
  - `get_industry_data` - 获取行业指数数据
  - `get_industry_stocks` - 获取行业成分股
  - `get_index_weight` - 获取指数成分权重

- 板块和概念方法
  - `get_block_data` - 获取板块数据
  - `get_concept_stocks` - 获取概念股票
  - `get_all_concepts` - 获取所有概念代码

- 财务和基本面数据方法
  - `get_financial_data` - 获取财务数据
  - `get_fund_holdings` - 获取基金持仓数据
  - `get_margin_data` - 获取融资融券数据
  - `get_dividend_data` - 获取分红送转数据

- 期货和期权数据方法
  - `get_future_info` - 获取期货合约信息
  - `get_option_info` - 获取期权合约信息

- 其他辅助方法
  - `get_all_stocks` - 获取所有股票代码
  - `get_trading_dates` - 获取交易日历
