<!DOCTYPE html>
<html class=" js flexbox canvastext" lang="en" style="">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>介绍 — AICloudMaster</title>

  <link rel="stylesheet" href="http://www.aicloudmaster.com/static/styles/theme.css" type="text/css">
  <link rel="stylesheet" href="http://www.aicloudmaster.com/static/styles/pygments.css" type="text/css">
  <link rel="stylesheet" href="http://www.aicloudmaster.com/static/styles/readthedocs-doc-embed.css" type="text/css">

</head>

<body style="">
  <div class="wy-grid-for-nav">
    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">
      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="breadcrumbs navigation">
            <hr>
          </div>
          <div role="main" class="document" itemscope="itemscope">
            <div itemprop="articleBody">
              <style>
                /* CSS overrides for sphinx_rtd_theme */
                /* 24px margin */
                .nbinput.nblast,
                .nboutput.nblast {
                  margin-bottom: 19px;
                  /* padding has already 5px */
                }
                /* ... except between code cells! */
                .nblast+.nbinput {
                  margin-top: -19px;
                }

                .admonition>p:before {
                  margin-right: 4px;
                  /* make room for the exclamation icon */
                }
              </style>
              <div class="section" id="index">
                <span id="id1"></span>
                <h1>介绍</h1>
                <p>AICloudMaster是一套完整的量化投资平台。该平台由三个子系统组成：QuantLab策略开发子系统，QuantRobot模拟交易子系
                  统和QuantCloud实盘交易云服务子系统。平台全部用C/C++语言开发，相较其他脚本开发系统运行效率高。平台以交易作为功能
                  核心，高效率、高并发、高可靠。</p>
                <p>另外，平台提供了微信公众号和小程序云服务器客户端，对云服务器进行管理。</p>
                <div class="section" id="id2">
                  <h2>特点</h2>
                  <div class="wy-table-responsive">
                    <table border="1" class="docutils">
                      <colgroup>
                        <col width="8%">
                        <col width="92%">
                      </colgroup>
                      <tbody valign="top">
                        <tr class="row-odd">
                          <td>易开发</td>
                          <td>策略开发易学，上手快。能编写算术表达式即可编写交易策略。</td>
                        </tr>
                        <tr class="row-even">
                          <td>更可靠</td>
                          <td>策略经过历史数据回溯测试和模拟交易，进行验证后，再做实盘交易更可靠。</td>
                        </tr>
                        <tr class="row-odd">
                          <td>云托管</td>
                          <td>云服务器托管交易系统，24小时运行，无须实时盯盘，夜盘无忧。</td>
                        </tr>
                        <tr class="row-even">
                          <td>随身行</td>
                          <td>手机终端实时登录云服务器，查看交易运行情况，外出、旅行不影响交易。</td>
                        </tr>
                        <tr class="row-odd">
                          <td>时掌握</td>
                          <td>微信实时通知查阅交易结果，轻松掌握。</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>


            </div>

          </div>
        </div>
      </div>

    </section>

  </div>
</body>

</html>