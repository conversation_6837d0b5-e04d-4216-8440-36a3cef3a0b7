"""
Risk Manager agent for the AI Hedge Fund module.

This agent calculates risk metrics and sets position limits.
"""

from typing import Dict, List, Any
import pandas as pd
import numpy as np

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_price_data


def risk_management_agent(state: AgentState) -> Dict[str, Any]:
    """
    Calculates risk metrics and sets position limits.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with risk management analysis
    """
    data = state["data"]
    tickers = data["tickers"]
    start_date = data["start_date"]
    end_date = data["end_date"]
    portfolio = data["portfolio"]
    
    # Initialize risk analysis dictionary
    risk_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("risk_management_agent", ticker, "Fetching price data")
        
        # Get price data
        price_data = get_price_data(ticker, start_date, end_date)
        
        if price_data.empty:
            # If no price data, set default values
            risk_analysis[ticker] = {
                "volatility": 0.0,
                "beta": 1.0,
                "var_95": 0.0,
                "max_drawdown": 0.0,
                "sharpe_ratio": 0.0,
                "current_price": 0.0,
                "position_limit": 10000.0,  # Default $10,000 limit
                "remaining_position_limit": 10000.0,
            }
            continue
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("risk_management_agent", ticker, "Calculating risk metrics")
        
        # Calculate daily returns
        price_data["daily_return"] = price_data["close"].pct_change()
        
        # Get current price (last available price)
        current_price = price_data["close"].iloc[-1]
        
        # Calculate risk metrics
        volatility = price_data["daily_return"].std() * np.sqrt(252)  # Annualized volatility
        var_95 = price_data["daily_return"].quantile(0.05) * np.sqrt(252)  # 95% VaR (annualized)
        
        # Calculate maximum drawdown
        rolling_max = price_data["close"].cummax()
        drawdown = (price_data["close"] - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # Calculate Sharpe ratio (assuming risk-free rate of 2%)
        risk_free_rate = 0.02 / 252  # Daily risk-free rate
        excess_return = price_data["daily_return"] - risk_free_rate
        sharpe_ratio = (excess_return.mean() / price_data["daily_return"].std()) * np.sqrt(252)
        
        # Calculate beta (if we have market data)
        beta = 1.0  # Default to 1.0 if no market data
        
        # Calculate position limit based on risk metrics
        # Higher volatility and drawdown => lower position limit
        base_position_limit = 10000.0  # Base limit of $10,000 per position
        
        # Adjust limit based on volatility (reduce limit for high volatility)
        volatility_factor = max(0.5, min(1.5, 1.0 / (volatility * 5)))
        
        # Adjust limit based on drawdown (reduce limit for high drawdown)
        drawdown_factor = max(0.5, min(1.5, 1.0 / (abs(max_drawdown) * 10)))
        
        # Combine factors to get final position limit
        position_limit = base_position_limit * volatility_factor * drawdown_factor
        
        # Calculate remaining position limit based on current position
        current_position = portfolio["positions"].get(ticker, {})
        long_position_value = current_position.get("long", 0) * current_price
        short_position_value = current_position.get("short", 0) * current_price
        
        # Net position value (long - short)
        net_position_value = long_position_value - short_position_value
        
        # Remaining position limit
        remaining_position_limit = position_limit - abs(net_position_value)
        remaining_position_limit = max(0, remaining_position_limit)
        
        # Store risk analysis
        risk_analysis[ticker] = {
            "volatility": volatility,
            "beta": beta,
            "var_95": var_95,
            "max_drawdown": max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            "current_price": current_price,
            "position_limit": position_limit,
            "remaining_position_limit": remaining_position_limit,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("risk_management_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(risk_analysis, "Risk Management Agent")
    
    # Add the risk analysis to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["risk_management_agent"] = risk_analysis
    
    return state
