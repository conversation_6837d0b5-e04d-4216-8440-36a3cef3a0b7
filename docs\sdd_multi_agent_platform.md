# 多智能体协作量化投资平台 - 系统设计文档 (SDD)

**版本:** 1.0

**作者:** Vitruvius (AI System Architect)

## 1. 引言

本文档旨在为“多智能体协作量化投资平台”提供一个全面、健壮、可扩展的技术架构蓝图。设计目标是支撑PRD中定义的、由多个AI专家智能体协作完成复杂投资任务的核心需求。

## 2. 架构概览

### 2.1. 架构风格

我们将采用 **事件驱动的微服务架构 (Event-Driven Microservices Architecture)**。

- **选型理由:**
  - **高内聚低耦合:** 每个AI专家Agent作为一个独立的微服务，可以独立开发、部署和扩展，互不影响。
  - **异步通信:** 投资研究的本质是异步的。事件驱动模型完美契合了Agent之间“发布报告-订阅报告”的协作模式。
  - **可扩展性:** 可以轻松地增加新的专家Agent，或对高负载的Agent（如量化分析师）进行水平扩展。
  - **韧性:** 单个Agent的故障不会导致整个系统崩溃。

### 2.2. 高层组件图

系统由以下核心组件构成：

1. **API网关 (API Gateway):** 系统的统一入口，处理用户身份验证和请求路由。面向最终用户。
2. **命令与控制中心 (C&C Center):** 即Web App，用户在此下达投资指令、审查投资提案。
3. **事件总线 (Event Bus):** 系统的中央动脉。所有服务间的通信都通过它进行，实现完全解耦。
4. **编排引擎 (Orchestration Engine):** 系统的“大脑”，由“投资组合经理”Agent实现。负责接收用户指令，将其分解为任务，并根据预设工作流驱动其他Agent协作。
5. **AI专家服务 (Agent Services):** 每个专家角色（经济学家、分析师等）都是一个独立的微服务，监听事件总线上的相关任务，并发布自己的研究成果。
6. **共享数据服务 (Shared Data Services):** 为所有Agent提供统一、标准的数据库和文件存储访问接口。

### 2.3. 核心数据流 (示例)

*用户下达“分析A股白酒板块”指令的数据流：*

1. `用户` -> `C&C Center` -> `API Gateway` 发送指令。
2. `API Gateway` -> `编排引擎` (Portfolio Manager)。
3. `编排引擎` -> `事件总线` 发布事件: `{ event: 'MACRO_ANALYSIS_REQUESTED' }`。
4. `首席经济学家Agent` 监听到事件，开始分析，完成后 -> `事件总线` 发布报告: `{ event: 'MACRO_REPORT_PUBLISHED', payload: {...} }`。
5. 同时，`编排引擎` -> `事件总线` 发布事件: `{ event: 'SECTOR_ANALYSIS_REQUESTED', sector: '白酒' }`。
6. `行业分析师Agent` 监听到事件，开始分析，完成后 -> `事件总线` 发布报告: `{ event: 'SECTOR_REPORT_PUBLISHED', payload: {...} }`。
7. `编排引擎` 订阅并接收到上述两份报告，进行综合分析，最终形成一份投资提案，更新内部状态，并通过 `API Gateway`通知用户。

## 3. 技术栈选型

| 组件                 | 技术选型                        | 选型理由                                                                                                                    |
| :------------------- | :------------------------------ | :-------------------------------------------------------------------------------------------------------------------------- |
| **API网关**    | FastAPI                         | 高性能，Python生态友好，与现有项目一致。                                                                                    |
| **事件总线**   | Apache Kafka                    | 高吞吐量，持久化消息，支持流处理，是金融级应用的事实标准。                                                                  |
| **编排引擎**   | LangGraph                       | 完美契合构建有状态、多Agent协作的图状工作流，与现有技术栈一致。                                                             |
| **Agent服务**  | Python + Docker                 | Python拥有最丰富的AI/ML库。Docker实现环境隔离和标准化部署。                                                                 |
| **统一数据库** | **PostgreSQL + pgvector** | **通过 `pgvector`和 `JSONB`类型，实现对事务、结构化、向量、文档数据的“四位一体”统一管理，是架构极简化的核心。** |
| **部署**       | Kubernetes (K8s)                | 自动化容器的部署、扩展和管理，是微服务架构的最佳实践。                                                                      |

## 4. 关键设计详述

### 4.1. 事件总线与事件模式 (Event Schema)

所有在事件总线上传递的消息都必须遵循统一的JSON Schema，以确保互操作性。

**事件示例:**

```json
{
  "eventId": "uuid-v4-string",
  "eventType": "SECTOR_REPORT_PUBLISHED",
  "sourceAgent": "Sector Analyst",
  "timestamp": "ISO-8601-datetime",
  "payload": {
    "sector": "白酒",
    "rating": "增持",
    "key_findings": [...],
    "related_tickers": ["600519.SH", "000858.SZ"]
  }
}
```

### 4.2. 编排引擎 (LangGraph) 设计

编排引擎的核心是一个由LangGraph定义的状态图。图的每个节点代表一个处理步骤（如 `await_macro_report`），每条边代表一个状态转换的条件。这种方式可以清晰地定义和管理复杂的、非线性的协作逻辑。

### 4.3. Agent服务模板

每个Agent服务都将基于一个标准模板构建，包含：

- 一个Kafka消费者，用于监听分配给它的任务。
- 核心业务逻辑，调用RAG、数据库或外部API。
- 一个Kafka生产者，用于将完成的报告发布回总线。

### 4.4. 终极统一查询模型 (The Ultimate Unified Query Model)

**此为新架构的“杀手级特性”。** 通过将所有数据类型统一到PostgreSQL中，我们可以利用SQL的全部能力，进行前所未有的、一步到位的混合查询，极大赋能上层Agent。

**混合查询示例:**
*需求: “查找由‘张三’撰写的，提到了‘A股’这个标签，且与‘人工智能产业政策’主题最相关的5篇新闻”*

```sql
SELECT title, publish_date, metadata->>'author' AS author
FROM news
WHERE
  metadata->>'author' = '张三' AND       -- 查询JSONB字段
  metadata->'tags' @> '["A股"]'::jsonb AND -- 查询JSONB数组是否包含某元素
  publish_date >= '2024-01-01'          -- 查询关系列
ORDER BY
  embedding <=> :query_embedding         -- 按向量相似度排序
LIMIT 5;
```

这种将**关系数据过滤**、**JSON文档内容查询**和**向量语义搜索**无缝融合在一条原子性SQL中的能力，是新架构的核心优势。

## 5. API 接口定义 (初步)

- `POST /api/v1/directives`: 用户创建新的投资指令。
- `GET /api/v1/directives`: 查看所有指令及其当前状态。
- `GET /api/v1/proposals/{proposal_id}`: 获取由AI团队生成的完整投资提案。
- `POST /api/v1/proposals/{proposal_id}/approve`: 批准提案，触发交易执行。

## 6. 横切关注点 (Cross-Cutting Concerns)

- **可观测性:**
  - **日志:** 所有Agent服务将日志集中推送到ELK或Loki进行统一管理。
  - **度量:** 使用Prometheus监控各微服务的性能指标（延迟、QPS、资源占用）。
  - **追踪:** LangSmith将用于端到端追踪一个投资指令在多个Agent之间流转的全过程，是调试和优化的关键。
- **安全性:** API网关将强制执行JWT认证。服务间的通信将使用mTLS进行加密和认证。

## 7. 部署与实施路线图

- **阶段一 (基础设施):** 搭建Kubernetes集群和Kafka事件总线。建立CI/CD流水线和统一的日志监控系统。
- **阶段二 (核心实现):** 开发编排引擎（投资组合经理Agent）和第一个专家Agent（如行业分析师）。打通从指令下达到生成第一份报告的端到端流程。
- **阶段三 (扩展):** 逐个实现并上线其他专家Agent，不断丰富系统的分析维度和能力。
- **阶段四 (闭环):** 实现交易执行Agent，完成从分析到交易的完整闭环。
