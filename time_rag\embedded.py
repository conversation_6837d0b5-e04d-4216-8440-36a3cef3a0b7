import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np

# --- Transformer Positional Encoding ---
class PositionalEncoding(nn.Module):
    """标准的 Transformer 位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0) # shape (1, max_len, d_model)
        self.register_buffer('pe', pe) # 注册为 buffer 而不是 parameter

    def forward(self, x):
        """
        Args:
            x: Tensor, shape [batch_size, seq_len, embedding_dim]
        """
        # x 只需要前 seq_len 个位置编码
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)

# --- 定义模型超参数 ---
# 这些需要根据你的具体数据和任务调整
SEQ_LEN = 30
VOCAB_SIZE = 40002
FUT_NUM_SYMBOLS = 100 # 假设有 100 个不同的证券代码
STK_NUM_SYMBOLS = 6000 # 假设有 6000 个不同的证券代码
SYMBOL_EMB_DIM = 32
TOKEN_EMB_DIM = 128
# 假设 get_time_features 返回 8 个特征
TIME_FEATURE_DIM = 8
HIDDEN_DIM = 256 # RNN/Transformer 内部维度
NUM_RNN_LAYERS = 2
NUM_TRANSFORMER_HEADS = 8
NUM_TRANSFORMER_LAYERS = 3
CNN_NUM_FILTERS = 128
CNN_KERNEL_SIZES = [3, 5, 7]
DROPOUT = 0.1
FINAL_EMB_DIM = 512 # RAG 检索向量的最终维度

class TimeSeriesRNNEmbedder(nn.Module):
    """
    基于 RNN 的 Embedding 模型
    """
    def __init__(self, num_symbols, vocab_size, seq_len,
                 symbol_emb_dim, token_emb_dim, time_feature_dim,
                 hidden_dim, num_layers, final_emb_dim, dropout=0.1):
        super().__init__()
        self.seq_len = seq_len
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # Embedding 层
        self.symbol_embedding = nn.Embedding(num_symbols, symbol_emb_dim)
        self.token_embedding = nn.Embedding(vocab_size, token_emb_dim)

        # LSTM 输入维度 = token embedding 维度 + 时间特征维度
        rnn_input_dim = token_emb_dim + time_feature_dim
        self.rnn = nn.LSTM(rnn_input_dim, hidden_dim, num_layers,
                           batch_first=True, dropout=dropout if num_layers > 1 else 0,
                           bidirectional=False) # 可以改为 True 实现双向

        # 输出层前的维度 = LSTM 隐藏层维度 + symbol embedding 维度
        # 如果是双向LSTM，hidden_dim * 2
        output_dim_before_proj = hidden_dim + symbol_emb_dim

        # 最终投影层
        self.projection = nn.Sequential(
            nn.Linear(output_dim_before_proj, output_dim_before_proj // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim_before_proj // 2, final_emb_dim)
        )

    def forward(self, symbol_indices, token_sequences, time_feature_vectors):
        """
        Args:
            symbol_indices: (batch_size,)
            token_sequences: (batch_size, seq_len)
            time_feature_vectors: (batch_size, seq_len, time_feature_dim)
        Returns:
            final_embedding: (batch_size, final_emb_dim)
        """
        batch_size = token_sequences.size(0)

        # 1. 获取 Embeddings
        sym_emb = self.symbol_embedding(symbol_indices) # (batch_size, symbol_emb_dim)
        tok_emb = self.token_embedding(token_sequences) # (batch_size, seq_len, token_emb_dim)

        # 2. 准备 RNN 输入
        # (batch_size, seq_len, token_emb_dim + time_feature_dim)
        rnn_input = torch.cat([tok_emb, time_feature_vectors], dim=-1)

        # 3. 通过 RNN
        # outputs: (batch_size, seq_len, hidden_dim * num_directions)
        # h_n: (num_layers * num_directions, batch_size, hidden_dim)
        # c_n: (num_layers * num_directions, batch_size, hidden_dim)
        outputs, (h_n, c_n) = self.rnn(rnn_input)

        # 4. 获取序列表示 (使用最后一个时间步的输出)
        # 如果是单向，取 outputs[:, -1, :]
        # h_n 的最后一个 layer 的 hidden state 也可以用：h_n[-1] 或 h_n.view(...) 处理
        sequence_repr = outputs[:, -1, :] # (batch_size, hidden_dim)

        # 5. 结合 Symbol Embedding
        # (batch_size, hidden_dim + symbol_emb_dim)
        combined_repr = torch.cat([sequence_repr, sym_emb], dim=-1)

        # 6. 最终投影
        final_embedding = self.projection(combined_repr) # (batch_size, final_emb_dim)

        return final_embedding
    

class TimeSeriesTransformerEmbedder(nn.Module):
    """
    基于 Transformer 的 Embedding 模型
    """
    def __init__(self, num_symbols, vocab_size, seq_len,
                 symbol_emb_dim, token_emb_dim, time_feature_dim,
                 d_model, nhead, num_encoder_layers, dim_feedforward,
                 final_emb_dim, dropout=0.1):
        super().__init__()
        self.seq_len = seq_len
        self.d_model = d_model # Transformer 内部维度

        # Embedding 层
        self.symbol_embedding = nn.Embedding(num_symbols, symbol_emb_dim)
        self.token_embedding = nn.Embedding(vocab_size, token_emb_dim)

        # 输入投影层，将 token_emb + time_feat 映射到 d_model
        input_dim = token_emb_dim + time_feature_dim
        self.input_projection = nn.Linear(input_dim, d_model)

        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout, max_len=seq_len + 1) # +1 以防万一

        # Transformer Encoder
        encoder_layers = nn.TransformerEncoderLayer(d_model, nhead, dim_feedforward, dropout, batch_first=True)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layers, num_encoder_layers)

        # 输出层前的维度 = d_model (来自序列表示) + symbol_emb_dim
        output_dim_before_proj = d_model + symbol_emb_dim

        # 最终投影层
        self.projection = nn.Sequential(
            nn.Linear(output_dim_before_proj, output_dim_before_proj // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim_before_proj // 2, final_emb_dim)
        )

    def forward(self, symbol_indices, token_sequences, time_feature_vectors):
        """
        Args:
            symbol_indices: (batch_size,)
            token_sequences: (batch_size, seq_len)
            time_feature_vectors: (batch_size, seq_len, time_feature_dim)
        Returns:
            final_embedding: (batch_size, final_emb_dim)
        """
        batch_size = token_sequences.size(0)

        # 1. 获取 Embeddings
        sym_emb = self.symbol_embedding(symbol_indices) # (batch_size, symbol_emb_dim)
        tok_emb = self.token_embedding(token_sequences) # (batch_size, seq_len, token_emb_dim)

        # 2. 准备 Transformer 输入
        # (batch_size, seq_len, token_emb_dim + time_feature_dim)
        step_input = torch.cat([tok_emb, time_feature_vectors], dim=-1)
        # (batch_size, seq_len, d_model)
        projected_input = self.input_projection(step_input) * math.sqrt(self.d_model) # 缩放

        # 3. 添加位置编码
        # (batch_size, seq_len, d_model)
        pos_encoded_input = self.pos_encoder(projected_input)

        # 4. 通过 Transformer Encoder
        # (batch_size, seq_len, d_model)
        # 注意：可以添加 src_key_padding_mask 如果有 padding 的话
        encoded_sequence = self.transformer_encoder(pos_encoded_input)

        # 5. 获取序列表示 (例如，使用平均池化)
        sequence_repr = encoded_sequence.mean(dim=1) # (batch_size, d_model)
        # 或者使用 CLS Token (需要修改输入和模型结构)

        # 6. 结合 Symbol Embedding
        # (batch_size, d_model + symbol_emb_dim)
        combined_repr = torch.cat([sequence_repr, sym_emb], dim=-1)

        # 7. 最终投影
        final_embedding = self.projection(combined_repr) # (batch_size, final_emb_dim)

        return final_embedding

class TimeSeriesCNNEmbedder(nn.Module):
    """
    基于 1D CNN 的 Embedding 模型
    """
    def __init__(self, num_symbols, vocab_size, seq_len,
                 symbol_emb_dim, token_emb_dim, time_feature_dim,
                 num_filters, kernel_sizes, final_emb_dim, dropout=0.1):
        super().__init__()
        self.seq_len = seq_len

        # Embedding 层
        self.symbol_embedding = nn.Embedding(num_symbols, symbol_emb_dim)
        self.token_embedding = nn.Embedding(vocab_size, token_emb_dim)

        # 输入维度
        input_dim = token_emb_dim + time_feature_dim

        # 1D 卷积层
        self.conv_layers = nn.ModuleList([
            nn.Conv1d(in_channels=input_dim,
                      out_channels=num_filters,
                      kernel_size=k,
                      padding=(k - 1) // 2) # 'same' padding
            for k in kernel_sizes
        ])

        # 输出层前的维度 = 卷积核数量 * 每个卷积核的滤波器数量 + symbol embedding 维度
        output_dim_before_proj = len(kernel_sizes) * num_filters + symbol_emb_dim

        self.dropout_layer = nn.Dropout(dropout)

        # 最终投影层
        self.projection = nn.Sequential(
            nn.Linear(output_dim_before_proj, output_dim_before_proj // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim_before_proj // 2, final_emb_dim)
        )

    def forward(self, symbol_indices, token_sequences, time_feature_vectors):
        """
        Args:
            symbol_indices: (batch_size,)
            token_sequences: (batch_size, seq_len)
            time_feature_vectors: (batch_size, seq_len, time_feature_dim)
        Returns:
            final_embedding: (batch_size, final_emb_dim)
        """
        batch_size = token_sequences.size(0)

        # 1. 获取 Embeddings
        sym_emb = self.symbol_embedding(symbol_indices) # (batch_size, symbol_emb_dim)
        tok_emb = self.token_embedding(token_sequences) # (batch_size, seq_len, token_emb_dim)

        # 2. 准备 CNN 输入
        # (batch_size, seq_len, token_emb_dim + time_feature_dim)
        cnn_input = torch.cat([tok_emb, time_feature_vectors], dim=-1)

        # 3. 调整维度以适应 Conv1d: (N, C_in, L_in)
        # (batch_size, input_dim, seq_len)
        cnn_input = cnn_input.permute(0, 2, 1)

        # 4. 通过卷积层 + 激活 + 池化
        pooled_outputs = []
        for conv in self.conv_layers:
            conv_out = conv(cnn_input) # (batch_size, num_filters, seq_len)
            activated_out = F.relu(conv_out)
            # 全局最大池化 Global Max Pooling
            # (batch_size, num_filters, 1) -> (batch_size, num_filters)
            pooled = F.max_pool1d(activated_out, kernel_size=activated_out.size(2)).squeeze(2)
            pooled_outputs.append(pooled)

        # 5. 拼接所有卷积核的池化输出
        # (batch_size, len(kernel_sizes) * num_filters)
        cnn_output = torch.cat(pooled_outputs, dim=1)
        cnn_output = self.dropout_layer(cnn_output) # 应用 Dropout

        # 6. 结合 Symbol Embedding
        # (batch_size, len(Ks)*num_filters + symbol_emb_dim)
        combined_repr = torch.cat([cnn_output, sym_emb], dim=-1)

        # 7. 最终投影
        final_embedding = self.projection(combined_repr) # (batch_size, final_emb_dim)

        return final_embedding
    

if __name__ == "__main__":
    # --- 准备伪数据 ---
    BATCH_SIZE = 4 # 示例 Batch Size

    # (B,)
    dummy_symbol_indices = torch.randint(0, FUT_NUM_SYMBOLS, (BATCH_SIZE,), dtype=torch.long)
    # (B, SeqLen)
    dummy_token_sequences = torch.randint(0, VOCAB_SIZE, (BATCH_SIZE, SEQ_LEN), dtype=torch.long)

    # (B, SeqLen, TimeFeatDim) - 实际中你需要根据 datetime 数据生成这个
    # 这里用随机数代替
    dummy_time_features = torch.randn(BATCH_SIZE, SEQ_LEN, TIME_FEATURE_DIM, dtype=torch.float)

    print(dummy_symbol_indices.shape)
    print(dummy_token_sequences.shape)
    print(dummy_time_features.shape)

    print(dummy_symbol_indices[0])
    print(dummy_token_sequences[0])
    print(dummy_time_features[0])

    # --- 实例化并测试模型 ---

    # RNN
    print("--- RNN Embedder ---")
    rnn_embedder = TimeSeriesRNNEmbedder(
        num_symbols=FUT_NUM_SYMBOLS, vocab_size=VOCAB_SIZE, seq_len=SEQ_LEN,
        symbol_emb_dim=SYMBOL_EMB_DIM, token_emb_dim=TOKEN_EMB_DIM,
        time_feature_dim=TIME_FEATURE_DIM, hidden_dim=HIDDEN_DIM,
        num_layers=NUM_RNN_LAYERS, final_emb_dim=FINAL_EMB_DIM, dropout=DROPOUT
    )
    rnn_output = rnn_embedder(dummy_symbol_indices, dummy_token_sequences, dummy_time_features)
    print("RNN Output Shape:", rnn_output.shape) # 应为 (BATCH_SIZE, FINAL_EMB_DIM)

    # Transformer
    print("\n--- Transformer Embedder ---")
    transformer_embedder = TimeSeriesTransformerEmbedder(
        num_symbols=FUT_NUM_SYMBOLS, vocab_size=VOCAB_SIZE, seq_len=SEQ_LEN,
        symbol_emb_dim=SYMBOL_EMB_DIM, token_emb_dim=TOKEN_EMB_DIM,
        time_feature_dim=TIME_FEATURE_DIM, d_model=HIDDEN_DIM, # 使用 HIDDEN_DIM 作为 d_model
        nhead=NUM_TRANSFORMER_HEADS, num_encoder_layers=NUM_TRANSFORMER_LAYERS,
        dim_feedforward=HIDDEN_DIM * 4, # 通常设为 d_model 的 4 倍
        final_emb_dim=FINAL_EMB_DIM, dropout=DROPOUT
    )
    transformer_output = transformer_embedder(dummy_symbol_indices, dummy_token_sequences, dummy_time_features)
    print("Transformer Output Shape:", transformer_output.shape) # 应为 (BATCH_SIZE, FINAL_EMB_DIM)

    # CNN
    print("\n--- CNN Embedder ---")
    cnn_embedder = TimeSeriesCNNEmbedder(
        num_symbols=FUT_NUM_SYMBOLS, vocab_size=VOCAB_SIZE, seq_len=SEQ_LEN,
        symbol_emb_dim=SYMBOL_EMB_DIM, token_emb_dim=TOKEN_EMB_DIM,
        time_feature_dim=TIME_FEATURE_DIM, num_filters=CNN_NUM_FILTERS,
        kernel_sizes=CNN_KERNEL_SIZES, final_emb_dim=FINAL_EMB_DIM, dropout=DROPOUT
    )
    cnn_output = cnn_embedder(dummy_symbol_indices, dummy_token_sequences, dummy_time_features)
    print("CNN Output Shape:", cnn_output.shape) # 应为 (BATCH_SIZE, FINAL_EMB_DIM)