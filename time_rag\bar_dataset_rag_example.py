"""
Example script demonstrating the usage of BarDatasetRetriever.

This script shows how to:
1. Create a BarDatasetRetriever
2. Build an index from BarDataset2 data
3. Save and load the index
4. Retrieve similar data points
5. Add new data to the index
6. Update existing data in the index
"""

import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from argparse import ArgumentParser
import random

from pyqlab.data.dataset.pipeline import Pipeline
from pyqlab.data.dataset.dataset_bar2 import BarDataset2
from pyqlab.const import *
from pyqlab.utils.config import set_seed, CfgNode as CN
from pyqlab.rag.embedded import TimeSeriesRNNEmbedder

from bar_dataset_rag import BarDatasetRetriever, create_bar_dataset_retriever


def get_config():
    """Get default configuration for BarDataset2."""
    C = CN()
    # data
    C.data = BarDataset2.get_default_config()
    return C


def setup_dataset(args):
    """Set up the BarDataset2 dataset."""
    config = get_config()
    config.data.update_from_dict(vars(args))

    # Set up the data pipeline
    pipe = Pipeline(
        config.data.data_path,
        config.data.market,
        config.data.block_name,
        config.data.period,
        config.data.start_year,
        config.data.end_year,
        config.data.start_date,
        config.data.end_date,
        config.data.block_size,
        config.data.timeenc,
        config.data.sel_codes
    )
    data = pipe.get_data()

    # Limit data size for example
    data = data[:300]

    # Create dataset
    dataset = BarDataset2(config.data, data)

    return dataset


def visualize_retrieval(query_data, retrieved_data, retrieved_metadata, title="Retrieval Results"):
    """Visualize the query and retrieved data."""
    plt.figure(figsize=(12, 6))

    # Plot query data
    plt.subplot(1, 2, 1)
    plt.plot(query_data.squeeze())
    plt.title("Query Data")
    plt.grid(True)

    # Plot retrieved data
    plt.subplot(1, 2, 2)
    for i, (data, metadata) in enumerate(zip(retrieved_data, retrieved_metadata)):
        plt.plot(data.squeeze(), label=f"Rank {i+1}, Index {metadata['index']}")

    plt.title("Retrieved Data")
    plt.grid(True)
    plt.legend()

    plt.suptitle(title)
    plt.tight_layout()
    plt.show()


def main(args):
    """Main function to demonstrate BarDatasetRetriever usage."""
    # Set random seed for reproducibility
    if args.seed is None:
        args.seed = random.randint(0, 10000)
    set_seed(args.seed)

    print(f"Using random seed: {args.seed}")

    # Set up dataset
    dataset = setup_dataset(args)
    print(f"Dataset size: {len(dataset)}")

    # Create embedder model
    hidden_dim = 64
    embedder = TimeSeriesRNNEmbedder(
        num_symbols=100,  # 假设有100个不同的证券代码
        vocab_size=40002,
        seq_len=30,
        symbol_emb_dim=32,
        token_emb_dim=128,
        time_feature_dim=8,
        hidden_dim=hidden_dim,
        num_layers=2,
        final_emb_dim=hidden_dim,
        dropout=0.1
    )

    # Create retriever
    print("Creating retriever...")
    if args.use_factory:
        # Use factory function
        retriever = create_bar_dataset_retriever(
            embedding_dim=hidden_dim,
            embedder_type='rnn',
            n_neighbors=args.n_neighbors,
            metric=args.metric,
            num_symbols=100,
            vocab_size=40002,
            seq_len=30,
            symbol_emb_dim=32,
            token_emb_dim=128,
            time_feature_dim=8,
            hidden_dim=hidden_dim,
            num_layers=2,
            dropout=0.1
        )
    else:
        # Create manually
        retriever = BarDatasetRetriever(
            embedding_dim=hidden_dim,
            n_neighbors=args.n_neighbors,
            metric=args.metric
        )

        # Create embedder function
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        embedder = embedder.to(device)
        embedder.eval()

        def embedder_fn(code, bar, x_mark):
            with torch.no_grad():
                embedding = embedder(code, bar, x_mark)
                return embedding.cpu().numpy()

        retriever.set_embedder(embedder_fn)

    # Build index
    print("Building index...")
    retriever.build_index(dataset)
    print(f"Index built with {len(retriever.metadata)} entries")

    # Save index
    if args.save_dir:
        os.makedirs(args.save_dir, exist_ok=True)
        print(f"Saving index to {args.save_dir}...")
        index_path = retriever.save(args.save_dir)
        print(f"Index saved to {index_path}")

        # Load index
        print("Loading index...")
        new_retriever = BarDatasetRetriever(embedding_dim=hidden_dim)
        new_retriever.load(args.save_dir)
        print(f"Index loaded with {len(new_retriever.metadata)} entries")

        # Use the loaded retriever for subsequent operations
        retriever = new_retriever

    # Retrieve similar data points
    print("\nRetrieving similar data points...")

    # Get a random data point as query
    query_idx = random.randint(0, len(dataset) - 1)
    data = dataset[query_idx]

    # Extract code, bar, x_mark from the dataset item
    code, bar, x_mark, _, _, _ = data

    # Retrieve similar data points
    print(code.shape, bar.shape, x_mark.shape)
    code = code.unsqueeze(0)
    bar = bar.unsqueeze(0)
    x_mark = x_mark.unsqueeze(0)
    print(code.shape, bar.shape, x_mark.shape)

    distances, indices, metadata = retriever.retrieve(code[:,0], bar, x_mark, k=args.n_neighbors)

    print(f"Query index: {query_idx}")
    print(f"Retrieved indices: {indices[0]}")
    print(f"Distances: {distances[0]}")
    print(f"Metadata: {metadata}")



    # Visualize results
    # if args.visualize:
    #     visualize_retrieval(bar.detach().cpu().numpy() if isinstance(bar, torch.Tensor) else bar,
    #                        retrieved_data, metadata)

    # Add new data to index
    # print("\nAdding new data to index...")

    # # Create synthetic data points
    # seq_len = bar.shape[1] if len(bar.shape) > 1 else bar.shape[0]
    # new_code = torch.zeros(1, seq_len, 1)  # 虚拟的code数据
    # new_bar = torch.tensor(np.sin(np.linspace(0, 10, seq_len)).reshape(1, seq_len, 1))  # 虚拟的bar数据
    # new_x_mark = torch.zeros(1, seq_len, 1)  # 虚拟的x_mark数据
    # new_metadata = {'source': 'synthetic', 'created_at': 'now'}

    # # Add to index
    # new_idx = retriever.add_to_index(new_code, new_bar, new_x_mark, new_metadata)
    # print(f"New data added at index {new_idx}")

    # # Update existing data
    # print("\nUpdating existing data...")

    # # Create updated data
    # seq_len = bar.shape[1] if len(bar.shape) > 1 else bar.shape[0]
    # updated_code = torch.zeros(1, seq_len, 1)  # 虚拟的code数据
    # updated_bar = torch.tensor(np.cos(np.linspace(0, 10, seq_len)).reshape(1, seq_len, 1))  # 虚拟的bar数据
    # updated_x_mark = torch.zeros(1, seq_len, 1)  # 虚拟的x_mark数据
    # updated_metadata = {'updated': True}

    # # Update index
    # retriever.update_index(new_idx, updated_code, updated_bar, updated_x_mark, updated_metadata)
    # print(f"Data at index {new_idx} updated")

    # Retrieve again to see the effect
    # print("\nRetrieving with updated index...")
    # distances, indices, metadata = retriever.retrieve(code, bar, x_mark, k=args.n_neighbors)

    # print(f"Query index: {query_idx}")
    # print(f"Retrieved indices: {indices[0]}")
    # print(f"Distances: {distances[0]}")

    # Check if our updated data is in the results
    # if new_idx in indices[0]:
    #     print(f"Updated data (index {new_idx}) found in results!")
    #     print(f"Metadata: {metadata[list(indices[0]).index(new_idx)]}")

    print("\nDone!")


if __name__ == "__main__":
    parser = ArgumentParser(description="BarDatasetRetriever Example")

    # Dataset parameters
    parser.add_argument("--data_path", type=str, default="data", help="Path to data directory")
    parser.add_argument("--market", type=str, default="fut", help="Market type (fut, stk)")
    parser.add_argument("--block_name", type=str, default="sf", help="Block name (sf, main)")
    parser.add_argument("--period", type=str, default="min5", help="Period (day, min5)")
    parser.add_argument("--start_year", type=int, default=2024, help="Start year")
    parser.add_argument("--end_year", type=int, default=2024, help="End year")
    parser.add_argument("--start_date", type=str, default="", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end_date", type=str, default="", help="End date (YYYY-MM-DD)")
    parser.add_argument("--block_size", type=int, default=30, help="Block size")
    parser.add_argument("--timeenc", type=int, default=2, help="Time encoding (0: fixed, 1: timeF)")
    parser.add_argument("--sel_codes", type=str, default=None, help="Selected codes (comma-separated)")

    # Retriever parameters
    parser.add_argument("--n_neighbors", type=int, default=5, help="Number of neighbors to retrieve")
    parser.add_argument("--metric", type=str, default="l2", choices=["l2", "ip"], help="Metric for similarity search")
    parser.add_argument("--use_factory", action="store_true", help="Use factory function to create retriever")
    parser.add_argument("--save_dir", type=str, default=None, help="Directory to save index")
    parser.add_argument("--visualize", action="store_true", help="Visualize retrieval results")
    parser.add_argument("--seed", type=int, default=None, help="Random seed")

    args = parser.parse_args()
    args.data_path = "f:/featdata/barenc/db2"
    print(args)

    main(args)
