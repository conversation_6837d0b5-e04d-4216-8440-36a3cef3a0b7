import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import mean_squared_error, mean_absolute_percentage_error
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from argparse import ArgumentParser
import faiss
from pyqlab.data.dataset.pipeline import Pipeline
from pyqlab.data.dataset.dataset_bar2 import BarDataset2
from pyqlab.const import *
from pyqlab.utils.config import set_seed, setup_logging, CfgNode as CN
from pyqlab.rag.embedded import TimeSeriesRNNEmbedder, TimeSeriesTransformerEmbedder, TimeSeriesCNNEmbedder
from torch.utils.data import TensorDataset, Subset
from sklearn.model_selection import TimeSeriesSplit, KFold
import random

# 检索组件
class TimeSeriesRetriever:
    def __init__(self, embedding_dim, n_neighbors=5):
        self.embedding_dim = embedding_dim
        self.n_neighbors = n_neighbors
        self.index = None
        self.ts_embeddings = None
        self.targets = None

    def build_index(self, ts_embeddings, targets):
        """构建FAISS索引"""
        self.ts_embeddings = ts_embeddings
        self.targets = targets

        # 构建索引
        self.index = faiss.IndexFlatL2(ts_embeddings.shape[1])
        self.index.add(ts_embeddings)

    def retrieve(self, ts_query):
        """检索最相似的历史序列"""
        # 执行检索
        distances, indices = self.index.search(ts_query, self.n_neighbors)

        # 返回检索结果
        retrieved_ts = self.ts_embeddings[indices]
        retrieved_targets = self.targets[indices]

        return retrieved_ts, retrieved_targets, distances
    
    def save(self, index_path):
        # 保存索引
        faiss.write_index(self.index, index_path)
        # 保存嵌入
        np.save(index_path + '_embeddings.npy', self.ts_embeddings)
        # 保存目标
        np.save(index_path + '_targets.npy', self.targets)

    def load(self, index_path):
        """加载FAISS索引"""
        self.index = faiss.read_index(index_path)
        # 加载嵌入
        self.ts_embeddings = np.load(index_path + '_embeddings.npy')
        # 加载目标
        self.targets = np.load(index_path + '_targets.npy')

# TimeRAG编码器
def GetTimeSeriesEncoder(embedder, args):
    """时间序列编码器
    
    支持三种不同的编码器类型:
    - RNN: 使用双向LSTM进行序列编码
    - Transformer: 使用Transformer编码器进行序列编码
    - CNN: 使用1D卷积进行序列编码
    
    Args:
        embedder (str): 编码器类型,可选 'rnn', 'transformer', 'cnn'
        args (dict): 编码器参数配置
    """
    
    # 根据embedder类型创建对应的编码器
    if embedder == 'rnn':
        encoder = TimeSeriesRNNEmbedder(
            num_symbols=args.fut_num_symbols,
            vocab_size=args.vocab_size,
            seq_len=args.seq_len,
            symbol_emb_dim=args.symbol_emb_dim,
            token_emb_dim=args.token_emb_dim,
            time_feature_dim=args.time_feature_dim,
            hidden_dim=args.hidden_dim,
            num_layers=args.num_rnn_layers,
            final_emb_dim=args.final_emb_dim,
            dropout=args.dropout
        )
    elif embedder == 'transformer':
        encoder = TimeSeriesTransformerEmbedder(
            num_symbols=args.fut_num_symbols,
            vocab_size=args.vocab_size,
            seq_len=args.seq_len,
            symbol_emb_dim=args.symbol_emb_dim,
            token_emb_dim=args.token_emb_dim,
            time_feature_dim=args.time_feature_dim,
            d_model=args.hidden_dim,
            nhead=args.num_transformer_heads,
            num_encoder_layers=args.num_transformer_layers,
            dim_feedforward=args.hidden_dim * 4,
            final_emb_dim=args.final_emb_dim,
            dropout=args.dropout
        )
    elif embedder == 'cnn':
        encoder = TimeSeriesCNNEmbedder(
            num_symbols=args.fut_num_symbols,
            vocab_size=args.vocab_size,
            seq_len=args.seq_len,
            symbol_emb_dim=args.symbol_emb_dim,
            token_emb_dim=args.token_emb_dim,
            time_feature_dim=args.time_feature_dim,
            num_filters=args.cnn_num_filters,
            kernel_sizes=args.cnn_kernel_sizes,
            final_emb_dim=args.final_emb_dim,
            dropout=args.dropout
        )
    else:
        raise ValueError(f"不支持的编码器类型: {embedder}")
    return encoder

# TimeRAG生成器
class TimeSeriesGenerator(nn.Module):
    def __init__(self, context_dim, hidden_dim, output_dim):
        super(TimeSeriesGenerator, self).__init__()

        self.context_processor = nn.Sequential(
            nn.Linear(context_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        self.retrieval_processor = nn.Sequential(
            nn.Linear(context_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        self.fusion_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, output_dim),
            nn.Sigmoid()  # 使用Sigmoid确保输出在0-1范围内（因为使用了MinMaxScaler）
        )

    def forward(self, context_embedding, retrieval_embedding):
        # 处理上下文嵌入
        processed_context = self.context_processor(context_embedding)

        # 处理检索嵌入
        processed_retrieval = self.retrieval_processor(retrieval_embedding)

        # 融合
        fusion = torch.cat([processed_context, processed_retrieval], dim=1)
        fusion_output = self.fusion_layer(fusion)

        # 生成预测
        prediction = self.output_layer(fusion_output)

        return prediction

# 完整的TimeRAG模型
class TimeRAG(nn.Module):
    def __init__(self, ts_dim, hidden_dim, output_dim, retriever, args, n_neighbors=5):
        super(TimeRAG, self).__init__()

        # 编码器
        self.ts_encoder = GetTimeSeriesEncoder(embedder=args.embedder, args=args)

        # 检索器Get
        self.retriever = retriever
        self.n_neighbors = n_neighbors

        # 生成器
        context_dim = hidden_dim * 2  # 因为LSTM是双向的
        self.generator = TimeSeriesGenerator(context_dim * 2, hidden_dim, output_dim)


    def forward(self, price_seq, is_training=True):
        # 编码输入序列
        ts_embedding = self.ts_encoder(price_seq)

        if is_training:
            # 在训练时，使用批处理
            batch_size = price_seq.size(0)

            # 转换为numpy以用于FAISS检索
            ts_np = ts_embedding.detach().cpu().numpy()

            # 执行检索
            retrieved_ts, _, _ = self.retriever.retrieve(ts_np)

            # 转换回PyTorch张量并重塑维度
            retrieved_ts = torch.FloatTensor(retrieved_ts).to(price_seq.device)

            # 调整形状为 [batch_size, n_neighbors, embedding_dim]
            retrieved_ts = retrieved_ts.view(batch_size, self.n_neighbors, -1)

            # 为每个样本计算平均检索嵌入
            avg_retrieved_ts = torch.mean(retrieved_ts, dim=1)

            # 生成预测
            prediction = self.generator(ts_embedding, avg_retrieved_ts)

        else:
            # 在推理时，使用单例处理

            # 转换为numpy以用于FAISS检索
            ts_np = ts_embedding.detach().cpu().numpy()

            # 执行检索
            retrieved_ts, _ = self.retriever.retrieve(ts_np)

            # 转换回PyTorch张量
            retrieved_ts = torch.FloatTensor(retrieved_ts).to(price_seq.device)

            # 计算平均检索嵌入
            avg_retrieved_ts = torch.mean(retrieved_ts, dim=1)

            # 生成预测
            prediction = self.generator(ts_embedding, avg_retrieved_ts)

        return prediction

# 训练与评估
def train_timerag(model, train_loader, val_loader, criterion, optimizer, device, epochs=50):
    """训练TimeRAG模型"""
    model.to(device)
    best_val_loss = float('inf')
    train_losses, val_losses = [], []

    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0

        for code, bar, x_mark, y, _ in train_loader:
            code, bar, y = code.to(device), bar.to(device), y.to(device)

            # 清除梯度
            optimizer.zero_grad()

            # 前向传播
            output = model(bar)

            # 计算损失
            loss = criterion(output, target)

            # 反向传播
            loss.backward()

            # 更新参数
            optimizer.step()

            train_loss += loss.item()

        train_loss /= len(train_loader)
        train_losses.append(train_loss)

        # 验证阶段
        model.eval()
        val_loss = 0

        with torch.no_grad():
            for ts_seq, target in val_loader:
                ts_seq, target = ts_seq.to(device), target.to(device)

                # 前向传播
                output = model(ts_seq, is_training=False)

                # 计算损失
                loss = criterion(output, target)
                val_loss += loss.item()

        val_loss /= len(val_loader)
        val_losses.append(val_loss)

        # 打印进度
        print(f'Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}')

        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), 'best_timerag_model.pth')

    return train_losses, val_losses

def evaluate_timerag(model, test_loader, device):
    """评估TimeRAG模型"""
    model.to(device)
    model.eval()
    predictions, actuals = [], []

    with torch.no_grad():
        for ts_seq, target in test_loader:
            ts_seq, target = ts_seq.to(device), target.to(device)

            # 前向传播
            output = model(ts_seq, is_training=False)

            # 转换回CPU
            output = output.cpu().numpy()
            target = target.cpu().numpy()

            # 逆标准化
            # output = scaler.inverse_transform(output)
            # target = scaler.inverse_transform(target)

            predictions.extend(output)
            actuals.extend(target)

    # 计算评估指标
    predictions = np.array(predictions)
    actuals = np.array(actuals)

    rmse = np.sqrt(mean_squared_error(actuals, predictions))
    mape = mean_absolute_percentage_error(actuals, predictions) * 100

    # 计算方向准确率
    direction_actual = np.diff(actuals.flatten())
    direction_pred = np.diff(predictions.flatten())
    direction_accuracy = np.mean((direction_actual * direction_pred) > 0) * 100

    return rmse, mape, direction_accuracy, predictions, actuals

# 回测交易策略
def backtest_strategy(predictions, actuals, initial_capital=10000):
    """回测简单的交易策略"""
    capital = initial_capital
    position = 0
    trades = []

    for i in range(1, len(predictions)):
        # 预测方向
        pred_direction = predictions[i] - actuals[i-1]

        # 实际方向
        actual_direction = actuals[i] - actuals[i-1]

        # 交易逻辑：预测上涨则买入，预测下跌则卖出
        if pred_direction > 0 and position <= 0:
            # 买入
            position = 1
            entry_price = actuals[i-1]
            trades.append(('buy', i-1, entry_price))
        elif pred_direction < 0 and position >= 0:
            # 卖出
            position = -1
            entry_price = actuals[i-1]
            trades.append(('sell', i-1, entry_price))

    # 计算交易绩效
    returns = []
    current_position = None

    for i in range(len(trades)):
        action, day, price = trades[i]

        if i < len(trades) - 1:
            next_action, next_day, next_price = trades[i+1]

            if action == 'buy' and next_action == 'sell':
                # 完成一次买入-卖出交易
                returns.append((next_price - price) / price)
            elif action == 'sell' and next_action == 'buy':
                # 完成一次卖出-买入交易
                returns.append((price - next_price) / price)

    # 计算绩效指标
    returns = np.array(returns)
    cumulative_return = np.prod(1 + returns) - 1
    annual_return = (1 + cumulative_return) ** (252 / len(actuals)) - 1
    volatility = np.std(returns) * np.sqrt(252)
    sharpe_ratio = annual_return / volatility if volatility != 0 else 0

    # 计算最大回撤
    cumulative_returns = np.cumprod(1 + returns) - 1
    peak = np.maximum.accumulate(cumulative_returns)
    drawdown = (peak - cumulative_returns) / (1 + peak)
    max_drawdown = np.max(drawdown)

    # 计算胜率
    win_rate = np.mean(returns > 0) * 100

    return {
        'cumulative_return': cumulative_return * 100,
        'annual_return': annual_return * 100,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown * 100,
        'win_rate': win_rate
    }

def get_config():
    C = CN()
    # data
    C.data = BarDataset2.get_default_config()
    return C

# 主函数
def main(args):
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    args.seed = random.randint(0, 10000)
    if args.period == 'min5':
        args.freq = 't'
    elif args.period == 'day':
        args.freq = 'b'
    else:
        raise ValueError(f"Invalid period: {args.period}")
    if args.market == 'fut' and args.block_name == 'sf':
        args.sel_codes = SF_FUT_CODES

    config = get_config()
    config.data.update_from_dict(vars(args))

    if args.time_encoding == 'timeF':
        config.data.timeenc = 1
    elif args.time_encoding == 'fixed':
        config.data.timeenc = 0
    elif args.time_encoding == 'learned':
        config.data.timeenc = 2

    print(args)

    config = get_config()
    # config.system.update_from_dict(vars(args))
    config.data.update_from_dict(vars(args))

    pipe = Pipeline(
        config.data.data_path,
        config.data.market,
        config.data.block_name,
        config.data.period,
        config.data.start_year,
        config.data.end_year,
        config.data.start_date,
        config.data.end_date,
        config.data.block_size,
        config.data.timeenc,
        config.data.sel_codes
    )
    data = pipe.get_data()

    data = data[:300]

    dataset = BarDataset2(config.data, data)

    # 首先将数据集分成两部分：训练+验证集(80%)和测试集(20%)
    first_split = KFold(n_splits=5, shuffle=True, random_state=args.seed)
    all_indices = np.arange(len(dataset))
    train_val_idx, test_idx = next(first_split.split(all_indices))

    # 然后将训练+验证集进一步分成训练集(64%)和验证集(16%)
    second_split = KFold(n_splits=5, shuffle=True, random_state=args.seed)
    train_idx, val_idx = next(second_split.split(all_indices[train_val_idx]))

    # 将相对索引转换为绝对索引
    train_idx = train_val_idx[train_idx]
    val_idx = train_val_idx[val_idx]

    print(f"数据集总大小: {len(dataset)}")
    print(f"训练集大小: {len(train_idx)} ({len(train_idx)/len(dataset)*100:.1f}%)")
    print(f"验证集大小: {len(val_idx)} ({len(val_idx)/len(dataset)*100:.1f}%)")
    print(f"测试集大小: {len(test_idx)} ({len(test_idx)/len(dataset)*100:.1f}%)")

    # 创建数据子集
    train_dataset = Subset(dataset, train_idx)
    val_dataset = Subset(dataset, val_idx)
    test_dataset = Subset(dataset, test_idx)

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)


    # 2. 构建检索器
    # 首先需要为训练集生成嵌入
    hidden_dim = 128
    if args.embedder == 'rnn':
        ts_encoder = TimeSeriesRNNEmbedder(
            num_symbols=args.fut_num_symbols,
            vocab_size=args.vocab_size,
            seq_len=args.seq_len,
            symbol_emb_dim=args.symbol_emb_dim,
            token_emb_dim=args.token_emb_dim,
            time_feature_dim=args.time_feature_dim,
            hidden_dim=args.hidden_dim,
            num_layers=args.num_rnn_layers,
            final_emb_dim=args.final_emb_dim,
            dropout=args.dropout
        ).to(device)
    elif args.embedder == 'transformer':
        ts_encoder = TimeSeriesTransformerEmbedder(
            num_symbols=args.fut_num_symbols,
            vocab_size=args.vocab_size,
            seq_len=args.seq_len,
            symbol_emb_dim=args.symbol_emb_dim,
            token_emb_dim=args.token_emb_dim,
            time_feature_dim=args.time_feature_dim,
            d_model=args.hidden_dim,
            nhead=args.num_transformer_heads,
            num_encoder_layers=args.num_transformer_layers,
            dim_feedforward=args.hidden_dim * 4,
            final_emb_dim=args.final_emb_dim,
            dropout=args.dropout
        ).to(device)
    elif args.embedder == 'cnn':
        ts_encoder = TimeSeriesCNNEmbedder(
            num_symbols=args.fut_num_symbols,
            vocab_size=args.vocab_size,
            seq_len=args.seq_len,
            symbol_emb_dim=args.symbol_emb_dim,
            token_emb_dim=args.token_emb_dim,
            time_feature_dim=args.time_feature_dim,
            num_filters=args.cnn_num_filters,
            kernel_sizes=args.cnn_kernel_sizes,
            final_emb_dim=args.final_emb_dim,
            dropout=args.dropout
        ).to(device)
    else:
        raise ValueError(f"Invalid embedder: {args.embedder}")

    # 生成嵌入
    ts_embeddings = []
    targets = []

    with torch.no_grad():
        for code, bar, x_mark, y, _, ctx in train_loader:
            code = code.to(device)
            bar = bar.to(device)
            x_mark = x_mark.to(device)
            y = y.to(device)
            # y_mark = y_mark.to(device)
            print(code[:,0].shape)
            print(bar.shape)
            print(x_mark.shape)
            # print(y.shape)
            # print(code[:,0])
            ts_emb = ts_encoder(code[:,0], bar, x_mark).cpu().numpy()
            print(ts_emb.shape)

            ts_embeddings.append(ts_emb)
            targets.append(ctx)

    ts_embeddings = np.vstack(ts_embeddings)
    targets = np.vstack(targets)
    print(ts_embeddings.shape)
    print(targets.shape)

    # 构建检索器
    retriever = TimeSeriesRetriever(embedding_dim=hidden_dim, n_neighbors=5)
    print(ts_embeddings.shape)
    print(targets.shape)
    retriever.build_index(ts_embeddings, targets)

    # retriever.save("f:/hqdata/retriever")
    return

    # 3. 构建TimeRAG模型
    timerag_model = TimeRAG(
        ts_dim=1,
        hidden_dim=hidden_dim,
        output_dim=1,
        retriever=retriever,
        args=args,
        n_neighbors=5
    )

    # 4. 训练模型
    criterion = nn.MSELoss()
    optimizer = optim.Adam(timerag_model.parameters(), lr=0.001)

    train_losses, val_losses = train_timerag(
        model=timerag_model,
        train_loader=train_loader,
        val_loader=val_loader,
        criterion=criterion,
        optimizer=optimizer,
        device=device,
        epochs=50
    )

    # 5. 评估模型
    # 加载最佳模型
    timerag_model.load_state_dict(torch.load('best_timerag_model.pth'))

    rmse, mape, direction_accuracy, predictions, actuals = evaluate_timerag(
        model=timerag_model,
        test_loader=test_loader,
        # scaler=processor.scaler_price,
        device=device
    )

    print(f'Test RMSE: {rmse:.2f}')
    print(f'Test MAPE: {mape:.2f}%')
    print(f'Direction Accuracy: {direction_accuracy:.2f}%')

    # 6. 回测交易策略
    performance = backtest_strategy(predictions, actuals)

    print(f'Cumulative Return: {performance["cumulative_return"]:.2f}%')
    print(f'Sharpe Ratio: {performance["sharpe_ratio"]:.2f}')
    print(f'Maximum Drawdown: {performance["max_drawdown"]:.2f}%')
    print(f'Win Rate: {performance["win_rate"]:.2f}%')

    # 7. 绘制结果
    plt.figure(figsize=(12, 6))
    plt.plot(actuals, label='Actual')
    plt.plot(predictions, label='Predicted')
    plt.legend()
    plt.title('TimeRAG - Price Prediction')
    plt.xlabel('Time')
    plt.ylabel('Price')
    plt.savefig('timerag_prediction.png')
    plt.close()

    # 绘制训练和验证损失
    plt.figure(figsize=(12, 6))
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.legend()
    plt.title('TimeRAG - Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.savefig('timerag_loss.png')
    plt.close()

SEQ_LEN = 30
VOCAB_SIZE = 40002
FUT_NUM_SYMBOLS = 100 # 假设有 100 个不同的证券代码
STK_NUM_SYMBOLS = 6000 # 假设有 6000 个不同的证券代码
SYMBOL_EMB_DIM = 32
TOKEN_EMB_DIM = 128
TIME_FEATURE_DIM = 8 # time_features 返回 8 个特征
HIDDEN_DIM = 256 # RNN/Transformer 内部维度
NUM_RNN_LAYERS = 2
NUM_TRANSFORMER_HEADS = 8
NUM_TRANSFORMER_LAYERS = 3
CNN_NUM_FILTERS = 128
CNN_KERNEL_SIZES = [3, 5, 7]
DROPOUT = 0.1
FINAL_EMB_DIM = 512 # RAG 检索向量的最终维度

if __name__ == '__main__':
    parser = ArgumentParser()

    # Data module ===========================
    parser.add_argument('--batch_size', default=64, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--seed', default=42, type=int)
    parser.add_argument('--data_path', default='f:/featdata/barenc/db2', type=str, help='path to the data')  
    parser.add_argument('--market', default='fut', choices=['fut', 'stk'], type=str, help='market')
    parser.add_argument('--block_name', default='sf', type=str, help='block name')
    parser.add_argument('--period', default='min5', choices=['day', 'min5'], type=str, help='period')
    parser.add_argument('--start_year', default=2024, type=int, help='start year of the data')  
    parser.add_argument('--end_year', default=2024, type=int, help='end year of the data')
    parser.add_argument('--start_date', default='', type=str)
    parser.add_argument('--end_date', default='', type=str)
    parser.add_argument('--block_size', default=SEQ_LEN, type=int, help='block size')
    parser.add_argument('--step_size', default=1, type=int, help='step size')
    parser.add_argument('--sel_codes', default=MAIN_SEL_FUT_CODES, type=list, choices=[MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES], help='selected codes')
    parser.add_argument('--time_encoding', type=str, default='learned', help='time features encoding, options:[timeF, fixed, learned]')
    parser.add_argument('--freq', type=str, default='t', help='time features encoding, options:[t, b]')
    parser.add_argument('--pos_embed_type', type=str, default='rope', help='BarGpt4: positional embedding type, options:[rope, alibi]')
    parser.add_argument('--is_sf', action='store_true')
    parser.add_argument('--k_folds', default='5', type=int)
    parser.add_argument('--embedder', type=str, default='transformer', help='embedder, options:[rnn, transformer, cnn]')

    parser.add_argument('--seq_len', default=SEQ_LEN, type=int)
    parser.add_argument('--vocab_size', default=VOCAB_SIZE, type=int)
    parser.add_argument('--fut_num_symbols', default=FUT_NUM_SYMBOLS, type=int)
    parser.add_argument('--stk_num_symbols', default=STK_NUM_SYMBOLS, type=int)
    parser.add_argument('--symbol_emb_dim', default=SYMBOL_EMB_DIM, type=int)
    parser.add_argument('--token_emb_dim', default=TOKEN_EMB_DIM, type=int)
    parser.add_argument('--time_feature_dim', default=TIME_FEATURE_DIM, type=int)
    parser.add_argument('--hidden_dim', default=HIDDEN_DIM, type=int)
    parser.add_argument('--num_rnn_layers', default=NUM_RNN_LAYERS, type=int)
    parser.add_argument('--num_transformer_heads', default=NUM_TRANSFORMER_HEADS, type=int)
    parser.add_argument('--num_transformer_layers', default=NUM_TRANSFORMER_LAYERS, type=int)
    parser.add_argument('--cnn_num_filters', default=CNN_NUM_FILTERS, type=int)
    parser.add_argument('--cnn_kernel_sizes', default=CNN_KERNEL_SIZES, type=list)
    parser.add_argument('--dropout', default=DROPOUT, type=float)
    parser.add_argument('--final_emb_dim', default=FINAL_EMB_DIM, type=int)

    args = parser.parse_args()
    main(args)
