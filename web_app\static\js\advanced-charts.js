// 高级图表模块 - 使用lightweight-charts库

// 全局变量
let advancedCharts = {};
let chartContainers = {};

// 初始化高级图表功能
function initAdvancedCharts() {
    // 添加高级图表按钮点击事件
    document.getElementById('showAdvancedChartsBtn').addEventListener('click', function() {
        const ticker = currentTicker;
        if (!ticker) {
            alert('请先搜索股票代码');
            return;
        }

        // 切换高级图表面板的显示/隐藏状态
        const advancedChartsPanel = document.getElementById('advancedChartsPanel');
        if (advancedChartsPanel.style.display === 'none') {
            // 显示高级图表面板
            advancedChartsPanel.style.display = 'block';
            // 加载高级图表
            loadAdvancedCharts(ticker);
        } else {
            // 隐藏高级图表面板
            advancedChartsPanel.style.display = 'none';
        }
    });

    // 关闭按钮点击事件
    document.getElementById('closeAdvancedChartsBtn').addEventListener('click', function() {
        document.getElementById('advancedChartsPanel').style.display = 'none';
    });

    // 时间间隔选择事件
    document.getElementById('timeInterval').addEventListener('change', function() {
        if (currentTicker) {
            updateAdvancedChart(currentTicker, 'candlestick');
        }
    });

    // 时间周期选择事件
    document.getElementById('timePeriod').addEventListener('change', function() {
        if (currentTicker) {
            updateAdvancedChart(currentTicker, 'candlestick');
        }
    });

    // 启用时间选择器（因为现在只有历史走势图）
    const timeControls = document.querySelectorAll('#timeInterval, #timePeriod');
    timeControls.forEach(control => control.disabled = false);
}

// 加载高级图表
async function loadAdvancedCharts(ticker) {
    // 显示加载动画
    const chartContainer = document.getElementById('advancedChartContainer');
    chartContainer.innerHTML = '<div class="text-center py-5"><div class="loading-spinner"></div><p class="mt-3">加载中...</p></div>';

    try {
        // 确保ticker有效
        if (!ticker) {
            throw new Error('无效的股票代码');
        }

        console.log('开始加载高级图表，股票代码:', ticker);

        // 直接加载历史走势图（K线图）
        await updateAdvancedChart(ticker, 'candlestick');

        console.log('高级图表加载完成');
    } catch (error) {
        console.error('加载高级图表失败:', error);
        chartContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">加载高级图表失败: ${error.message}</p></div>`;
    }
}

// 更新高级图表
async function updateAdvancedChart(ticker, chartType) {
    console.log('开始更新高级图表，股票代码:', ticker);

    // 清理之前的图表
    cleanupCharts();

    // 创建图表容器
    const chartContainer = document.getElementById('advancedChartContainer');

    // 确保图表容器可见
    chartContainer.style.display = 'block';

    // 设置图表容器内容
    chartContainer.innerHTML = '<div id="advancedChart" style="width: 100%; height: 500px;"></div>';

    // 等待DOM更新
    await new Promise(resolve => setTimeout(resolve, 0));

    // 创建历史走势图（K线图）
    await createCandlestickChart(ticker);

    console.log('高级图表更新完成');
}

// 清理图表
function cleanupCharts() {
    // 销毁所有图表实例
    for (const key in advancedCharts) {
        if (advancedCharts[key]) {
            advancedCharts[key].remove();
            delete advancedCharts[key];
        }
    }

    // 清空容器引用
    chartContainers = {};
}

// 创建日内走势图
async function createIntradayChart(ticker) {
    try {
        // 获取数据
        const rawData = await fetchIntradayData(ticker);

        // 处理数据格式
        console.log('原始数据格式:', rawData.slice(0, 2));

        // 确保数据格式正确
        const data = Array.isArray(rawData) ? rawData : [];

        if (data.length === 0) {
            throw new Error('无法获取日内数据');
        }

        // 创建图表容器
        const container = document.getElementById('advancedChart');

        // 创建图表
        const chart = LightweightCharts.createChart(container, {
            width: container.clientWidth,
            height: container.clientHeight,
            layout: {
                backgroundColor: '#ffffff',
                textColor: '#333',
            },
            grid: {
                vertLines: {
                    color: 'rgba(197, 203, 206, 0.5)',
                },
                horzLines: {
                    color: 'rgba(197, 203, 206, 0.5)',
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
            },
            timeScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
                timeVisible: true,
                secondsVisible: false,
            },
            // 禁用鼠标滚轮缩放，避免影响页面滚动
            handleScroll: {
                mouseWheel: false,
            },
            // 禁用鼠标滚轮缩放
            handleScale: {
                mouseWheel: false,
            },
        });

        // 添加折线图系列
        const lineSeries = chart.addLineSeries({
            color: '#2962FF',
            lineWidth: 2,
        });

        // 设置数据
        // 确保数据格式正确
        const formattedData = data.map(item => ({
            time: item.time,
            value: item.value
        }));

        console.log('格式化后的数据:', formattedData.slice(0, 2));

        lineSeries.setData(formattedData);

        // 调整时间刻度以适应所有数据
        chart.timeScale().fitContent();

        // 保存图表引用
        advancedCharts['intraday'] = chart;
        chartContainers['intraday'] = container;

        // 添加窗口大小变化监听器
        window.addEventListener('resize', handleResize);

    } catch (error) {
        console.error('创建日内走势图失败:', error);
        document.getElementById('advancedChartContainer').innerHTML = `<div class="text-center py-5"><p class="text-danger">创建日内走势图失败: ${error.message}</p></div>`;
    }
}

// 创建K线图
async function createCandlestickChart(ticker) {
    try {
        console.log('开始创建K线图，股票代码:', ticker);

        // 获取数据
        const rawData = await fetchCandlestickData(ticker);

        // 处理数据格式
        console.log('K线图原始数据格式:', rawData.slice(0, 2));

        // 确保数据格式正确
        const data = Array.isArray(rawData) ? rawData : [];

        if (data.length === 0) {
            throw new Error('无法获取K线图数据');
        }

        // 创建图表容器
        const container = document.getElementById('advancedChart');

        if (!container) {
            throw new Error('找不到图表容器元素');
        }

        console.log('图表容器尺寸:', container.clientWidth, 'x', container.clientHeight);

        // 创建图表
        const chart = LightweightCharts.createChart(container, {
            width: container.clientWidth,
            height: container.clientHeight,
            layout: {
                backgroundColor: '#ffffff',
                textColor: '#333',
            },
            grid: {
                vertLines: {
                    color: 'rgba(197, 203, 206, 0.5)',
                },
                horzLines: {
                    color: 'rgba(197, 203, 206, 0.5)',
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
            },
            timeScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
                timeVisible: true,
                secondsVisible: false,
            },
            // 禁用鼠标滚轮缩放，避免影响页面滚动
            handleScroll: {
                mouseWheel: false,
            },
            // 禁用鼠标滚轮缩放
            handleScale: {
                mouseWheel: false,
            },
        });

        // 添加真正的K线图系列
        const candlestickSeries = chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderUpColor: '#26a69a',
            borderDownColor: '#ef5350',
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
        });

        // 将数据转换为适合K线图的格式
        const formattedData = data.map(item => ({
            time: item.time,
            open: item.open || item.close,
            high: item.high || item.close,
            low: item.low || item.close,
            close: item.close
        }));

        console.log('K线图格式化后的数据:', formattedData.slice(0, 2));

        candlestickSeries.setData(formattedData);

        // 默认添加主要均线
        // 添加MA5均线
        const ma5Series = chart.addLineSeries({
            color: '#2196F3',
            lineWidth: 1,
            title: 'MA5',
            priceLineVisible: false,
        });

        // 添加MA10均线
        const ma10Series = chart.addLineSeries({
            color: '#FF9800',
            lineWidth: 1,
            title: 'MA10',
            priceLineVisible: false,
        });

        // 添加MA20均线
        const ma20Series = chart.addLineSeries({
            color: '#9C27B0',
            lineWidth: 1,
            title: 'MA20',
            priceLineVisible: false,
        });

        // 计算并设置均线数据
        const ma5Data = calculateMA(formattedData, 5);
        const ma10Data = calculateMA(formattedData, 10);
        const ma20Data = calculateMA(formattedData, 20);

        ma5Series.setData(ma5Data);
        ma10Series.setData(ma10Data);
        ma20Series.setData(ma20Data);

        // 默认添加布林带
        // 计算布林带数据
        const bollingerData = calculateBollingerBands(formattedData, 20, 2);

        // 添加布林带中轨线
        const bbMiddleSeries = chart.addLineSeries({
            color: '#2196F3',
            lineWidth: 1,
            title: 'BB Middle',
            priceLineVisible: false,
        });

        // 添加布林带上轨线
        const bbUpperSeries = chart.addLineSeries({
            color: '#FF5252',
            lineWidth: 1,
            title: 'BB Upper',
            priceLineVisible: false,
            lineStyle: 1, // 虚线
        });

        // 添加布林带下轨线
        const bbLowerSeries = chart.addLineSeries({
            color: '#4CAF50',
            lineWidth: 1,
            title: 'BB Lower',
            priceLineVisible: false,
            lineStyle: 1, // 虚线
        });

        // 设置布林带数据
        bbMiddleSeries.setData(bollingerData.middle);
        bbUpperSeries.setData(bollingerData.upper);
        bbLowerSeries.setData(bollingerData.lower);

        // 添加成交量柱状图
        const volumeSeries = chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: 'volume',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });

        // 生成成交量数据
        const volumeData = data.map(item => {
            // 确保有效的开盘价和收盘价
            const open = item.open || item.close;
            const close = item.close;

            return {
                time: item.time,
                value: item.volume || (Math.random() * 1000000 + 100000), // 如果没有成交量数据，使用模拟数据
                color: close >= open ? '#26a69a' : '#ef5350'
            };
        });

        volumeSeries.setData(volumeData);

        // 添加MA60均线
        addMovingAverageSeries(chart, formattedData, 60, '#607D8B');

        // 调整时间刻度以适应所有数据
        chart.timeScale().fitContent();

        // 保存图表引用
        advancedCharts['candlestick'] = chart;
        chartContainers['candlestick'] = container;

        // 添加窗口大小变化监听器
        window.addEventListener('resize', handleResize);

        // 确保图表完全渲染
        setTimeout(() => {
            // 强制重新调整图表大小
            chart.applyOptions({
                width: container.clientWidth,
                height: container.clientHeight
            });

            // 再次调整时间刻度
            chart.timeScale().fitContent();

            console.log('K线图渲染完成');
        }, 100);

    } catch (error) {
        console.error('创建K线图失败:', error);
        document.getElementById('advancedChartContainer').innerHTML = `<div class="text-center py-5"><p class="text-danger">创建K线图失败: ${error.message}</p></div>`;
    }
}

// 添加移动平均线
function addMovingAverageSeries(chart, data, period, color) {
    // 创建移动平均线系列
    const maSeries = chart.addLineSeries({
        color: color,
        lineWidth: 1,
        title: `MA${period}`,
        priceLineVisible: false,
    });

    // 计算移动平均线数据
    const maData = [];

    for (let i = 0; i < data.length; i++) {
        if (i >= period - 1) {
            let sum = 0;
            for (let j = 0; j < period; j++) {
                sum += data[i - j].close;
            }
            maData.push({
                time: data[i].time,
                value: sum / period
            });
        }
    }

    // 设置数据
    maSeries.setData(maData);

    return maSeries;
}

// 计算移动平均线
function calculateMA(data, period) {
    const result = [];

    for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
            // 数据点不足，跳过
            continue;
        }

        let sum = 0;
        for (let j = 0; j < period; j++) {
            sum += data[i - j].close;
        }

        const ma = sum / period;

        result.push({
            time: data[i].time,
            value: ma
        });
    }

    return result;
}

// 计算布林带
function calculateBollingerBands(data, period, stdDev) {
    const middle = [];
    const upper = [];
    const lower = [];

    for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
            // 数据点不足，跳过
            continue;
        }

        // 计算移动平均线
        let sum = 0;
        for (let j = 0; j < period; j++) {
            sum += data[i - j].close;
        }
        const ma = sum / period;

        // 计算标准差
        let squaredSum = 0;
        for (let j = 0; j < period; j++) {
            squaredSum += Math.pow(data[i - j].close - ma, 2);
        }
        const std = Math.sqrt(squaredSum / period);

        // 计算布林带
        middle.push({
            time: data[i].time,
            value: ma
        });

        upper.push({
            time: data[i].time,
            value: ma + (std * stdDev)
        });

        lower.push({
            time: data[i].time,
            value: ma - (std * stdDev)
        });
    }

    return {
        middle: middle,
        upper: upper,
        lower: lower
    };
}

// 添加布林带
function addBollingerBands(chart, data, period, stdDev) {
    // 创建中轨线（MA20）
    const middleSeries = chart.addLineSeries({
        color: '#2196F3',
        lineWidth: 1,
        title: 'BB Middle',
        priceLineVisible: false,
    });

    // 创建上轨线
    const upperSeries = chart.addLineSeries({
        color: '#FF5252',
        lineWidth: 1,
        title: 'BB Upper',
        priceLineVisible: false,
        lineStyle: 1, // 虚线
    });

    // 创建下轨线
    const lowerSeries = chart.addLineSeries({
        color: '#4CAF50',
        lineWidth: 1,
        title: 'BB Lower',
        priceLineVisible: false,
        lineStyle: 1, // 虚线
    });

    // 计算布林带数据
    const bbData = calculateBollingerBands(data, period, stdDev);

    // 设置数据
    middleSeries.setData(bbData.middle);
    upperSeries.setData(bbData.upper);
    lowerSeries.setData(bbData.lower);

    return {
        middle: middleSeries,
        upper: upperSeries,
        lower: lowerSeries
    };
}

// 处理窗口大小变化
function handleResize() {
    for (const key in advancedCharts) {
        if (advancedCharts[key] && chartContainers[key]) {
            const container = chartContainers[key];
            const chart = advancedCharts[key];

            chart.applyOptions({
                width: container.clientWidth,
                height: container.clientHeight
            });
        }
    }
}

// 获取日内走势数据
async function fetchIntradayData(ticker) {
    try {
        // 构建API URL
        const url = `${API_BASE_URL}/stock/${ticker}/intraday`;

        console.log(`正在获取日内数据，URL: ${url}`);

        // 调用API
        const response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const data = await response.json();
        console.log(`成功获取日内数据，数据点数量: ${data.length}`);
        console.log('数据示例:', data.slice(0, 2));

        // 处理数据
        return data;
    } catch (error) {
        console.error(`获取${ticker}日内走势数据失败:`, error);
        console.log('将使用后端提供的模拟数据');

        // 尝试再次调用API，后端应该会返回模拟数据
        try {
            const retryResponse = await fetch(`${API_BASE_URL}/stock/${ticker}/intraday`, {
                headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
            });

            if (retryResponse.ok) {
                const retryData = await retryResponse.json();
                console.log('成功获取模拟数据:', retryData.slice(0, 2));
                return retryData;
            }
        } catch (retryError) {
            console.error('重试获取模拟数据失败:', retryError);
        }

        // 如果重试也失败，使用前端生成的模拟数据
        alert(`获取日内数据失败: ${error.message}\n正在使用模拟数据代替。`);
        return generateMockIntradayData();
    }
}

// 获取K线图数据
async function fetchCandlestickData(ticker) {
    try {
        // 获取时间周期和时间间隔
        const interval = document.getElementById('timeInterval').value;
        const period = document.getElementById('timePeriod').value;

        // 构建API URL - 使用datahub的candlestick接口
        const url = `${API_BASE_URL}/v1/datahub/candlestick?label=${ticker}&interval=${interval}&period=${period}`;

        console.log(`正在获取K线图数据，URL: ${url}`);

        // 调用API
        const response = await fetch(url, {
            headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
        });

        if (!response.ok) {
            throw new Error(`API错误: ${response.status}`);
        }

        const data = await response.json();

        // 检查数据是否有效
        if (!data || !Array.isArray(data) || data.length === 0) {
            throw new Error('获取到的数据无效');
        }

        console.log(`成功获取K线图数据，数据点数量: ${data.length}`);
        console.log('数据示例:', data.slice(0, 2));
        console.log('时间周期:', period, '时间间隔:', interval);

        // 处理数据
        return data;
    } catch (error) {
        console.error(`获取${ticker}K线图数据失败:`, error);
        console.log('尝试使用旧的API端点...');

        // 尝试使用旧的API端点作为备选
        try {
            // 获取时间周期和时间间隔
            const interval = document.getElementById('timeInterval').value;
            const period = document.getElementById('timePeriod').value;

            const fallbackUrl = `${API_BASE_URL}/stock/${ticker}/candlestick?interval=${interval}&period=${period}`;
            console.log(`尝试备选URL: ${fallbackUrl}`);

            const fallbackResponse = await fetch(fallbackUrl, {
                headers: accessToken ? { 'Authorization': `Bearer ${accessToken}` } : {}
            });

            if (fallbackResponse.ok) {
                const fallbackData = await fallbackResponse.json();

                // 检查备选数据是否有效
                if (!fallbackData || !Array.isArray(fallbackData) || fallbackData.length === 0) {
                    throw new Error('备选API返回的数据无效');
                }

                console.log('成功使用旧API获取数据:', fallbackData.slice(0, 2));
                return fallbackData;
            } else {
                throw new Error(`备选API错误: ${fallbackResponse.status}`);
            }
        } catch (fallbackError) {
            console.error('备选API也失败:', fallbackError);
        }

        // 如果两个API都失败，使用前端生成的模拟数据
        console.log('使用模拟数据');
        const mockData = generateMockCandlestickData();
        console.log('生成的模拟数据:', mockData.slice(0, 2));
        return mockData;
    }
}

// 生成模拟日内走势数据
function generateMockIntradayData() {
    const data = [];
    const now = new Date();
    now.setHours(9, 30, 0, 0); // 设置为交易开始时间 9:30

    let basePrice = 100 + Math.random() * 100;

    // 生成一天的交易数据（每5分钟一个点）
    for (let i = 0; i < 78; i++) { // 6.5小时交易时间，每5分钟一个点
        const time = new Date(now);
        time.setMinutes(now.getMinutes() + i * 5);

        // 模拟价格变动
        basePrice += (Math.random() - 0.5) * 2;

        // 添加数据点
        data.push({
            time: Math.floor(time.getTime() / 1000),
            value: basePrice
        });
    }

    return data;
}

// 生成模拟K线图数据
function generateMockCandlestickData() {
    const data = [];
    const now = new Date();
    now.setHours(0, 0, 0, 0);

    let basePrice = 100 + Math.random() * 100;
    let volume = Math.floor(Math.random() * 1000000) + 500000;

    // 生成60天的K线数据，增加数据点数量
    for (let i = 59; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(now.getDate() - i);

        // 模拟当天的开盘价、最高价、最低价和收盘价
        const open = basePrice;
        const close = open + (Math.random() - 0.5) * 5;
        const high = Math.max(open, close) + Math.random() * 2;
        const low = Math.min(open, close) - Math.random() * 2;

        // 模拟成交量
        volume = Math.floor(volume * (0.8 + Math.random() * 0.4));

        // 更新基础价格为下一天的开盘价
        basePrice = close;

        // 添加数据点
        data.push({
            time: Math.floor(date.getTime() / 1000 / 86400),
            open: parseFloat(open.toFixed(2)),
            high: parseFloat(high.toFixed(2)),
            low: parseFloat(low.toFixed(2)),
            close: parseFloat(close.toFixed(2)),
            volume: volume
        });
    }

    console.log('生成了', data.length, '个模拟数据点');

    return data;
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否存在高级图表面板
    if (document.getElementById('advancedChartsPanel')) {
        initAdvancedCharts();
    }
});
