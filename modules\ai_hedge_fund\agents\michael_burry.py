"""
<PERSON> agent for the AI Hedge Fund module.

This agent analyzes stocks using <PERSON>'s contrarian investment principles.
"""

from typing import Dict, List, Any, Literal, Optional
from pydantic import BaseModel, Field
import json
import numpy as np

from ..graph.state import AgentState, show_agent_reasoning
from ..tools.api import get_financial_metrics, get_market_cap, search_line_items, get_price_data
from ..utils.llm import call_llm


class MichaelBurrySignal(BaseModel):
    """Signal generated by the <PERSON> agent."""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")


def michael_burry_agent(state: AgentState) -> Dict[str, Any]:
    """
    Analyzes stocks using <PERSON>'s principles and LLM reasoning.
    
    Args:
        state: Current state of the agent workflow
        
    Returns:
        Updated state with <PERSON>'s analysis
    """
    data = state["data"]
    end_date = data["end_date"]
    start_date = data["start_date"]
    tickers = data["tickers"]
    
    # Collect all analysis for LLM reasoning
    analysis_data = {}
    burry_analysis = {}
    
    for ticker in tickers:
        # Update progress status if available
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Fetching financial metrics")
        
        # Fetch required data
        metrics = get_financial_metrics(ticker, end_date, period="ttm", limit=5)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Gathering financial line items")
        
        financial_line_items = search_line_items(
            ticker,
            [
                "total_assets",
                "total_liabilities",
                "cash_and_cash_equivalents",
                "short_term_investments",
                "long_term_debt",
                "net_income",
                "revenue",
                "inventory",
                "accounts_receivable",
                "accounts_payable",
                "capital_expenditure",
            ],
            end_date,
        )
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Getting price data")
        
        # Get price data for technical analysis
        price_data = get_price_data(ticker, start_date, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Getting market cap")
        
        # Get current market cap
        market_cap = get_market_cap(ticker, end_date)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Analyzing valuation")
        
        # Analyze valuation
        valuation_analysis = analyze_valuation(metrics, financial_line_items, market_cap)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Analyzing financial health")
        
        # Analyze financial health
        financial_health_analysis = analyze_financial_health(financial_line_items)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Analyzing market sentiment")
        
        # Analyze market sentiment (contrarian view)
        market_sentiment_analysis = analyze_market_sentiment(price_data)
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Analyzing hidden value")
        
        # Analyze hidden value
        hidden_value_analysis = analyze_hidden_value(financial_line_items)
        
        # Calculate total score
        total_score = (
            valuation_analysis["score"] + 
            financial_health_analysis["score"] + 
            market_sentiment_analysis["score"] + 
            hidden_value_analysis["score"]
        )
        
        max_possible_score = (
            valuation_analysis["max_score"] + 
            financial_health_analysis["max_score"] + 
            market_sentiment_analysis["max_score"] + 
            hidden_value_analysis["max_score"]
        )
        
        # Generate trading signal based on score
        # Burry is more likely to be bearish or neutral than bullish
        if total_score >= 0.8 * max_possible_score:  # Higher threshold for bullish
            signal = "bullish"
        elif total_score <= 0.4 * max_possible_score:  # Lower threshold for bearish
            signal = "bearish"
        else:
            signal = "neutral"
        
        # Combine all analysis results
        analysis_data[ticker] = {
            "signal": signal,
            "score": total_score,
            "max_score": max_possible_score,
            "valuation_analysis": valuation_analysis,
            "financial_health_analysis": financial_health_analysis,
            "market_sentiment_analysis": market_sentiment_analysis,
            "hidden_value_analysis": hidden_value_analysis,
            "market_cap": market_cap,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Generating Michael Burry analysis")
        
        # Generate detailed analysis using LLM
        burry_output = generate_michael_burry_output(
            ticker=ticker,
            analysis_data=analysis_data,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        burry_analysis[ticker] = {
            "signal": burry_output.signal,
            "confidence": burry_output.confidence,
            "reasoning": burry_output.reasoning,
        }
        
        if "progress" in state["metadata"]:
            state["metadata"]["progress"].update_status("michael_burry_agent", ticker, "Done")
    
    # Show reasoning if requested
    if state["metadata"].get("show_reasoning", False):
        show_agent_reasoning(burry_analysis, "Michael Burry Agent")
    
    # Add the signal to the analyst_signals dictionary
    if "analyst_signals" not in state["data"]:
        state["data"]["analyst_signals"] = {}
    
    state["data"]["analyst_signals"]["michael_burry_agent"] = burry_analysis
    
    return state


def analyze_valuation(
    metrics: List[Any],
    financial_line_items: List[Any],
    market_cap: Optional[float],
) -> Dict[str, Any]:
    """
    Analyze valuation metrics with a contrarian perspective.
    
    Args:
        metrics: List of financial metrics
        financial_line_items: List of financial line items
        market_cap: Current market cap
        
    Returns:
        Dictionary with valuation analysis results
    """
    if not metrics or not financial_line_items or market_cap is None:
        return {"score": 0, "max_score": 5, "details": "Insufficient data for valuation analysis"}
    
    score = 0
    max_score = 5
    reasoning = []
    
    # Check P/E ratio (Burry looks for low P/E)
    if hasattr(metrics[0], "pe_ratio") and metrics[0].pe_ratio is not None:
        pe_ratio = metrics[0].pe_ratio
        
        if pe_ratio < 10:  # Very low P/E
            score += 2
            reasoning.append(f"Extremely low P/E ratio of {pe_ratio:.1f} suggests significant undervaluation")
        elif pe_ratio < 15:  # Moderately low P/E
            score += 1
            reasoning.append(f"Reasonably low P/E ratio of {pe_ratio:.1f}")
        elif pe_ratio > 30:  # High P/E - potential short opportunity
            score -= 1
            reasoning.append(f"Elevated P/E ratio of {pe_ratio:.1f} suggests potential overvaluation")
        else:
            reasoning.append(f"Average P/E ratio of {pe_ratio:.1f}")
    else:
        reasoning.append("P/E ratio data not available")
    
    # Check price-to-book ratio (Burry often looks for stocks trading below book value)
    if hasattr(metrics[0], "price_to_book") and metrics[0].price_to_book is not None:
        pb_ratio = metrics[0].price_to_book
        
        if pb_ratio < 1.0:  # Trading below book value
            score += 2
            reasoning.append(f"Trading below book value with P/B ratio of {pb_ratio:.1f}")
        elif pb_ratio < 2.0:  # Moderately low P/B
            score += 1
            reasoning.append(f"Reasonable P/B ratio of {pb_ratio:.1f}")
        elif pb_ratio > 5.0:  # High P/B - potential short opportunity
            score -= 1
            reasoning.append(f"Elevated P/B ratio of {pb_ratio:.1f} suggests potential overvaluation")
        else:
            reasoning.append(f"Average P/B ratio of {pb_ratio:.1f}")
    else:
        reasoning.append("Price-to-book ratio data not available")
    
    # Check tangible book value vs market cap
    latest_item = financial_line_items[0] if financial_line_items else None
    if latest_item and hasattr(latest_item, "total_assets") and hasattr(latest_item, "total_liabilities"):
        total_assets = latest_item.total_assets
        total_liabilities = latest_item.total_liabilities
        
        if total_assets is not None and total_liabilities is not None:
            book_value = total_assets - total_liabilities
            
            if market_cap < book_value * 0.8:  # Trading well below book value
                score += 1
                reasoning.append(f"Market cap (${market_cap/1e6:.1f}M) significantly below book value (${book_value/1e6:.1f}M)")
            elif market_cap > book_value * 3:  # Trading well above book value
                score -= 1
                reasoning.append(f"Market cap (${market_cap/1e6:.1f}M) significantly above book value (${book_value/1e6:.1f}M)")
            else:
                reasoning.append(f"Market cap (${market_cap/1e6:.1f}M) reasonably aligned with book value (${book_value/1e6:.1f}M)")
    else:
        reasoning.append("Book value data not available")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_financial_health(financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze financial health with a focus on debt and liquidity.
    
    Args:
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with financial health analysis results
    """
    if not financial_line_items:
        return {"score": 0, "max_score": 4, "details": "Insufficient data for financial health analysis"}
    
    score = 0
    max_score = 4
    reasoning = []
    
    latest_item = financial_line_items[0]
    
    # Check debt-to-assets ratio
    if (hasattr(latest_item, "total_assets") and latest_item.total_assets and
        hasattr(latest_item, "long_term_debt") and latest_item.long_term_debt):
        
        debt_to_assets = latest_item.long_term_debt / latest_item.total_assets
        
        if debt_to_assets < 0.2:  # Very low debt
            score += 2
            reasoning.append(f"Very low debt-to-assets ratio of {debt_to_assets:.1%}")
        elif debt_to_assets < 0.4:  # Moderate debt
            score += 1
            reasoning.append(f"Reasonable debt-to-assets ratio of {debt_to_assets:.1%}")
        elif debt_to_assets > 0.6:  # High debt
            score -= 1
            reasoning.append(f"Concerning debt-to-assets ratio of {debt_to_assets:.1%}")
        else:
            reasoning.append(f"Average debt-to-assets ratio of {debt_to_assets:.1%}")
    else:
        reasoning.append("Debt-to-assets ratio data not available")
    
    # Check cash and short-term investments relative to total assets
    if (hasattr(latest_item, "total_assets") and latest_item.total_assets and
        hasattr(latest_item, "cash_and_cash_equivalents") and latest_item.cash_and_cash_equivalents):
        
        cash_ratio = latest_item.cash_and_cash_equivalents / latest_item.total_assets
        
        if cash_ratio > 0.25:  # Strong cash position
            score += 2
            reasoning.append(f"Strong cash position at {cash_ratio:.1%} of total assets")
        elif cash_ratio > 0.15:  # Adequate cash position
            score += 1
            reasoning.append(f"Adequate cash position at {cash_ratio:.1%} of total assets")
        elif cash_ratio < 0.05:  # Low cash position
            score -= 1
            reasoning.append(f"Concerning low cash position at {cash_ratio:.1%} of total assets")
        else:
            reasoning.append(f"Average cash position at {cash_ratio:.1%} of total assets")
    else:
        reasoning.append("Cash ratio data not available")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_market_sentiment(price_data: Any) -> Dict[str, Any]:
    """
    Analyze market sentiment with a contrarian perspective.
    
    Args:
        price_data: Price data for the stock
        
    Returns:
        Dictionary with market sentiment analysis results
    """
    if price_data is None or price_data.empty:
        return {"score": 0, "max_score": 3, "details": "Insufficient price data for sentiment analysis"}
    
    score = 0
    max_score = 3
    reasoning = []
    
    # Calculate recent price movement
    if len(price_data) >= 20:  # At least 20 days of data
        recent_close = price_data["close"].iloc[-1]
        month_ago_close = price_data["close"].iloc[-20]
        price_change = (recent_close - month_ago_close) / month_ago_close
        
        # Contrarian view - negative on strong rallies, positive on sharp declines
        if price_change < -0.2:  # Sharp decline (>20%)
            score += 2
            reasoning.append(f"Sharp price decline of {price_change:.1%} over past month presents contrarian opportunity")
        elif price_change < -0.1:  # Moderate decline (10-20%)
            score += 1
            reasoning.append(f"Moderate price decline of {price_change:.1%} over past month")
        elif price_change > 0.2:  # Sharp rally (>20%)
            score -= 1
            reasoning.append(f"Sharp price rally of {price_change:.1%} over past month suggests potential overvaluation")
        else:
            reasoning.append(f"Moderate price movement of {price_change:.1%} over past month")
    else:
        reasoning.append("Insufficient historical price data")
    
    # Calculate volatility
    if len(price_data) >= 20:
        returns = price_data["close"].pct_change().dropna()
        volatility = returns.std() * np.sqrt(252)  # Annualized volatility
        
        if volatility > 0.5:  # Very high volatility
            score += 1
            reasoning.append(f"Extremely high volatility of {volatility:.1%} suggests market uncertainty and potential opportunity")
        elif volatility < 0.2:  # Low volatility
            score -= 1
            reasoning.append(f"Low volatility of {volatility:.1%} suggests market complacency")
        else:
            reasoning.append(f"Moderate volatility of {volatility:.1%}")
    else:
        reasoning.append("Insufficient data for volatility calculation")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def analyze_hidden_value(financial_line_items: List[Any]) -> Dict[str, Any]:
    """
    Analyze for hidden value in assets or operations.
    
    Args:
        financial_line_items: List of financial line items
        
    Returns:
        Dictionary with hidden value analysis results
    """
    if not financial_line_items:
        return {"score": 0, "max_score": 3, "details": "Insufficient data for hidden value analysis"}
    
    score = 0
    max_score = 3
    reasoning = []
    
    latest_item = financial_line_items[0]
    
    # Check for significant inventory (potential hidden value)
    if hasattr(latest_item, "inventory") and latest_item.inventory and hasattr(latest_item, "total_assets") and latest_item.total_assets:
        inventory_ratio = latest_item.inventory / latest_item.total_assets
        
        if inventory_ratio > 0.2:  # Significant inventory
            score += 1
            reasoning.append(f"Significant inventory at {inventory_ratio:.1%} of total assets may contain hidden value")
        else:
            reasoning.append(f"Average inventory at {inventory_ratio:.1%} of total assets")
    else:
        reasoning.append("Inventory data not available")
    
    # Check for improving operational efficiency
    if len(financial_line_items) >= 2:
        current = financial_line_items[0]
        previous = financial_line_items[1]
        
        if (hasattr(current, "revenue") and current.revenue and
            hasattr(previous, "revenue") and previous.revenue and
            hasattr(current, "net_income") and current.net_income and
            hasattr(previous, "net_income") and previous.net_income):
            
            current_margin = current.net_income / current.revenue
            previous_margin = previous.net_income / previous.revenue
            margin_improvement = current_margin - previous_margin
            
            if margin_improvement > 0.05:  # Significant improvement
                score += 2
                reasoning.append(f"Significant margin improvement of {margin_improvement:.1%} suggests operational improvements")
            elif margin_improvement > 0.02:  # Moderate improvement
                score += 1
                reasoning.append(f"Moderate margin improvement of {margin_improvement:.1%}")
            elif margin_improvement < -0.05:  # Significant deterioration
                score -= 1
                reasoning.append(f"Concerning margin deterioration of {margin_improvement:.1%}")
            else:
                reasoning.append(f"Stable margins with {margin_improvement:.1%} change")
        else:
            reasoning.append("Margin data not available")
    else:
        reasoning.append("Insufficient historical data for margin analysis")
    
    return {
        "score": score,
        "max_score": max_score,
        "details": "; ".join(reasoning),
    }


def generate_michael_burry_output(
    ticker: str,
    analysis_data: Dict[str, Any],
    model_name: str,
    model_provider: str,
) -> MichaelBurrySignal:
    """
    Get investment decision from LLM with Michael Burry's principles.
    
    Args:
        ticker: Stock ticker symbol
        analysis_data: Analysis data for the ticker
        model_name: Name of the LLM model to use
        model_provider: Provider of the LLM model
        
    Returns:
        MichaelBurrySignal object with the investment decision
    """
    # Create a system prompt for the LLM
    system_prompt = """You are a Michael Burry AI agent. Decide on investment signals based on Michael Burry's principles:
    - Contrarian Thinking: Go against market consensus when data supports it
    - Value Focus: Look for stocks trading below intrinsic value
    - Balance Sheet Analysis: Focus on debt levels and hidden assets
    - Margin of Safety: Demand a significant discount to fair value
    - Catalyst Identification: Look for specific events that could unlock value
    - Patience: Willing to wait years for a thesis to play out
    - Risk Management: Extremely thorough in identifying and mitigating risks

    When providing your reasoning, be thorough and specific by:
    1. Explaining the key factors that influenced your decision the most (both positive and negative)
    2. Highlighting how the company aligns with or violates specific Burry principles
    3. Providing quantitative evidence where relevant (e.g., specific valuation metrics, debt levels)
    4. Concluding with a Burry-style assessment of the investment opportunity
    5. Using Michael Burry's voice and conversational style in your explanation

    For example, if bullish: "The market is missing the significance of [specific factor]. With a P/B of only [X] and [Y specific catalyst], there's substantial upside potential despite [acknowledged risk]..."
    For example, if bearish: "The balance sheet reveals concerning [specific issue] that the market is ignoring. With [X metric] at historically elevated levels and [Y trend], I see significant downside risk..."

    Follow these guidelines strictly.
    """
    
    # Create a human prompt for the LLM
    human_prompt = f"""Based on the following data, create the investment signal as Michael Burry would:

    Analysis Data for {ticker}:
    {json.dumps(analysis_data, indent=2)}

    Return the trading signal in the following JSON format exactly:
    {{
      "signal": "bullish" | "bearish" | "neutral",
      "confidence": float between 0 and 100,
      "reasoning": "string"
    }}
    """
    
    # Create a prompt object for the LLM
    prompt = {
        "system": system_prompt,
        "human": human_prompt
    }
    
    # Default fallback signal in case parsing fails
    def create_default_michael_burry_signal():
        ticker_data = analysis_data.get(ticker, {})
        signal = ticker_data.get("signal", "neutral")
        score = ticker_data.get("score", 0)
        max_score = ticker_data.get("max_score", 1)
        confidence = (score / max_score * 100) if max_score > 0 else 50.0
        
        return MichaelBurrySignal(
            signal=signal,
            confidence=confidence,
            reasoning="Analysis based on quantitative metrics only. Unable to generate detailed reasoning."
        )
    
    # Call the LLM
    return call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=MichaelBurrySignal,
        agent_name="michael_burry_agent",
        default_factory=create_default_michael_burry_signal,
    )
