#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户数据模型

该模块定义了用户相关的数据库模型。
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declarative_base
import hashlib
from datetime import datetime

# 使用与其他模型相同的Base
from rest_api.models import Base

class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False)
    full_name = Column(String(100))
    hashed_password = Column(String(128), nullable=False)
    disabled = Column(Boolean, default=False)
    is_admin = Column(Boolean, default=False)
    register_time = Column(DateTime, default=datetime.now)
    last_login = Column(DateTime)
    last_login_ip = Column(String(50))

    # 存储API令牌，使用JSON格式存储
    api_tokens = Column(Text, default="[]")  # 存储为JSON字符串

    # 用户活动日志关联
    activity_logs = relationship("UserActivityLog", primaryjoin="User.id == UserActivityLog.user_id",
                               backref="user", cascade="all, delete-orphan")

    # 用户自选股关联
    favorites = relationship("UserFavorite", back_populates="user", cascade="all, delete-orphan")

    @staticmethod
    def get_password_hash(password: str) -> str:
        """
        获取密码哈希值

        Args:
            password: 明文密码

        Returns:
            str: 哈希后的密码
        """
        return hashlib.sha256(password.encode()).hexdigest()

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """
        验证密码

        Args:
            plain_password: 明文密码
            hashed_password: 哈希后的密码

        Returns:
            bool: 密码是否匹配
        """
        return User.get_password_hash(plain_password) == hashed_password

    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"
