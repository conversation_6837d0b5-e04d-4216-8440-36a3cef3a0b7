"""
智能体投顾助手 - 主程序

这是智能体投顾助手的主程序，提供Web界面和REST API服务。
主要功能包括：
- 股票数据可视化
- 技术指标分析
- 新闻聚合
- 时序检索检索
- 模型预测
"""

from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from pathlib import Path
import os
import logging
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# 导入路由模块
# from web_app import api_routes

# 导入API版本管理模块
from rest_api.core.api_version import API_V1

# 导入API管理器
from rest_api.core.api_manager import APIManager
from config.config_loader import ConfigLoader

# 创建API管理器
config_loader = ConfigLoader(config_file="config/config.json")

# 获取API启用状态
api_settings = config_loader.get("API_SETTINGS", {})
core_apis = api_settings.get("CORE_APIS", [])
enabled_apis = api_settings.get("ENABLED_APIS", {})
enable_all_apis = os.getenv("ENABLE_ALL_APIS", "false").lower() == "true"

# 判断API是否启用
def is_api_enabled(api_name):
    if api_name in core_apis:
        return True
    if enable_all_apis:
        return True
    return enabled_apis.get(api_name, False)

# 根据启用状态导入路由模块

# 始终导入核心API路由模块
from rest_api.routes import portfolio_routes, user_routes, favorite_routes

# 按需导入其他API路由模块
if is_api_enabled("model"):
    from rest_api.routes import model_routes
    logger.info("已导入model_routes模块")
else:
    model_routes = None
    logger.info("跳过禁用的model_routes模块")

if is_api_enabled("tsrag"):
    from rest_api.routes import tsrag_routes
    logger.info("已导入tsrag_routes模块")
else:
    tsrag_routes = None
    logger.info("跳过禁用的tsrag_routes模块")

if is_api_enabled("datahub"):
    from rest_api.routes import datahub_routes
    logger.info("已导入datahub_routes模块")
else:
    datahub_routes = None
    logger.info("跳过禁用的datahub_routes模块")

if is_api_enabled("subscription"):
    from rest_api.routes import subscription_routes
    logger.info("已导入subscription_routes模块")
else:
    subscription_routes = None
    logger.info("跳过禁用的subscription_routes模块")

if is_api_enabled("activity_log"):
    from rest_api.routes import activity_log_routes
    logger.info("已导入activity_log_routes模块")
else:
    activity_log_routes = None
    logger.info("跳过禁用的activity_log_routes模块")

if is_api_enabled("stock"):
    from rest_api.routes import stock_routes
    logger.info("已导入stock_routes模块")
else:
    stock_routes = None
    logger.info("跳过禁用的stock_routes模块")

if is_api_enabled("news"):
    from rest_api.routes import news_routes
    logger.info("已导入news_routes模块")
else:
    news_routes = None
    logger.info("跳过禁用的news_routes模块")

if is_api_enabled("tdx"):
    from rest_api.routes import tdx_routes
    logger.info("已导入tdx_routes模块")
else:
    tdx_routes = None
    logger.info("跳过禁用的tdx_routes模块")

# 创建FastAPI应用
app = FastAPI(
    title="智能体投顾助手",
    description="提供股票数据分析、可视化和时序检索检索功能的平台",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源，实际生产环境应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 获取当前文件所在目录
BASE_DIR = Path(__file__).resolve().parent

# 挂载静态文件
app.mount("/static", StaticFiles(directory=str(BASE_DIR / "web_app" / "static")), name="static")

# 设置模板目录
templates = Jinja2Templates(directory=str(BASE_DIR / "web_app" / "templates"))

# Web页面路由
@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """首页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/profile", response_class=HTMLResponse)
async def profile(request: Request):
    """用户个人资料页面"""
    return templates.TemplateResponse("profile.html", {"request": request})

@app.get("/admin", response_class=HTMLResponse)
async def admin(request: Request):
    """管理员页面"""
    return templates.TemplateResponse("admin.html", {"request": request})

@app.get("/stock/{ticker}", response_class=HTMLResponse)
async def stock_detail(request: Request, ticker: str):
    """股票详情页面"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/model", response_class=HTMLResponse)
async def model(request: Request):
    """模型预测页面"""
    return templates.TemplateResponse("model.html", {"request": request})

@app.get("/tsrag", response_class=HTMLResponse)
async def tsrag(request: Request):
    """时序检索页面"""
    return templates.TemplateResponse("tsrag.html", {"request": request})

@app.get("/portfolio", response_class=HTMLResponse)
async def portfolio(request: Request):
    """投资组合页面"""
    return templates.TemplateResponse("portfolio.html", {"request": request})

# 创建API管理器
api_manager = APIManager(app, config_loader)

# 添加API路由
# Web应用API路由
# app.include_router(api_routes.router, prefix="/api")

# REST API路由
# 注册版本化的路由

# 注册核心API路由（始终启用）
api_manager.register_core_router(portfolio_routes.router, tags=["投资组合"])
api_manager.register_core_router(user_routes.router, tags=["用户管理"])
api_manager.register_core_router(favorite_routes.router, tags=["自选股"])

# 注册可配置的API路由
# 只注册已经导入的模块
if stock_routes:
    api_manager.register_router(stock_routes.router, prefix=f"/api/{API_V1}/stock", tags=["股票数据"], api_name="stock")

if news_routes:
    api_manager.register_router(news_routes.router, prefix=f"/api/{API_V1}/news", tags=["新闻文章"], api_name="news")

if model_routes:
    api_manager.register_router(model_routes.router, prefix=f"/api/{API_V1}/model", tags=["模型信息"], api_name="model")

if tsrag_routes:
    api_manager.register_router(tsrag_routes.router, prefix=f"/api/{API_V1}/tsrag", tags=["时序检索"], api_name="tsrag")

if datahub_routes:
    api_manager.register_router(datahub_routes.router, prefix=f"/api/{API_V1}/datahub", tags=["数据中心"], api_name="datahub")

if subscription_routes:
    api_manager.register_router(subscription_routes.router, tags=["订阅服务"], api_name="subscription")

if activity_log_routes:
    api_manager.register_router(activity_log_routes.router, tags=["活动日志"], api_name="activity_log")

if tdx_routes:
    api_manager.register_router(tdx_routes.router, prefix=f"/api/{API_V1}/tdx", tags=["通达信数据"], api_name="tdx")

# 导入Environment
from rest_api.environment import Environment

@app.on_event("startup")
async def startup_event():
    """应用启动时执行的事件"""
    logger.info("智能体投顾助手启动")

    # 实例化Environment
    try:
        logger.info("开始初始化Environment...")
        env = Environment()
        logger.info("Environment实例化成功")

        # 检查Redis连接
        logger.info("开始检查Redis连接...")
        if env.check_redis_connection():
            logger.info("Redis连接成功，环境初始化完成")
        else:
            logger.warning("Redis连接失败，应用将继续运行，但部分功能可能受到影响")
    except Exception as e:
        logger.error(f"环境初始化失败: {str(e)}")
        logger.warning("应用将继续运行，但部分功能可能受到影响")

    # 设置环境变量
    if not os.environ.get("RETRIEVER_PATH"):
        retriever_path = os.path.join(BASE_DIR, "data", "tsrag", "bar_dataset_retriever")
        os.environ["RETRIEVER_PATH"] = retriever_path
        logger.info(f"设置RETRIEVER_PATH环境变量: {retriever_path}")

@app.get("/api")
async def api_root():
    """API根路径"""
    # 获取所有API的启用状态
    api_status = {
        "stock": stock_routes is not None,
        "news": news_routes is not None,
        "model": model_routes is not None,
        "tsrag": tsrag_routes is not None,
        "datahub": datahub_routes is not None,
        "portfolio": True,  # 核心API，始终启用
        "user": True,  # 核心API，始终启用
        "subscription": subscription_routes is not None,
        "activity_log": activity_log_routes is not None,
        "favorite": True,  # 核心API，始终启用
        "tdx": tdx_routes is not None  # 通达信数据服务
    }

    # 构建API端点信息
    endpoints = {}
    if api_status["stock"]:
        endpoints["股票数据"] = f"/api/{API_V1}/stock"
    if api_status["news"]:
        endpoints["新闻文章"] = f"/api/{API_V1}/news"
    if api_status["model"]:
        endpoints["模型信息"] = f"/api/{API_V1}/model"
    if api_status["tsrag"]:
        endpoints["时序检索"] = f"/api/{API_V1}/tsrag"
    if api_status["datahub"]:
        endpoints["数据中心"] = f"/api/{API_V1}/datahub"
    if api_status["portfolio"]:
        endpoints["投资组合"] = f"/api/{API_V1}/portfolios"
    if api_status["user"]:
        endpoints["用户管理"] = f"/api/{API_V1}/user"
    if api_status["subscription"]:
        endpoints["订阅服务"] = f"/api/{API_V1}/subscription"
    if api_status["activity_log"]:
        endpoints["活动日志"] = f"/api/{API_V1}/logs"
    if api_status["favorite"]:
        endpoints["自选股"] = f"/api/{API_V1}/favorites"
    if api_status["tdx"]:
        endpoints["通达信数据"] = f"/api/{API_V1}/tdx"

    # 获取配置中的API启用状态
    config_status = {}
    for api_name in api_status.keys():
        config_status[api_name] = is_api_enabled(api_name)

    return {
        "message": "欢迎使用智能体投顾助手API",
        "version": "1.0.0",
        "available_versions": [API_V1],
        "current_version": API_V1,
        "api_status": api_status,
        "config_status": config_status,
        "endpoints": endpoints
    }

# 启动应用
if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8080))
    uvicorn.run("app:app", host="0.0.0.0", port=port, reload=True)
